.container {
	width: 100vw;
	height: 100vh;;
	display: flex;
	flex-direction: column;
}
.scroll-view {
	flex: 1;
	height: 0;
	transform: translateX(0);
}
.cart-header {
	padding: 20rpx 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	line-height: 36rpx;
	background: #f7f7f7;

	.num-wrap {
		margin-left: $margin-both;
		color: #666666;
		font-size: 26rpx;
	}

	.cart-action {
		line-height: inherit;
		margin-right: $margin-both;
		color: #666666;
		font-size: 26rpx;
	}

	&.invalid {
		margin-left: $margin-both;
		margin-top: $margin-updown;
		flex: 1;
		line-height: inherit;
	}
}

.cart-wrap {
	margin: 0 24rpx 24rpx;
	border-radius: 16rpx;
	overflow: hidden;

	.fixed-wrap {
		height: 116rpx;
	}

	.cart-goods {
		background: #fff;
		box-sizing: border-box;
		position: relative;
		padding: 30rpx 0 30rpx 30rpx;
		&::after {
			content: '';
			position: absolute;
			left: 20rpx;
			right: 20rpx;
			height: 2rpx;
			bottom: 0;
			background-color: #f2f2f2;
		}
		&:last-of-type::after {
			height: 0;
		}

		.goods-wrap {
			display: flex;
			position: relative;
			padding-left: 64rpx;
			transition: all 0.1s;
			& > .iconfont {
				font-size: 40rpx;
				position: absolute;
				top: 50%;
				left: 0;
				transform: translateY(-50%);
				transition: all 0.2s;
			}
			& > .icon-yuan_checkbox {
				color: $color-disabled;
			}

			&.edit {
				transform: translateX(-70rpx);
				> .iconfont {
					opacity: 0;
				}
			}

			.goods-img {
				width: 180rpx;
				height: 180rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
				}
			}

			.goods-info {
				flex: 1;
				width: 0;
				padding: 0 30rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: $font-size-base;
				}

				.sku-wrap {
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
					width: 100%;

					.sku {
						line-height: 1;
						margin: 10rpx 0 18rpx 0;
						display: inline-flex;
						align-items: center;
						background: #f4f4f4;
						border-radius: 8rpx;
						padding: 0 10rpx 0 20rpx;

						.goods-spec {
							color: #666666;
							font-size: 24rpx;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.iconfont {
							font-size: 28rpx;
							padding-left: 10rpx;
							color: #666666;
						}
					}
				}

				.goods-sub-section {
					display: flex;
					justify-content: space-between;
					width: 100%;
					align-items: center;

					.unit {
						font-size: $font-size-tag;
						margin-right: 4rpx;
					}

					.goods-price {
						display: flex;
						flex-direction: row;
						font-weight: bold;
						color: var(--price-color);
						.bottom-price {
							width: 100%;
							font-size: $font-size-toolbar;
							line-height: 1;
							color: var(--price-color);
							image {
								width: 56rpx;
								height: 22rpx;
								margin-left: 6rpx;
							}
						}
					}

					/deep/ .decrease {
						width: 52rpx;
						height: 52rpx;
						line-height: 48rpx;
						font-size: 40rpx;
						border-radius: 10rpx 0px 0px 10rpx;
					}
					/deep/ input {
						height: 52rpx;
						line-height: 52rpx;
					}
					/deep/ .increase {
						width: 52rpx;
						height: 52rpx;
						line-height: 48rpx;
						font-size: 40rpx;
						border-radius: 0px 10rpx 10rpx 0px;
					}
				}
			}
		}

		.item-del {
			position: absolute;
			width: 0;
			height: 99%;
			color: #fff;
			right: 0;
			top: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s;
			overflow: hidden;
			white-space: nowrap;
			font-size: $font-size-tag;
			&.show {
				width: 90rpx;
			}
		}
		
		&:first-child {
			padding-top: 30rpx;
		}
		&:last-child {
			.goods-info {
				border-bottom: none;
				padding-bottom: 0;
			}
		}

		.discount-wrap {
			line-height: 1.5;
			font-size: 24rpx;
			margin-top: 20rpx;
			display: flex;

			.discount-tag {
				color: $base-color;
				line-height: 1;
				padding: 0 10rpx;
				border-radius: $border-radius;
				border: 1.5px solid $base-color;
				margin-right: 10rpx;
				white-space: nowrap;
				line-height: 32rpx;
				background: var(--main-color-shallow);
			}
			.interval {
				height: 20rpx;
				margin: 0 10rpx;
				border-left: 2rpx solid #ddd;
				white-space: nowrap;
				margin-top: 10rpx;
				&:last-child {
					display: none;
				}
			}
			.scroll-view {
				flex: 1;
				width: 0;
				height: 100%;
				white-space: nowrap;
			}
		}
	}

	.invalid-goods {
		.invalid-mark {
			color: $color-tip;
			padding: 6rpx 16rpx;
			display: inline-block;
			font-size: $font-size-goods-tag;
		}
	}
}

.invalid .cart-header {
	padding-top: 0;
}
.invalid .cart-header + .cart-goods {
	padding-top: 30rpx;
}

.cart-bottom-block {
    height: 100rpx;
    margin-top: 20rpx;
}
.cart-bottom {
	width: 100vw;
	height: 100rpx;
    position: fixed;
    left: 0;
    bottom: var(--tab-bar-height, 0);
	background: #fff;
	overflow: hidden;
	display: flex;
	z-index: 9;
	.all-election {
		height: 100rpx;
		position: relative;
		display: inline-block;

		& > .iconfont {
			font-size: 40rpx;
			position: absolute;
			top: 50%;
			left: 30rpx;
			transform: translateY(-50%);
		}
		& > .icon-yuan_checkbox {
			color: $color-disabled;
		}

		& > text {
			margin-left: 56rpx;
			line-height: 100rpx;
			padding-left: 30rpx;
		}
	}

	.settlement-info {
		flex: 1;
		width: 0;
		padding-right: 10rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		text-align: right;

		.money {
			line-height: 1;
			font-size: 32rpx;

			.value {
				font-weight: bold;
				color: var(--price-color);
			}

			.unit {
				font-size: $font-size-tag;
				margin-right: 4rpx;
				color: var(--price-color);
				font-weight: bold;
			}
		}
		.detail {
			line-height: 1;
			font-size: 22rpx;
			color: #666666;
			margin-top: 10rpx;

			.iconfont {
				font-size: 28rpx;
				margin-left: 6rpx;
				transition: all 0.1s;
				display: inline-block;
				&.open {
					transform: rotate(180deg);
				}
			}
		}
	}

	.action-btn {
		// width: 200rpx;
		height: 100rpx;
		line-height: 100rpx;
		border-radius: 0;
		margin: 0;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		margin-right: 30rpx;
		white-space: nowrap;

		button {
			padding: 0 30rpx;
			height: 70rpx;
			line-height: 70rpx;
			font-size: $font-size-base;
			font-weight: bold;
			border-radius: 50rpx;
			&.delete {
				height: 50rpx;
				line-height: 46rpx;
			}
		}
	}
}
.cart-bottom.active {
	bottom: calc(constant(safe-area-inset-bottom) + 110rpx) !important;
	bottom: calc(env(safe-area-inset-bottom) + 110rpx) !important;
}
.cart-empty {
	text-align: center;
	padding: 140rpx $padding 80rpx $padding;

	image {
		width: 380rpx;
	}

	button {
		min-width: 300rpx;
		margin-top: 100rpx;
		height: 70rpx;
		line-height: 70rpx !important;
		font-size: $font-size-base;
		border-radius: 50rpx;
		&.visit-the {
			font-weight: bold;
		}
	}
}

.popup {
	width: 100vw;
	background: #fff;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;

	.popup-header {
		display: flex;
		position: relative;
		padding: 40rpx;

		.tit {
			flex: 1;
			font-size: $font-size-toolbar;
			line-height: 1;
			text-align: center;
		}
		.iconfont {
			line-height: 1;
			position: absolute;
			right: 30rpx;
			top: 50%;
			transform: translate(0, -50%);
			color: $color-tip;
			font-size: $font-size-toolbar;
		}
	}

	.popup-body {
	}
}

.discount-popup {
	.popup-body {
		min-height: 300rpx;
	}

	.detail-item {
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;

		.money {
			font-weight: bold;
		}

		.reduce {
			color: var(--price-color);
		}
	}

	.total {
		margin-top: 20rpx;

		.title {
			font-size: 36rpx;
			font-weight: bold;
		}
	}
}

.coupon-use-tips {
	padding: 30rpx;
	line-height: 1;
	background: #fff;
	border-bottom: 2rpx solid #eee;
	display: flex;
	justify-content: space-between;
	align-items: center;

	& view {
		line-height: 1;
	}

	.iconfont {
		font-size: 28rpx;
		margin-left: 4rpx;
	}
	.title {
		font-size: 28rpx;
		font-weight: 600;
	}
	.desc {
		font-size: 26rpx;
		margin-left: 24rpx;
	}
}

.coupon-item {
	margin: $margin-updown $margin-both;
	border-radius: 4rpx;
	padding: 0;
	position: relative;
	background-color: #fff2f0;

	&:before,
	&:after {
		position: absolute;
		content: '';
		background-color: #fff;
		top: 50%;
		width: 30rpx;
		height: 30rpx;
		border-radius: 50%;
		z-index: 5;
	}
	&:before {
		left: 0;
		transform: translate(-50%, -50%);
	}
	&:after {
		right: 0;
		transform: translate(50%, -50%);
	}

	.coupon-info {
		height: 190rpx;
		display: flex;
		width: 100%;
		position: relative;

		.info-wrap {
			width: 220rpx;
			height: 190rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 20rpx;
			background-repeat: no-repeat;
			background-size: 100% 100%;
			background: linear-gradient(to left, var(--bg-color), var(--bg-color-shallow));
			position: relative;
			.coupon-line {
				position: absolute;
				right: 0;
				top: 0;
				height: 100%;
			}
			.coupon-money {
				color: #fff;
				text-align: center;
				line-height: 1;
				.unit {
					font-size: 30rpx;
				}
				.money {
					font-size: 60rpx;
				}
			}
			.at-least {
				font-size: $font-size-tag;
				color: #fff;
				text-align: center;
				margin-top: 20rpx;
			}
		}

		.desc-wrap {
			flex: 1;
			max-width: calc(100% - 400rpx);

			view {
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.coupon-name {
				margin-top: 10rpx;
				margin-bottom: 4rpx;
				font-size: $font-size-base;
			}
			.limit {
				font-size: $font-size-activity-tag;
			}
			.time {
				border-top: 2rpx dashed $color-disabled;
				position: absolute;
				bottom: 30rpx;
				color: $color-tip;
				padding-top: 10rpx;
				line-height: 1.5;
				font-size: $font-size-activity-tag;
			}
		}

		button {
			font-size: $font-size-tag;
			position: absolute;
			top: 50%;
			right: 20rpx;
			transform: translate(0, -50%);
			margin: 0;
			height: 50rpx;
			line-height: 50rpx;
			width: 100rpx;
			padding: 0;
			&[disabled] {
				background-color: #dedede !important;
			}
		}
	}

	&.disabled {
		background-color: #f2f2f2;
		.coupon-money {
			color: $color-tip !important;
		}
		.at-least {
			color: $color-tip !important;
		}
	}
}

.uni-popup-discount {
    z-index: 5;
}

.store-wrap {
	display: flex;
	align-items: center;
	background-color: #fff;
	padding: 26rpx 30rpx 0;
	font-weight: bold;
	line-height: 1;

	.name {
		font-size: $font-size-base;
		margin-left: 10rpx;
	}
}