.site-info-box {
	padding: 20rpx 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.6);
	position: fixed;
	width: calc(100% - 40rpx);
	z-index: 999;
	.site-info {
		display: flex;
		align-items: center;
		.img-box {
			width: 70rpx;
			height: 70rpx;
			line-height: 70rpx;
			border-radius: 50%;
			overflow: hidden;
			image {
				width: 100%;
				height: 100%;
			}
		}
		.info-box {
			display: flex;
			flex-direction: column;
			color: #fff;
			justify-content: space-between;
			margin-left: 20rpx;
			text-align: left;
			width: 400rpx;
			text {
				display: -webkit-box;
				font-size: $font-size-tag;
				overflow: hidden;
				opacity: 0.8;
				white-space: normal;
				text-overflow: ellipsis;
				-webkit-line-clamp: 1;
				-webkit-box-orient: vertical;
				line-height: 1.4;
				&:nth-child(1) {
					width: 300rpx;
					font-weight: bold;
					opacity: 1;
				}
			}
		}
	}
	.dite-button {
		background-color: #fff;
		width: 180rpx;
		height: 56rpx;
		line-height: 56rpx;
		text-align: center;
		border-radius: 66rpx;
		color: $base-color;
		font-size: $font-size-tag;
	}
}

.official-accounts-inner /deep/ .uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box {
	overflow: inherit;
}
.official-accounts-wrap {
	display: flex;
	align-items: center;
	flex-direction: column;
	padding: 30rpx;
	.content {
		width: 300rpx;
		height: 300rpx;
	}
	.desc {
		margin-top: 10rpx;
		font-size: $font-size-tag;
		color: $color-tip;
	}
	.close {
		position: absolute;
		left: 50%;
		bottom: -130rpx;
		transform: translateX(-50%);
		z-index: 99;
		color: #fff;
		font-size: 70rpx;
	}
}