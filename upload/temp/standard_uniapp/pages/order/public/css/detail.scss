@mixin flex-row-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

@mixin wrap {
	margin: $margin-updown 24rpx;
	padding: 30rpx;
	border-radius: 16rpx;
	background: #fff;
	position: relative;
}
text,
view {
	font-size: $font-size-tag;
}
.bottom-safe-area {
	padding-bottom: calc(constant(safe-area-inset-bottom) + 10rpx) !important;
	padding-bottom: calc(env(safe-area-inset-bottom) + 10rpx) !important;
}

.align-right {
	text-align: right;
}

.color-text-white {
	color: #fff;
}

.detail-container {
	// height: 100vh;
	.height-box {
		display: block;
		padding-bottom: 100rpx;
	}

	&.safe-area {
		.height-box {
			display: block;
			padding-bottom: 168rpx;
		}
	}
}

.status-wrap {
	background-size: 100% 100%;
	padding: 40rpx;
	height: 180rpx;
	position: relative;

	image {
		width: 104rpx;
		height: 86rpx;
		margin-right: 20rpx;
		margin-top: 20rpx;
	}

	.order-status-left {
		display: flex;
	}

	.order-time {
		position: absolute;
		top: 70rpx;
		right: 30rpx;
		display: flex;
		align-items: center;
		font-size: 10px;
		color: #fff;

		image {
			width: 26rpx;
			height: 26rpx;
			margin-right: 6rpx;
		}
	}

	& > view {
		text-align: center;
		color: #fff;
	}

	.desc {
		margin-left: 20rpx;
	}

	.price {
		font-weight: 600;
	}

	.action-group {
		text-align: center;
		padding-top: 20rpx;

		.action-btn {
			line-height: 1;
			padding: 16rpx 50rpx;
			display: inline-block;
			border-radius: $border-radius;
			background: #fff;
			box-shadow: 0 0 14rpx rgba(158, 158, 158, 0.6);
		}
	}
}

/deep/ #action-date .uni-countdown .uni-countdown__number {
	border: none !important;
	padding: 0 !important;
	margin: 0 !important;
	background: rgba(0, 0, 0, 0) !important;
}

.address-wrap {
	@include wrap;
	min-height: 100rpx;
	margin-top: -69rpx;

	.icon {
		position: absolute;
		top: 20rpx;
		margin-right: 20rpx;

		image {
			width: 60rpx;
			height: 60rpx;
		}

		.iconfont {
			line-height: 50rpx;
			font-size: $font-size-base;
		}
		.icon-mendian {
			font-size: $font-size-toolbar;
		}
	}

	.address-info {
		padding-left: 40rpx;

		.info {
			display: flex;
			line-height: 1;
			color: #333;
		}

		.detail {
			line-height: 1.3;
			color: #333;
			margin-top: 20rpx;
		}
	}

	.store-info {
		padding-left: 100rpx;
	}

	.cell-more {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 10rpx;

		.iconfont {
			color: #999;
		}
	}
}

.pickup-info {
	@include wrap;

	.pickup-point-info {
		.name {
			display: flex;
			height: 50rpx;
			align-items: flex-end;
			margin-bottom: 10px;

			text {
				line-height: 1;
				&.mark {
					font-size: $font-size-activity-tag;
					padding: 1px 10rpx;
					border: 0.5px solid #ffffff;
					border-radius: 4rpx;
					margin-left: 10rpx;
				}
			}
		}

		.address,
		.time,
		.contact {
			font-size: $font-size-tag;
			line-height: 1;
			margin-top: 16rpx;

			.iconfont {
				color: #999;
				font-size: $font-size-tag;
				line-height: 1;
				margin-right: 10rpx;
			}
		}
	}
	.hr {
		border-top: 1px dashed #e5e5e5;
		margin: 20rpx 0;
	}

	.pickup-code-info {
		.info {
			text-align: center;
		}

		.code {
			display: flex;
			flex-direction: column;
			align-items: center;
			image.barcode {
				width: 360rpx;
				height: auto;
				will-change: transform;
				margin-top: 20rpx;
			}
			image.qrcode {
				width: 240rpx;
				height: auto;
				will-change: transform;
				margin-top: 50rpx;
			}
		}

		.copy {
			font-size: $font-size-tag;
			display: inline-block;
			color: #666;
			background: #f7f7f7;
			line-height: 1;
			padding: 6rpx 14rpx;
			margin-left: 10rpx;
			border-radius: $border-radius;
			border: 0.5px solid #666;
		}
	}
}

.virtual-mobile-wrap {
	@include wrap;
	margin-top: -69rpx;
	display: flex;

	view:nth-child(2) {
		flex: 1;
		text-align: right;
	}
}

.verify-code-wrap {
	@include wrap;

	.wrap {
		text-align: center;
		line-height: 2;

		.copy {
			font-size: $font-size-tag;
			display: inline-block;
			color: #666;
			background: #f7f7f7;
			line-height: 1;
			padding: 6rpx 14rpx;
			margin-left: 10rpx;
			border-radius: $border-radius;
			border: 0.5px solid #666;
		}

		.virtual-code {
			font-weight: bold;
		}
	}

	.hr {
		border-top: 1px dashed #e5e5e5;
		margin: 20rpx 0;
	}

	.code {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;

		image.barcode {
			width: 400rpx;
			margin-top: 10rpx;
		}
		image.qrcode {
			width: 300rpx;
			margin-top: 50rpx;
		}

		text {
			margin-top: 20rpx;
		}
	}
}

.verify-info-wrap {
	@include wrap;

	.head {
		font-size: $font-size-base;
		border-bottom: 1px dashed #f7f7f7;
		line-height: 1;
		padding: 10rpx 0 30rpx 0;
	}

	.order-cell {
		.tit {
			font-size: $font-size-base;
		}

		.box text {
			font-size: $font-size-base;
			font-weight: bold;
		}
	}

	.record-empty {
		text-align: center;
		padding-top: 30rpx;
	}

	.record-item {
		margin-bottom: 40rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}
}

.carmichael {
	.order-cell {
		.tit {
			font-size: $font-size-base;
		}

		.box text {
			font-size: $font-size-base;
			font-weight: normal;
		}

		.copy {
			font-size: $font-size-activity-tag;
			display: inline-block;
			background: #f7f7f7;
			line-height: 1;
			padding: 6rpx 10rpx;
			margin-left: 10rpx;
			border-radius: $border-radius;
			border: 2rpx solid #d2d2d2;
		}
	}
}

.site-wrap {
	@include wrap;
	padding: 10rpx 30rpx;

	.site-header {
		display: flex;
		align-items: center;

		.icon-dianpu {
			display: inline-block;
			line-height: 1;
			margin-right: 12rpx;
			font-size: $font-size-base;
		}
	}

	.site-body {
		.goods-item {
			padding-top: 20rpx;
			border-bottom: 2rpx solid #f7f7f7;
			&:last-child {
				border-bottom: 0;
			}
		}

		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;

			&:last-of-type {
				margin-bottom: 0;
			}

			.goods-img {
				width: 160rpx;
				height: 160rpx;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: $border-radius;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				max-width: calc(100% - 180rpx);

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: $font-size-base;
					font-weight: bold;
				}

				.goods-sub-section {
					width: 100%;
					line-height: 1.3;
					display: flex;
					margin-top: 20rpx;
					align-items: center;
					.goods-price {
						font-weight: 700;
						font-size: $font-size-activity-tag;
						color: var(--price-color);
					}

					.unit {
						font-weight: 700;
						font-size: $font-size-tag;
						margin-right: 2rpx;
					}

					view {
						flex: 1;
						line-height: 1.3;
						&:last-of-type {
							text-align: right;
							font-weight: bold;

							.iconfont {
								line-height: 1;
								font-size: $font-size-tag;
							}
						}
					}
				}

				.goods-card {
					text-align: right;
				}
			}
		}

		.goods-form {
			.tit {
				font-size: $font-size-base;
				width: 190rpx;
			}

			.box {
				padding-right: 0;

				text {
					font-size: $font-size-base;
					word-wrap: break-word;
					word-break: break-all;
				}
				.copy {
					font-size: $font-size-activity-tag !important;
					display: inline-block;
					background: #f7f7f7;
					line-height: 1;
					padding: 6rpx 10rpx;
					margin-left: 20rpx;
					border-radius: $border-radius;
					border: 2rpx solid #d2d2d2;
					float: right;
				}
			}
		}

		.goods-action {
			text-align: right;
			margin: 20rpx 0;

			navigator {
				display: inline-block;
			}

			.order-box-btn {
				height: 48rpx !important;
				line-height: 48rpx !important;
				font-size: $font-size-tag !important;
				display: inline-block;
				background: #fff;
				border: 2rpx solid #999;
				margin-left: 10rpx;
				box-sizing: content-box;
			}
		}
	}
}

.order-cell {
	display: flex;
	margin: 20rpx 0;
	align-items: center;
	background: #fff;
	line-height: 40rpx;
	&.remark{
		align-items: flex-start !important;
	}
	.tit {
		text-align: left;
	}

	.box {
		flex: 1;
		padding: 0 20rpx;
		line-height: inherit;

		.textarea {
			height: 40rpx;
		}
	}

	.img-box {
		display: flex;
		flex-wrap: wrap;

		.img {
			width: 100rpx;
			height: 100rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			margin-right: 30rpx;
			margin-bottom: 30rpx;
			position: relative;
			border-radius: $border-radius;
			line-height: 1;
			overflow: hidden;

			image {
				width: 100%;
			}
		}
	}

	.iconfont {
		color: #bbb;
		font-size: $font-size-base;
	}

	.order-pay {
		padding: 0;

		text {
			display: inline-block;
			margin-left: 6rpx;
		}
	}
}

.order-summary {
	@include wrap;

	.order-cell {
		&:first-child {
			margin-top: 0;
		}

		.tit {
			font-size: $font-size-base;
			width: 190rpx;
		}

		.box {
			display: flex;
			align-items: center;

			text {
				font-size: $font-size-base;
				word-wrap: break-word;
				word-break: break-all;
			}
		}

		.copy {
			white-space: nowrap;
			font-size: $font-size-activity-tag !important;
			display: inline-block;
			background: #f7f7f7;
			line-height: 1;
			padding: 6rpx 10rpx;
			margin-left: 10rpx;
			border-radius: $border-radius;
			border: 2rpx solid #d2d2d2;
		}
	}

	.hr {
		// width: calc(100% - 190rpx);
		width: 100%;
		height: 2rpx;
		background: #f7f7f7;
		margin-bottom: 20rpx;
	}
}

.order-money {
	@include wrap;

	.order-cell {
		.tit {
			font-size: $font-size-base;
		}

		.box {
			font-weight: 600;
			padding: 0;
            text-align: right;

			text {
				font-size: $font-size-base;
				font-weight: bold;
			}
			> text.color-text {
				color: var(--price-color);
			}

			.operator {
				font-size: $font-size-tag;
				margin-right: 6rpx;
			}
			&.align-right {
				.color-base-text {
					text {
						color: var(--price-color);
					}
				}
			}
		}
	}
}

.kefu {
	@include wrap;
	margin: 30rpx 0 10rpx;
	border-top: 2rpx solid #f7f7f7;
	padding-bottom: 0;
	padding-top: 30rpx;
	& > view {
		@include flex-row-center;

		.iconfont {
			font-weight: bold;
			margin-right: 10rpx;
			font-size: $font-size-base;
			line-height: 1;
		}
	}

	button {
		width: 100%;
		// position: absolute;
		border: none;
		z-index: 1;
		padding: 0;
		margin: 0;
		background: none;
		height: 50rpx;
		line-height: 50rpx;
		display: flex;
		justify-content: center;
		&::after {
			border: none !important;
		}
		.iconfont {
			margin-right: 10rpx;
		}
	}
}

.fixed-bottom-box {
	height: 80rpx;
}
.order-action {
	text-align: right;

	.order-box-btn {
		margin-right: $margin-both;
		margin-left: 0;
		font-size: $font-size-tag;
		height: 60rpx;
		line-height: 60rpx;
		box-sizing: content-box;
		min-width: 60rpx;
		text-align: center;

		&.color-base-bg {
			color: var(--btn-text-color);
		}

		&:last-child {
			margin-right: 0;
		}
	}
}
.status-name {
	view,
	text {
		font-size: $font-size-toolbar;
		color: #fff;
		line-height: 1;
		margin-top: 40rpx;
		text-align: left;
	}

	.desc {
		font-size: $font-size-tag;
		margin: 10rpx 0 0 0;
	}
}

.head-nav {
	width: 100%;
	height: var(--status-bar-height);
}

.head-nav.active {
	padding-top: 40rpx;
}

.head-return {
	height: 90rpx;
	line-height: 90rpx;
	color: #fff;
	font-weight: 600;
	font-size: $font-size-toolbar;
	position: relative;
	text-align: center;
	text {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		left: 20rpx;
		display: inline-block;
		margin-right: 10rpx;
		font-size: $font-size-toolbar;
	}
}

.store-detail view {
	font-size: $font-size-activity-tag;
}

.store-wrap {
	@include wrap;
	margin-top: -76rpx;

	.store-info {
		display: flex;
		align-items: center;
		padding-left: 50rpx;
		position: relative;

		.icon {
			left: 0;
			position: absolute;
			top: 4rpx;

			.iconfont {
				line-height: 50rpx;
				font-size: $font-size-base;
			}
			.icon-mendian {
				font-size: $font-size-toolbar;
			}
		}

		.store-name {
			display: flex;

			.name {
				flex: 1;
			}
		}

		.store-info-detail {
			flex: 1;
			.store-detail view {
				font-size: $font-size-goods-tag + 2rpx;
			}
			& > view:first-of-type {
				font-size: $font-size-tag + 2rpx;
			}
		}
		.cell-more {
			margin-left: 50rpx;
		}
	}
}
.pick-block {
	&.first-pick-block {
		border-top: 2rpx solid #f1f1f1;
	}
	display: flex;
	align-items: center;
	margin-top: 20rpx;
	padding-top: 20rpx;
	input,
	.last-child {
		flex: 1;
		text-align: right;
		font-size: $font-size-tag;
	}
}
.sku {
	display: flex;
	line-height: 1;
	margin-top: 10rpx;
	margin-bottom: 10rpx;
}
.goods-spec {
	color: #838383;
	font-size: $font-size-goods-tag;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	flex: 1;
}
.goods-num{
	font-size: 22rpx;
	margin-left: 20rpx;
}
.delivery-status{
	line-height: 1.3;
}
.fixed-bottom {
	width: 100%;
	position: fixed;
	left: 0;
	bottom: 0;
	padding: 10rpx 30rpx;
	box-sizing: border-box;
	background: #ffffff;
	z-index: 5;
}

.order-cell.order-form {
	.box {
		display: block;
		padding-right: 0;
		.copy {
			margin-left: 20rpx;
			float: right;
		}
	}
}
