.collectPopupWindow {
	position: relative;
	height: 113rpx;
	width: 510rpx;
	margin-left: calc(100% - 530rpx);

	image {
		width: 100%;
		height: 100%;
	}

	text {
		color: #ff4544 !important;
		font-size: 24rpx !important;
		position: absolute;
		top: 48rpx;
		right: 25rpx;
	}
}
.z<PERSON><PERSON><PERSON> {
	width: 100vw;
	height: 100vh;
	background-color: transparent;
}
image {
	max-width: 100% !important;
	max-height: 100% !important;
}
.diy-wrap {
	/* #ifdef H5 */
	height: calc(100vh - 88rpx);
	/* #endif */
	/* #ifdef MP-WEIXIN */
	height: 100vh;
	/* #endif */
}

.page-img {
	background-size: contain !important;
	background-repeat: no-repeat !important;
}

.page-header {
	background-size: 100% !important;
	background-repeat: no-repeat !important;
	background-position: top center;
	background-attachment: fixed;
}

.bg-index {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	background-size: 100% !important;
	background-repeat: no-repeat !important;
}

.wap-floating {
	text {
		display: block;
		font-size: 60rpx;
		color: #ffffff;
		text-align: center;
	}
}

.wap-floating-collect .uni-popup__mask {
	background: transparent;
}

::-webkit-scrollbar {
	width: 0;
	height: 0;
	color: transparent;
}

.popup-box {
	width: 450rpx;
	background: #ffffff;
	border-radius: $border-radius;
	overflow: hidden;

	.close_title {
		width: 100%;
		text-align: center;
		height: 70rpx;
		line-height: 70rpx;
		font-size: $font-size-base;
	}

	.close_content {
		width: 100%;
		max-height: 500rpx;
		padding: $padding;
		box-sizing: border-box;
	}

	.close_content_box {
		width: 100%;
		max-height: 460rpx;
		line-height: 1.3;
	}
}

.padding-bottom {
	padding-bottom: 40rpx !important;
}

.choose-store {
    /deep/ .uni-popup__wrapper{
        background: none!important;
    }
}

.choose-store-popup {
	padding: 30rpx;
	background-color: #fff;
	.head-wrap {
		font-weight: bold;
		font-size: $font-size-toolbar;
		text-align: center;
		margin-bottom: 20rpx;
		color: #202021;
	}
	.position-wrap {
		display: flex;
		color: #202021;
		align-items: center;
		margin-bottom: 20rpx;
		.icon-dizhi {
			font-weight: bold;
			font-size: $font-size-tag;
			margin-right: 10rpx;
		}
		.address {
			font-weight: bold;
			font-size: $font-size-tag;
			margin-right: 10rpx;
			flex: 1;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			-o-text-overflow: ellipsis;
		}
		.reposition {
			display: flex;
			align-items: center;
			.iconfont {
				font-size: $font-size-base;
				margin-right: 6rpx;
			}
			text {
				font-size: $font-size-tag;
				color: #fd463e;
			}
		}
	}
	.store-wrap {
		border: 1px solid $base-color;
		border-radius: 16rpx;
		padding: 20rpx 30rpx;
		margin-bottom: 30rpx;
		.tag {
			background-color: #fee9ea;
			color: #fd463e;
			font-size: $font-size-activity-tag;
			display: inline-block;
			border-radius: 6rpx;
			padding: 4rpx 12rpx;
			// #ifdef H5
			transform: scale(0.8);
			margin-left: -10rpx;
			// #endif
		}
		.store-name {
			margin: 10rpx 0;
			font-weight: bold;
			color: #202021;
			font-size: $font-size-toolbar;
		}
		.store-close-desc{
			color: red;
			font-size: $font-size-tag;
			margin-bottom: 10rpx;
		}
		.address {
			color: #5f6067;
			font-size: $font-size-tag;
			margin-bottom: 10rpx;
		}
		.distance {
			display: flex;
			align-items: center;
			color: #5f6067;
			font-size: $font-size-tag;
			.iconfont {
				font-size: $font-size-base;
				margin-right: 10rpx;
			}
		}
	}

	button {
		border-radius: 40rpx;
	}

	.other-store {
		display: flex;
		align-items: center;
		color: #5e6066;
		font-weight: bold;
		justify-content: center;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		.iconfont {
			margin-left: 10rpx;
			font-size: $font-size-tag;
		}
	}
}
.page-bottom {
	margin-top: 20rpx;
}
.chain-stores{
	.chain-store-popup{
		background-color: #fff;
		border-top-left-radius: 24rpx;
		border-top-right-radius: 24rpx;
		overflow: hidden;
		.title{
			font-size: 36rpx;
			line-height: 104rpx;
			text-align: center;
			color: #000;
			font-weight: bold;
		}
		.body{
			padding: 20rpx 30rpx;
			background-color: #F4F4F4;
			padding-bottom: calc(20rpx  + constant(safe-area-inset-bottom)) !important;
			padding-bottom: calc(20rpx  + env(safe-area-inset-bottom)) !important;
			.center{
				background-color: #fff;
				box-shadow: 4rpx 4rpx 12rpx 4rpx rgba(0,0,0,0.02);
				border-radius: 24rpx;
				padding-top: 60rpx;
				padding-bottom: 23rpx;
				.image{
					display: flex;
					justify-content: center;
				}
				.text-top{
					margin-top: 44rpx;
					font-size: 30rpx;
					font-weight: bold;
					color: #000;
					line-height: 42rpx;
					text-align: center;
				}
				.text-bottom{
					margin-top: 20rpx;
					padding: 0 57rpx;
					font-size: 24rpx;
					line-height: 34rpx;
					color: #999;
					text-align: center;
				}
				.footer{
					display: flex;
					margin-top: 20rpx;
					padding: 0 24rpx;
					button{
						margin: 0 !important;
						box-sizing: border-box;
						height: 84rpx;
						line-height: 84rpx;
						border-radius: 62rpx;
						font-size: 30rpx;
						flex:1;
					}
					button.btn-right{
						margin-left: 20rpx !important;
					}
				}
			}
		}
	}
}

