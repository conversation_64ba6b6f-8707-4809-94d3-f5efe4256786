export default {
	'default': {
		//红色
		name: 'default',
		main_color: '#F4391c',
		aux_color: '#F7B500',
		bg_color: '#FF4646',//主题背景
		bg_color_shallow: '#FF4646',//主题背景渐变浅色
		promotion_color: '#FF4646',//活动背景
		promotion_aux_color: '#F7B500',//活动背景辅色
		main_color_shallow: '#FFF4F4',//淡背景
		price_color: 'rgb(252,82,39)',//价格颜色
		btn_text_color: '#FFFFFF',//按钮文字颜色
		goods_detail: {
			goods_price: 'rgb(252,82,39,1)',//价格
			promotion_tag: '#FF4646',
			goods_card_bg: '#201A18',//会员卡背景
			goods_card_bg_shallow: '#7C7878',//会员卡背景浅色
			goods_card_color: '#FFD792',
			goods_coupon: '#FC5227',
			goods_cart_num_corner: '#FC5227',//购物车数量角标
			goods_btn_color: '#FF4646',//按钮颜色
			goods_btn_color_shallow: '#F7B500',//副按钮颜色
		},
		pintuan: {
			pintuan_label_bg: '#F7B500',
			pintuan_label_color: '#FFFFFF',
			pintuan_color: '#FA6400',
			pintuan_promotion_color: '#FA3A1D',//活动背景
			pintuan_promotion_aux_color: '#FD9A01',//活动背景辅色
		},
		super_member: {
			super_member_start_bg: '#7c7878',
			super_member_end_bg: '#201a18',
			super_member_start_text_color: '#FFDBA6',
			super_member_end_text_color: '#FFEBCA',
		},
		bargain: {
			bargain_promotion_color: '#F0353E',//活动背景
			bargain_promotion_aux_color: '#FD9A01',//活动辅色
		},
		seckill: {
			seckill_promotion_color: '#F83530',//活动背景
			seckill_promotion_aux_color: '#FD9A01',//活动辅色
		},
		giftcard: {
			giftcard_promotion_color: '#FF3369',//活动背景
			giftcard_promotion_aux_color: '#F7B500',//活动辅色
		},
		groupby: {
			groupby_promotion_color: '#E64136',//活动背景
			groupby_promotion_aux_color: '#F7B500',//活动辅色
		},
	},
	'green': {
		name: 'green',
		main_color: '#19C650',
		aux_color: '#FA6400',
		bg_color: '#19C650',
		bg_color_shallow: '#19C650',
		promotion_color: '#19C650',
		promotion_aux_color: '#FA6400',
		main_color_shallow: '#F0FFF5',//淡背景
		price_color: 'rgba(252,82,39,1)',//价格颜色
		btn_text_color: '#FFFFFF',//按钮文字颜色
		goods_detail: {
			goods_price: 'rgba(252,82,39,1)',//价格
			promotion_tag: '#19C650',
			goods_card_bg: '#201A18',//会员卡背景
			goods_card_bg_shallow: '#7C7878',//会员卡背景浅色
			goods_card_color: '#FFD792',
			goods_coupon: '#FC5227',
			goods_cart_num_corner: '#FC5227',//购物车数量角标
			goods_btn_color: '#19C650',//按钮颜色
			goods_btn_color_shallow: '#FA6400',//副按钮颜色
		},
		pintuan: {
			pintuan_label_bg: '#F7B500',
			pintuan_label_color: '#FFFFFF',
			pintuan_color: '#FA6400',
			pintuan_promotion_color: '#FA3A1D',//活动背景
			pintuan_promotion_aux_color: '#FD9A01',//活动背景辅色
		},
		super_member: {
			super_member_start_bg: '#7c7878',
			super_member_end_bg: '#201a18',
			super_member_start_text_color: '#FFDBA6',
			super_member_end_text_color: '#FFEBCA',
		},
		bargain: {
			bargain_promotion_color: '#F0353E',//活动背景
			bargain_promotion_aux_color: '#FD9A01',//活动辅色
		},
		seckill: {
			seckill_promotion_color: '#F83530',//活动背景
			seckill_promotion_aux_color: '#FD9A01',//活动辅色
		},
		giftcard: {
			giftcard_promotion_color: '#FF3369',//活动背景
			giftcard_promotion_aux_color: '#F7B500',//活动辅色
		},
		groupby: {
			groupby_promotion_color: '#E64136',//活动背景
			groupby_promotion_aux_color: '#F7B500',//活动辅色
		},
	},
	'blue': {
		name: 'blue',
		main_color: '#36ABFF',
		aux_color: '#FA6400',
		bg_color: '#36ABFF',
		bg_color_shallow: '#36ABFF',
		promotion_color: '#36ABFF ',
		promotion_aux_color: '#FA6400',
		main_color_shallow: '#E2F3FF',
		price_color: 'rgba(252,82,39,1)',//价格颜色
		btn_text_color: '#FFFFFF',//按钮文字颜色
		goods_detail: {
			goods_price: 'rgba(252,82,39,1)',//价格
			promotion_tag: '#36ABFF',
			goods_card_bg: '#201A18',//会员卡背景
			goods_card_bg_shallow: '#7C7878',//会员卡背景浅色
			goods_card_color: '#FFD792',
			goods_coupon: '#FC5227',
			goods_cart_num_corner: '#FC5227',//购物车数量角标
			goods_btn_color: '#36ABFF',//按钮颜色
			goods_btn_color_shallow: '#FA6400',//副按钮颜色
		},
		pintuan: {
			pintuan_label_bg: '#F7B500',
			pintuan_label_color: '#FFFFFF',
			pintuan_color: '#FA6400',
			pintuan_promotion_color: '#FA3A1D',//活动背景
			pintuan_promotion_aux_color: '#FD9A01',//活动背景辅色
		},
		super_member: {
			super_member_start_bg: '#7c7878',
			super_member_end_bg: '#201a18',
			super_member_start_text_color: '#FFDBA6',
			super_member_end_text_color: '#FFEBCA',
		},
		bargain: {
			bargain_promotion_color: '#F0353E',//活动背景
			bargain_promotion_aux_color: '#FD9A01',//活动辅色
		},
		seckill: {
			seckill_promotion_color: '#F83530',//活动背景
			seckill_promotion_aux_color: '#FD9A01',//活动辅色
		},
		giftcard: {
			giftcard_promotion_color: '#FF3369',//活动背景
			giftcard_promotion_aux_color: '#F7B500',//活动辅色
		},
		groupby: {
			groupby_promotion_color: '#E64136',//活动背景
			groupby_promotion_aux_color: '#F7B500',//活动辅色
		},
	},
	'pink': {
		name: 'pink',
		main_color: '#FF407E',
		aux_color: '#F7B500',
		bg_color: '#FF407E',//主题背景
		bg_color_shallow: '#FF407E',//主题背景渐变浅色
		promotion_color: '#FF407E',//活动背景
		promotion_aux_color: '#F7B500',//活动背景辅色
		main_color_shallow: '#FFF5F8',//淡背景
		price_color: 'rgba(252,82,39,1)',//价格颜色
		btn_text_color: '#FFFFFF',//按钮文字颜色
		goods_detail: {
			goods_price: 'rgba(252,82,39,1)',//价格
			promotion_tag: '#FF407E',
			goods_card_bg: '#201A18',//会员卡背景
			goods_card_bg_shallow: '#7C7878',//会员卡背景浅色
			goods_card_color: '#FFD792',
			goods_coupon: '#FC5227',
			goods_cart_num_corner: '#FC5227',//购物车数量角标
			goods_btn_color: '#FF407E',//按钮颜色
			goods_btn_color_shallow: '#F7B500',//副按钮颜色
		},
		pintuan: {
			pintuan_label_bg: '#F7B500',
			pintuan_label_color: '#FFFFFF',
			pintuan_color: '#FA6400',
			pintuan_promotion_color: '#FA3A1D',//活动背景
			pintuan_promotion_aux_color: '#FD9A01',//活动背景辅色
		},
		super_member: {
			super_member_start_bg: '#7c7878',
			super_member_end_bg: '#201a18',
			super_member_start_text_color: '#FFDBA6',
			super_member_end_text_color: '#FFEBCA',
		},
		bargain: {
			bargain_promotion_color: '#F0353E',//活动背景
			bargain_promotion_aux_color: '#FD9A01',//活动辅色
		},
		seckill: {
			seckill_promotion_color: '#F83530',//活动背景
			seckill_promotion_aux_color: '#FD9A01',//活动辅色
		},
		giftcard: {
			giftcard_promotion_color: '#FF3369',//活动背景
			giftcard_promotion_aux_color: '#F7B500',//活动辅色
		},
		groupby: {
			groupby_promotion_color: '#E64136',//活动背景
			groupby_promotion_aux_color: '#F7B500',//活动辅色
		},
	},
	'gold': {
		name: 'gold',
		main_color: '#CFAF70',
		aux_color: '#444444',
		bg_color: '#CFAF70',//主题背景
		bg_color_shallow: '#CFAF70',//主题背景渐变浅色
		promotion_color: '#CFAF70',//活动背景
		promotion_aux_color: '#444444',//活动背景辅色
		main_color_shallow: '#FFFAF1',//淡背景
		price_color: 'rgba(252,82,39,1)',//价格颜色
		btn_text_color: '#FFFFFF',//按钮文字颜色
		goods_detail: {
			goods_price: 'rgba(252,82,39,1)',//价格
			promotion_tag: '#CFAF70',
			goods_card_bg: '#201A18',//会员卡背景
			goods_card_bg_shallow: '#7C7878',//会员卡背景浅色
			goods_card_color: '#FFD792',
			goods_coupon: '#FC5227',
			goods_cart_num_corner: '#FC5227',//购物车数量角标
			goods_btn_color: '#CFAF70',//按钮颜色
			goods_btn_color_shallow: '#444444',//副按钮颜色
		},
		pintuan: {
			pintuan_label_bg: '#F7B500',
			pintuan_label_color: '#FFFFFF',
			pintuan_color: '#FA6400',
			pintuan_promotion_color: '#FA3A1D',//活动背景
			pintuan_promotion_aux_color: '#FD9A01',//活动背景辅色
		},
		super_member: {
			super_member_start_bg: '#7c7878',
			super_member_end_bg: '#201a18',
			super_member_start_text_color: '#FFDBA6',
			super_member_end_text_color: '#FFEBCA',
		},
		bargain: {
			bargain_promotion_color: '#F0353E',//活动背景
			bargain_promotion_aux_color: '#FD9A01',//活动辅色
		},
		seckill: {
			seckill_promotion_color: '#F83530',//活动背景
			seckill_promotion_aux_color: '#FD9A01',//活动辅色
		},
		giftcard: {
			giftcard_promotion_color: '#FF3369',//活动背景
			giftcard_promotion_aux_color: '#F7B500',//活动辅色
		},
		groupby: {
			groupby_promotion_color: '#E64136',//活动背景
			groupby_promotion_aux_color: '#F7B500',//活动辅色
		},
	},
	'purple': {
		name: 'purple',
		main_color: '#A253FF',
		aux_color: '#222222',
		bg_color: '#A253FF',//主题背景
		bg_color_shallow: '#A253FF',//主题背景渐变浅色
		promotion_color: '#A253FF',//活动背景
		promotion_aux_color: '#222222',//活动背景辅色
		main_color_shallow: '#F8F3FF',//淡背景
		price_color: 'rgba(252,82,39,1)',//价格颜色
		btn_text_color: '#FFFFFF',//按钮文字颜色
		goods_detail: {
			goods_price: 'rgba(252,82,39,1)',//价格
			promotion_tag: '#A253FF',
			goods_card_bg: '#201A18',//会员卡背景
			goods_card_bg_shallow: '#7C7878',//会员卡背景浅色
			goods_card_color: '#FFD792',
			goods_coupon: '#FC5227',
			goods_cart_num_corner: '#FC5227',//购物车数量角标
			goods_btn_color: '#A253FF',//按钮颜色
			goods_btn_color_shallow: '#222222',//副按钮颜色
		},
		pintuan: {
			pintuan_label_bg: '#F7B500',
			pintuan_label_color: '#FFFFFF',
			pintuan_color: '#FA6400',
			pintuan_promotion_color: '#FA3A1D',//活动背景
			pintuan_promotion_aux_color: '#FD9A01',//活动背景辅色
		},
		super_member: {
			super_member_start_bg: '#7c7878',
			super_member_end_bg: '#201a18',
			super_member_start_text_color: '#FFDBA6',
			super_member_end_text_color: '#FFEBCA',
		},
		bargain: {
			bargain_promotion_color: '#F0353E',//活动背景
			bargain_promotion_aux_color: '#FD9A01',//活动辅色
		},
		seckill: {
			seckill_promotion_color: '#F83530',//活动背景
			seckill_promotion_aux_color: '#FD9A01',//活动辅色
		},
		giftcard: {
			giftcard_promotion_color: '#FF3369',//活动背景
			giftcard_promotion_aux_color: '#F7B500',//活动辅色
		},
		groupby: {
			groupby_promotion_color: '#E64136',//活动背景
			groupby_promotion_aux_color: '#F7B500',//活动辅色
		},
	},
	'yellow': {
		name: 'yellow',
		main_color: '#FFD009',
		aux_color: '#1D262E',
		bg_color: '#FFD009',//主题背景
		bg_color_shallow: '#FFD009',//主题背景渐变浅色
		promotion_color: '#FFD009',//活动背景
		promotion_aux_color: '#1D262E',//活动背景辅色
		main_color_shallow: '#FFFBEF',//淡背景
		price_color: 'rgba(252,82,39,1)',//价格颜色
		btn_text_color: '#303133',//按钮文字颜色
		goods_detail: {
			goods_price: 'rgba(252,82,39,1)',//价格
			promotion_tag: '#FFD009',
			goods_card_bg: '#201A18',//会员卡背景
			goods_card_bg_shallow: '#7C7878',//会员卡背景浅色
			goods_card_color: '#FFD792',
			goods_coupon: '#FC5227',
			goods_cart_num_corner: '#FC5227',//购物车数量角标
			goods_btn_color: '#FFD009',//按钮颜色
			goods_btn_color_shallow: '#1D262E',//副按钮颜色
		},
		pintuan: {
			pintuan_label_bg: '#F7B500',
			pintuan_label_color: '#FFFFFF',
			pintuan_color: '#FA6400',
			pintuan_promotion_color: '#FA3A1D',//活动背景
			pintuan_promotion_aux_color: '#FD9A01',//活动背景辅色
		},
		super_member: {
			super_member_start_bg: '#7c7878',
			super_member_end_bg: '#201a18',
			super_member_start_text_color: '#FFDBA6',
			super_member_end_text_color: '#FFEBCA',
		},
		bargain: {
			bargain_promotion_color: '#F0353E',//活动背景
			bargain_promotion_aux_color: '#FD9A01',//活动辅色
		},
		seckill: {
			seckill_promotion_color: '#F83530',//活动背景
			seckill_promotion_aux_color: '#FD9A01',//活动辅色
		},
		giftcard: {
			giftcard_promotion_color: '#FF3369',//活动背景
			giftcard_promotion_aux_color: '#F7B500',//活动辅色
		},
		groupby: {
			groupby_promotion_color: '#E64136',//活动背景
			groupby_promotion_aux_color: '#F7B500',//活动辅色
		},
	},
	'black': {
		name: 'black',
		main_color: '#222222',
		aux_color: '#FFFFFF',
		bg_color: '#222222',//主题背景
		bg_color_shallow: '#333333',//主题背景渐变浅色
		promotion_color: '#222222',//活动背景
		promotion_aux_color: '#FA8B00',//活动背景辅色
		main_color_shallow: '#efefef',//淡背景
		price_color: 'rgba(255,0,0,1)',//价格颜色
		btn_text_color: '#FFFFFF',//按钮文字颜色
		goods_detail: {
			goods_price: 'rgba(255,0,0,1)',//价格
			promotion_tag: '#222222',
			goods_card_bg: '#201A18',//会员卡背景
			goods_card_bg_shallow: '#7C7878',//会员卡背景浅色
			goods_card_color: '#FFD792',
			goods_coupon: '#222222',
			goods_cart_num_corner: '#FF0000',//购物车数量角标
			goods_btn_color: '#222222',//按钮颜色
			goods_btn_color_shallow: '#FA8B00',//副按钮颜色
		},
		pintuan: {
			pintuan_label_bg: '#F7B500',
			pintuan_label_color: '#FFFFFF',
			pintuan_color: '#FA6400',
			pintuan_promotion_color: '#FA3A1D',//活动背景
			pintuan_promotion_aux_color: '#FD9A01',//活动背景辅色
		},
		super_member: {
			super_member_start_bg: '#fadcb5',
			super_member_end_bg: '#f6bd74',
			super_member_start_text_color: '#ab6126',
			super_member_end_text_color: '#d19336',
		},
		bargain: {
			bargain_promotion_color: '#F0353E',//活动背景
			bargain_promotion_aux_color: '#FD9A01',//活动辅色
		},
		seckill: {
			seckill_promotion_color: '#F83530',//活动背景
			seckill_promotion_aux_color: '#FD9A01',//活动辅色
		},
		giftcard: {
			giftcard_promotion_color: '#FF3369',//活动背景
			giftcard_promotion_aux_color: '#F7B500',//活动辅色
		},
		groupby: {
			groupby_promotion_color: '#E64136',//活动背景
			groupby_promotion_aux_color: '#F7B500',//活动辅色
		},
	}
}
