button, input, select, textarea {
    font-family: inherit;
    font-size: 100%;
    margin: 0;
}

input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
    color: #999;
}

input:-moz-placeholder, textarea:-moz-placeholder {
    color: #999;
}

.empty{width: 100%;margin-top: 15px;text-align: center;padding: 75px 0;}

input::-moz-placeholder, textarea::-moz-placeholder {
    color: #999;
}

input:-ms-input-placeholder, textarea:-ms-input-placeholder {
    color: #999;
}

li {
    list-style: none;
}
a:-webkit-any-link {
    color: #333;
    cursor: pointer;
    text-decoration: none;
}

.reply-opts a:-webkit-any-link {
    color: var(--base-color);
    cursor: pointer;
    text-decoration: none;
}
.reply-opts .delet-color:-webkit-any-link {
	color: #ff3d3d;
}
.rule-group .rule-meta h3 .rule-opts .delet-color {
	color: #ff3d3d;
}

button {
    display: inline-block;
    padding: 0 22px;
    min-width: 54px;
    line-height: 2.42857143;
    vertical-align: middle;
    text-align: center;
    text-decoration: none;
    border-radius: 3px;
    font-size: 14px;
    cursor: pointer;
    border-width: 1px;
    border-style: solid;
    box-sizing: content-box;
}

.layui-body {
    overflow-y: auto !important;
}

.hide {
    display: none;
}

.page-title h2 {
    font-size: 26px;
    font-weight: 400;
    line-height: 1;
    margin-bottom: 20px;
}

.type-menu {
    text-align: left;
    height: 50px;
    line-height: 40px;
    border-bottom: 1px solid #E0E1E2;
    font-size: 16px;
}

.type-menu li {
    float: left;
    margin-right: 24px;
    line-height: 40px;
    font-size: 16px;
}

.replay-info {
    padding: 15px 0 0;
    height: 30px;
}

.weixin-normal {
    margin-top: 15px;
    position: relative;
}

.replay-info .info {
    float: left;
}

.replay-info h3 {
    display: inline-block;
    font-size: 20px;
    font-weight: 400;
    line-height: 1;
    margin-right: 10px;
}

.replay-info h3:last-child {
    font-style: normal;
}

.replay-button {
    float: right;
    position: relative;
}

.replay-button > div {
    float: left;
}

.replay-button:after {
    content: '';
    clear: both;
}

.replay-button > label {
    background-color: #1AAD19;
    border-color: #1AAD19;
    color: #FFFFFF;
    display: inline-block;
    min-width: 54px;
    line-height: 2.42857143;
    vertical-align: middle;
    text-align: center;
    text-decoration: none;
    border-radius: 3px;
    font-size: 14px;
    cursor: pointer;
    border-width: 1px;
    border-style: solid;
    box-sizing: content-box;
}

.replay-button .layui-layer-page {
    text-align: left;
}

.replay-button .layui-form > .layui-btn {
    display: block;
    margin: auto;
    margin-top: 30px;
}

.replay-button label > input {
    position: absolute;
    left: -9999em;
}

.replay-button .search {
    display: inline-block;
    vertical-align: middle;
    margin-right: .5em;
    width: 50px;
}

.replay-button .down {
    float: left;
}

.replay-button .down button {
    padding: 14px 12px 10px 12px;
    min-width: unset;
}

.down .show-menu li a {
    color: #00A717;
}

.down .show-menu li a {
    padding: 0 15px;
}

.rule-group {
    border: 1px solid #e5e5e5;
    margin-bottom: 20px;
    border-radius: 2px;
    background: #fff;
}

.rule-group:hover {
    border: 1px solid #e5e5e5;
}

.rule-group .rule-meta {
    padding: 5px 10px;
}

.rule-group .rule-meta h3 .rule-opts a:hover {
    color: var(--base-color);
}

.rule-group .rule-meta h3 {
    width: 100%;
    position: relative;
    font-size: 14px;
    margin: 0;
    line-height: 1.5em;
    font-weight: bold;
}

.rule-group .rule-meta h3 .rule-opts {
    position: absolute;
    top: 0;
    right: 0px;
    font-weight: normal;
    font-size: 12px;
    color: #ddd;
}

.rule-group .rule-meta h3 .rule-opts a {
    color: var(--base-color);
}

.rule-group .rule-body {
    border-top: 1px solid #e5e5e5;
    /* margin: 0 0 10px;
    padding-top: 5px; */
    overflow: auto;
}

.rule-group .rule-body:before, .rule-group .rule-body:after {
    display: table;
    content: "";
    line-height: 0;
}

/* .rule-group .long-dashed {
    position: absolute;
    top: 80px;
    width: 100%;
    border-bottom: 1px dashed #e5e5e5;
} */

.weixin-normal .rule-keywords {
    float: left;
		margin-top:10px;
}

.rule-group .rule-inner {
    font-size: 12px;
    padding: 0 10px 5px;
}

.rule-group .rule-inner h4 {
    color: #000;
    font-weight: normal;
    font-size: 14px;
}

.rule-group .keyword-containe {
    padding-top: 5px;
    margin-bottom: 5px;
}

.rule-group .info:empty {
    padding: 0;
}

.rule-group .keyword-list, .reply-list {
    padding-top: 10px;
}

.keyword-list .keyword {
    position: relative;
    margin-right: 10px;
    margin-bottom: 10px;
    height: 30px;
    vertical-align: middle;
    cursor: pointer;
    display: inline-block;
}

.keyword .close--circle {
    display: none;
}

.keyword-list .keyword:hover .close--circle {
    display: block;
}

.complex-content .close--circle ,.keyword-list .close--circle {
    position: absolute;
    z-index: 91;
    top: -9px;
    right: -9px;
    width: 20px;
    height: 20px;
    font-size: 16px;
    line-height: 20px;
    color: #fff;
    text-align: center;
    cursor: pointer;
    background: rgba(153, 153, 153, 0.6);
    border-radius: 10px;
}

.keyword .value {
    background-color: #fff;
    text-align: center;
    display: inline-block;
    height: 20px;
    padding: 2px 10px;
    font-size: 12px;
    line-height: 20px;
    color: #333;
    vertical-align: middle;
    border-radius: 2px 0 0 2px;
    border: 1px solid #ccc;
}

.input-append .add-on, .input-prepend .add-on {
    display: inline-block;
    width: auto;
    height: 20px;
    min-width: 16px;
    padding: 2px 7px;
    font-size: 10px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    text-shadow: 0 1px 0 #fff;
    background-color: #f7f7f7;
    border: 1px solid #ccc;
    margin-left: -4px;
    border-radius: 0 2px 2px 0;
    vertical-align: middle;
}

/* .rule-group hr.dashed {
    border-bottom: 1px dashed #d7d7d7;
    border-top: none;
    margin: 0;
    background-color: rgb(255, 255, 255);

} */
.layui-textarea {
    resize:none
}
.rule-group .opt {
    margin-top: 5px;
}

.rule-group .opt a {
    color: var(--base-color);
}
.inner-keywordbox a {
	color: var(--base-color);
}

.weixin-normal .rule-replies {
    position: static;
    float: left;
		border-left: 1px solid #f7f7f7;
		padding-top: 10px;
		min-height: 115px;
}

.rule-group .rule-inner {
    font-size: 12px;
    padding: 0 10px 5px;
}

.rule-group .rule-inner .inner-keywordbox {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	padding: 8px 15px;
	background: #f7f7f7;
	border-radius: 2px;
}

.rule-group .rule-inner h4 {
    color: #000;
    font-weight: normal;
    font-size: 14px;
}

.rule-group .reply-container, .keyword-container {
    padding: 5px 0;
}

.rule-group .info {
    padding: 5px 0;
    color: #999;
}

.rule-group .info:empty {
    padding: 0;
}

ol.reply-list {
    list-style-type: decimal !important;
}

.reply-list li, .quick-dropmenu li {
    position: relative;
    padding: 8px 90px 8px 5px;
    margin-left: 20px;
    list-style: unset;
}

.reply-list .reply-opts a:hover {
    color: var(--base-color);
}

.reply-cont {
    display: inline-block;
    max-width: 100%;
}

.reply-summary {
    display: inline-block;
    max-width: 360px;
    word-break: break-all;
    word-wrap: break-word;
    vertical-align: top;
}

.reply-list .reply-opts, .quick-dropmenu .reply-opts {
    position: absolute;
    top: 8px;
    right: 5px;
}

.reply-list li::after, .quick-dropmenu li::after {
    content: "";
    position: absolute;
    border-bottom: 1px dashed #d7d7d7;
    bottom: 0;
    left: -20px;
    right: 0;
}

.reply-list li:last-child::after, .quick-dropmenu li:last-child::after {
    border-bottom: none;
}

.rule-group .opt .disable-opt {
    color: #999;
}

.badge-success, .label-success {
    display: inline-block;
    padding: 2px 4px;
    font-size: 12px;
    line-height: 14px;
    color: #fff;
    white-space: nowrap;
    background-color: #1AAD19;
}

/***************************************/
.misc {
    height: 45px;
}

.misc > a {
    display: inline-block;
    padding: 7px 10px;
    color: var(--base-color) !important;
    border-bottom: 2px solid #fff;
}

.misc .active {
    color: var(--base-color)!important;
    border-color: var(--base-color);
}

.others {
    display: inline-block;
    position: relative;
}

.others > a {
    padding: 10px;
    color: var(--base-color) !important
}

.pull-right {
    color: #ccc;
    float: right;
}

.dropdown-menu {
    display: none;
    position: absolute;
    z-index: 100;
    top: 25px;
    left: 10px;
    width: 110px;
    height: 110px;
    font-size: 12px;
    border: 1px solid rgba(0, 0, 0, 0.125);
    cursor: pointer;
    background: #fff;
    border-radius: 10px;
    padding: 10px 0;
    overflow: auto;
}

.dropdown-menu li {
    padding: 0 10px;
    line-height: 30px;
    color: #333;
}

.others:hover .dropdown-menu {
    display: block;
}

.dropdown-menu li:hover {
    background: var(--base-color);
}

.dropdown-menu li:hover a {
    color: #fff;
}

.complex-backdrop {
    display: none;
    position: absolute;
    top: 75px;
    left: 25px;
    width: 86%;
    height: 36.5%;
    background-color: #fff;
    /* -webkit-box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.25);
    box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.25); */
		/* border:1px solid #E6E6E6; */
    /* padding-top: 12px; */
}

.layui-layout-admin .layui-body .body-content {
	background: #f8f8f8;
	padding: 0;
}

.fourstage-nav .layui-tab-title {
	background: #fff;
	height: 50px;
}

.fourstage-nav .layui-tab-title li {
	line-height: 48px;
    margin: 0 15px;
}

.keyword-content {
	background: #fff;
	padding: 15px;
	margin-top: 15px;
}

[lay-id=marterial_graphic_message_list] .article-img .bg-color {
    background-color: #1aad19 !important;
}
.layui-table-view{
	background-color: red!important;
}

.complex-content {
    padding: 15px;
}

.ng.ng-image {
    width: 80px;
    height: 80px;
    border: none;
    text-align: center;
}

.insert-connect {
	position: absolute;
	right: 10px;
	bottom: 20px;
	color: var(--base-color) !important;
}

.ng {
    position: relative;
    vertical-align: top;
    width: 250px;
    border-radius: 5px;
    border: 1px solid #eee;
    background-color: #fff;
    margin-bottom: 5px;
    display: inline-block;
}

.picture > img {
    width: auto;
    max-width: 100%;
    height: auto;
    max-height: 100%;
}

.msg-music-thumb {
    height: 100px;
    text-align: center;
}

.msg-music-thumb a {
    width: 100%;
    height: 100%;
    border: 1px dashed #F2F2F2;
    display: block;
    line-height: 100px;
    color: var(--base-color);
}

#music .layui-textarea {
    min-height: 60px;
}

.voice-player {
    border-radius: 5px;
    position: relative;
    border: 1px solid #85ac4c;
    display: inline-block;
    width: 90px;
    height: 25px;
    padding: 0 6px 0 7px;
    font-size: 12px !important;
    line-height: 25px;
    cursor: pointer;
    background: #a0ce3d;
    vertical-align: middle;
    margin-left: 7px;
}

.voice-player::before {
    position: absolute;
    content: "";
    left: -13px;
    top: 6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-right: 6px solid #85ac4c;
}

.voice-player .stop {
    display: inline-block;
    color: #fff;
    text-shadow: 1px 1px 1px #8ab433;
}

.popover .close--circle {
    z-index: initial;
}

.close--circle {
    position: absolute;
    z-index: 91;
    top: 5px;
    right: 10px;
    font-size: 28px;
		color: #333;
    text-align: center;
    cursor: pointer;
}

.voice-player .second {
    display: none;
    float: right;
    font-size: 12px;
    color: #476600;
    margin-left: 2px;
}

.voice-player .play {
    display: inline-block;
    width: 17px;
    height: 20px;
    margin-top: 2px;
}

.voice-player::after {
    position: absolute;
    content: "";
    left: -12px;
    top: 6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-right: 6px solid #a0ce3d;
}

/*其他*/
.ng .close--circle {
    top: -9px;
    right: -9px;
}

.ng .ng-item {
    border-bottom: 1px solid #eee;
    overflow: hidden;
    padding: 5px 9px;
}

.ng .label {
    vertical-align: middle;
}

/*图文*/
.ng .ng-title {
    display: inline-block !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    text-overflow: ellipsis !important;
    line-height: 16px;
    min-height: 16px;
    vertical-align: middle;
    max-width: 180px;
}

a.new-window {
    color: var(--base-color);
}

.ng .ng-item.view-more {
    color: #666;
}

.ng .ng-item.view-more a {
    color: #666;
}

/*************************************/

.rule-container .add_reply-top{
	position: absolute;
	width: 0px; 
	height: 0px; 
	line-height: 0px;/*为了防止ie下出现题型*/ 
	border-bottom: 10px solid #fff; 
	border-left: 10px solid rgba(0,0,0,0); 
	border-right: 10px solid rgba(0,0,0,0);
	right: 55px; 
	top: -10px;
}

.popover > .close--circle {
    top: -5px;
    right: -5px;
}

.rule-container .arrow {
    position: absolute;
    width: 0;
    height: 0;
    top: 50%;
    left: -13px;
    margin-top: -5px;
}

.rule-container .arrow i {
    color: #e5e5e5;
    background: #fff;
}

.rule-group-container {
    position: relative;
    min-height: 165px
}

.search {
    position: absolute;
    z-index: 1;
    top: 1px;
    right: 0;
}

.layui-form-item .layui-textarea {
    width: 100%;
}

.layui-form.hyperlink {
    padding: 10px;
    margin-bottom: 0;
}

.layui-form.hyperlink .layui-form-item {
    margin-bottom: 0;
}
.layui-layout-shop .fourstage-nav ul li.layui-this:after {
    border: none !important;
    width: 100%;
    height: 4px;
    background-color: var(--base-color);
    border-radius: 30px !important;
    position: absolute;
    top: 40px;
}