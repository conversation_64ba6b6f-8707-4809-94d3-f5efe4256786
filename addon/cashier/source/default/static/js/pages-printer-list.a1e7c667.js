(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-printer-list"],{"206e":function(t,i,e){"use strict";e.r(i);var r=e("8a60"),n=e("e26b");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);e("24d4");var s=e("828b"),l=Object(s["a"])(n["default"],r["b"],r["c"],!1,null,"2acf7057",null,!1,r["a"],void 0);i["default"]=l.exports},"24d4":function(t,i,e){"use strict";var r=e("b704"),n=e.n(r);n.a},"3cc3":function(t,i,e){var r=e("c86c");i=r(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-2acf7057]{display:none}\r\n/* 收银台相关 */uni-text[data-v-2acf7057],\r\nuni-view[data-v-2acf7057]{font-size:.14rem}body[data-v-2acf7057]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-2acf7057]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-2acf7057]::-webkit-scrollbar-button{display:none}body[data-v-2acf7057]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-2acf7057]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-2acf7057]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-2acf7057]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-2acf7057]{color:var(--primary-color)!important}.printerlist[data-v-2acf7057]{width:100%;height:100%;display:flex;align-items:center;justify-content:space-between;box-sizing:border-box}.printerlist .printerlist-box[data-v-2acf7057]{width:100%;height:100%;background:#fff;display:flex}.printerlist .printerlist-box .printerlist-left[data-v-2acf7057]{width:5rem;height:100%;border-right:.01rem solid #e6e6e6;box-sizing:border-box;display:flex;flex-direction:column}.printerlist .printerlist-box .printerlist-left .notYet[data-v-2acf7057]{color:#e6e6e6;font-size:.4rem;margin-top:3rem;text-align:center}.printerlist .printerlist-box .printerlist-left .add-printer[data-v-2acf7057]{padding:.24rem .2rem;background:#fff}.printerlist .printerlist-box .printerlist-left .add-printer uni-button[data-v-2acf7057]{height:.4rem;line-height:.4rem}.printerlist .printerlist-box .printerlist-left .printer-title[data-v-2acf7057]{text-align:center;line-height:.6rem;font-size:.18rem;font-weight:500;height:.6rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box;position:relative}.printerlist .printerlist-box .printerlist-left .printer-title .icongengduo1[data-v-2acf7057]{position:absolute;top:50%;right:.2rem;-webkit-transform:translateY(-50%);transform:translateY(-50%);font-size:.3rem;color:var(--primary-color)}.printerlist .printerlist-box .printerlist-left .printer-list-wrap[data-v-2acf7057]{flex:1;height:0}.printerlist .printerlist-box .printerlist-left .printer-list-scroll[data-v-2acf7057]{width:100%;height:100%}.printerlist .printerlist-box .printerlist-left .printer-list-scroll .itemhover[data-v-2acf7057]{background:var(--primary-color-light-9)}.printerlist .printerlist-box .printerlist-left .printer-list-scroll .item[data-v-2acf7057]{width:100%;display:flex;align-items:center;padding:.2rem;box-sizing:border-box;border-bottom:.01rem solid #e6e6e6}.printerlist .printerlist-box .printerlist-left .printer-list-scroll .item uni-image[data-v-2acf7057]{width:.7rem;height:.7rem;margin-right:.1rem}.printerlist .printerlist-box .printerlist-left .printer-list-scroll .item .item-right[data-v-2acf7057]{display:flex;flex-direction:column;justify-content:space-between;height:.6rem}.printerlist .printerlist-box .printerlist-left .printer-list-scroll .item .item-right .printer-name[data-v-2acf7057]{font-size:.16rem}.printerlist .printerlist-box .printerlist-left .printer-list-scroll .item .item-right .printer-money[data-v-2acf7057]{font-size:.14rem}.printerlist .printerlist-box .printerlist-right[data-v-2acf7057]{width:0;flex:1;height:100%;box-sizing:border-box;position:relative}.printerlist .printerlist-box .printerlist-right .printer-title[data-v-2acf7057]{text-align:center;line-height:.6rem;font-size:.18rem;font-weight:500;height:.6rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box;position:relative}.printerlist .printerlist-box .printerlist-right .printer-title .icongengduo1[data-v-2acf7057], .printerlist .printerlist-box .printerlist-right .printer-title .iconguanbi1[data-v-2acf7057]{position:absolute;top:50%;right:.2rem;-webkit-transform:translateY(-50%);transform:translateY(-50%);font-size:.3rem;color:var(--primary-color);cursor:pointer}.printerlist .printerlist-box .printerlist-right .printer-information[data-v-2acf7057]{width:100%;padding:.2rem .2rem .88rem .2rem;box-sizing:border-box;height:calc(100% - .6rem);overflow:auto;position:relative}.printerlist .printerlist-box .printerlist-right .printer-information .title[data-v-2acf7057]{font-size:.18rem;margin-bottom:.32rem}.printerlist .printerlist-box .printerlist-right .printer-information .title2[data-v-2acf7057]{margin-bottom:.35rem}.printerlist .printerlist-box .printerlist-right .printer-information .information-box[data-v-2acf7057]{display:flex;justify-content:space-between}.printerlist .printerlist-box .printerlist-right .printer-information .information-box .box-left[data-v-2acf7057]{width:5rem}.printerlist .printerlist-box .printerlist-right .printer-information .information-box .box-left .information[data-v-2acf7057]{width:100%;padding-left:.1rem;box-sizing:border-box;display:flex;align-items:center;margin-bottom:.15rem}.printerlist .printerlist-box .printerlist-right .printer-information .information-box .box-left .information uni-view[data-v-2acf7057]{color:#303133;font-size:.14rem}.printerlist .printerlist-box .printerlist-right .printer-information .information-box .box-left .information uni-view[data-v-2acf7057]:nth-child(1){width:1.3rem;margin-right:.16rem;text-align:right}.printerlist .printerlist-box .printerlist-right .printer-information .information-box .box-left .information uni-view[data-v-2acf7057]:nth-child(2){width:74%;margin-right:.23rem;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.printerlist .printerlist-box .printerlist-right .printer-information .information-box .box-left .information[data-v-2acf7057]:last-child{margin-bottom:.35rem}.printerlist .printerlist-box .printerlist-right .printer-information .information-box .printer-img[data-v-2acf7057]{width:1.5rem;height:1.5rem}.printerlist .printerlist-box .printerlist-right .printer-information .table[data-v-2acf7057]{width:100%;height:2.6rem;box-sizing:border-box}.printerlist .printerlist-box .printerlist-right .printer-information .table .table-all[data-v-2acf7057]{width:100%;display:flex;align-items:center;justify-content:space-between;padding:0 .38rem;box-sizing:border-box}.printerlist .printerlist-box .printerlist-right .printer-information .table .table-all .table-td[data-v-2acf7057]{font-size:.14rem;text-align:left;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.printerlist .printerlist-box .printerlist-right .printer-information .table .table-th[data-v-2acf7057]{height:.56rem;background:#f7f8fa}.printerlist .printerlist-box .printerlist-right .printer-information .table .table-tb[data-v-2acf7057]{width:100%;height:calc(100% - .56rem)}.printerlist .printerlist-box .printerlist-right .printer-information .table .table-tb .table-tr[data-v-2acf7057]{height:.7rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box}.printerlist .printerlist-box .printerlist-right .printer-information .table .table-tb .table-tr .table-td[data-v-2acf7057]{text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical}.printerlist .printerlist-box .printerlist-right .printer-information .table .table-tb .table-tr .table-td uni-image[data-v-2acf7057]{width:.5rem;height:.5rem}uni-view[data-v-2acf7057]{color:#303133}[data-v-2acf7057] .uni-scroll-view::-webkit-scrollbar{width:.05rem;height:.3rem}[data-v-2acf7057] .uni-scroll-view::-webkit-scrollbar-thumb{border-radius:.1rem;box-shadow:inset 0 0 .05rem rgba(0,0,0,.2);background:#c1c1c1}.printer-information[data-v-2acf7057]::-webkit-scrollbar{width:.05rem;height:.3rem}.printer-information[data-v-2acf7057]::-webkit-scrollbar-thumb{border-radius:.1rem;box-shadow:inset 0 0 .05rem rgba(0,0,0,.2);background:#c1c1c1}.button-box[data-v-2acf7057]{position:absolute;width:100%;right:0;bottom:0;background-color:#fff;display:flex;align-items:center;justify-content:flex-end;padding:.24rem .2rem;box-sizing:border-box}.button-box uni-button[data-v-2acf7057]{width:1rem;height:.4rem;line-height:.4rem;margin:0;margin-left:.1rem}.button-box[data-v-2acf7057]:after{overflow:hidden;content:"";height:0;display:block;clear:both}.cart-empty[data-v-2acf7057]{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:2.1rem}.order-type[data-v-2acf7057]{margin-right:.1rem}',""]),t.exports=i},"518d":function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("5c47"),e("af8f"),e("c223"),e("bf0f"),e("2797");var r=e("bf41"),n={data:function(){return{selectprinterKeys:0,search_text:"",page:1,page_size:8,one_judge:!0,detail:{},brandList:{yilianyun:"易联云",365:"365"},template:{},list:[],repeatFlag:!1}},onLoad:function(){this.getTemplateFn()},methods:{printerType:function(t){var i="";switch(t){case"cloud":i="云打印机";break;case"local":i="本地打印机";break;case"network":i="网络打印机";break}return i},switchStoreAfter:function(){this.search()},printerSelect:function(t,i){this.selectprinterKeys=i,this.getPrinterDetail(t.printer_id)},search:function(){this.page=1,this.list=[],this.one_judge=!0,this.getPrinterListFn()},addprinter:function(){this.$util.redirectTo("/pages/printer/add")},editprinter:function(t){this.$util.redirectTo("/pages/printer/add",{printer_id:t})},getPrinterListFn:function(){var t=this;(0,r.getPrinterList)({page:this.page,page_size:this.page_size}).then((function(i){0==i.data.list.length&&t.one_judge&&(t.detail={},t.one_judge=!1),i.code>=0&&0!=i.data.list.length&&(t.page+=1,0==t.list.length?t.list=i.data.list:t.list=t.list.concat(i.data.list),t.one_judge&&t.getPrinterDetail(t.list[0].printer_id))}))},getTemplateFn:function(){var t=this;(0,r.getTemplate)().then((function(i){if(0==i.code){var e={};i.data.forEach((function(t){e[t.template_id]=t})),t.template=e,t.getPrinterListFn()}}))},getPrinterDetail:function(t){var i=this;(0,r.getPrinterInfo)(t).then((function(t){0==t.code&&(i.detail=t.data,i.one_judge=!1)}))},deletePrinterFn:function(t){var i=this;this.repeatFlag||(this.repeatFlag=!0,(0,r.deletePrinter)(t).then((function(t){i.repeatFlag=!1,t.code>=0?(i.page=1,i.list=[],i.one_judge=!0,i.$refs.deletePop.close(),i.getPrinterListFn()):i.$util.showToast({title:t.message})})))}}};i.default=n},"8a60":function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){return r}));var r={uniPopup:e("cea0").default},n=function(){var t=this,i=t.$createElement,r=t._self._c||i;return r("base-page",[r("v-uni-view",{staticClass:"printerlist"},[r("v-uni-view",{staticClass:"printerlist-box"},[r("v-uni-view",{staticClass:"printerlist-left"},[r("v-uni-view",{staticClass:"printer-title"},[t._v("打印机"),r("v-uni-text",{staticClass:"iconfont icongengduo1"})],1),r("v-uni-view",{staticClass:"printer-list-wrap"},[t.list.length>0?[r("v-uni-scroll-view",{staticClass:"printer-list-scroll all-scroll",attrs:{"scroll-y":"true"},on:{scrolltolower:function(i){arguments[0]=i=t.$handleEvent(i),t.getPrinterListFn.apply(void 0,arguments)}}},t._l(t.list,(function(i,e){return r("v-uni-view",{key:e,staticClass:"item",class:e==t.selectprinterKeys?"itemhover":"",on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.printerSelect(i,e)}}},[r("v-uni-view",{staticClass:"item-right"},[r("v-uni-view",{staticClass:"printer-name"},[t._v(t._s(i.printer_name))]),r("v-uni-view",{staticClass:"printer-money"},[t._v(t._s(t.printerType(i.printer_type)))])],1)],1)})),1)]:t.one_judge||0!=t.list.length?t._e():r("v-uni-view",{staticClass:"notYet"},[t._v("暂无打印机")])],2),r("v-uni-view",{staticClass:"add-printer"},[r("v-uni-button",{staticClass:"primary-btn",attrs:{type:"default"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.addprinter.apply(void 0,arguments)}}},[t._v("添加打印机")])],1)],1),r("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!t.one_judge,expression:"!one_judge"}],staticClass:"printerlist-right"},[r("v-uni-view",{staticClass:"printer-title"},[t._v("打印机详情")]),r("v-uni-view",{staticClass:"printer-information"},["{}"!=JSON.stringify(t.detail)?[r("v-uni-view",{staticClass:"title"},[t._v("基本信息")]),r("v-uni-view",{staticClass:"information-box"},[r("v-uni-view",{staticClass:"box-left"},[r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("名称：")]),r("v-uni-view",[t._v(t._s(t.detail.printer_name))])],1),r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印机类型：")]),r("v-uni-view",[t._v(t._s(t.printerType(t.detail.printer_type)))])],1),"cloud"==t.detail.printer_type?[r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("品牌：")]),r("v-uni-view",[t._v(t._s(t.brandList[t.detail.brand]))])],1),r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印机编号：")]),r("v-uni-view",[t._v(t._s(t.detail.printer_code))])],1),r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印机秘钥：")]),r("v-uni-view",[t._v(t._s(t.detail.printer_key))])],1),r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("应用id：")]),r("v-uni-view",[t._v(t._s(t.detail.open_id))])],1),r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("apiKey：")]),r("v-uni-view",[t._v(t._s(t.detail.apikey))])],1)]:t._e(),"local"==t.detail.printer_type?[r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印机端口：")]),r("v-uni-view",[t._v(t._s(t.detail.host))])],1)]:t._e(),"network"==t.detail.printer_type?[r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印机地址：")]),r("v-uni-view",[t._v(t._s(t.detail.ip))])],1),r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印机端口：")]),r("v-uni-view",[t._v(t._s(t.detail.host))])],1)]:t._e(),r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("添加时间：")]),r("v-uni-view",[t._v(t._s(t.detail.create_time?t.$util.timeFormat(t.detail.create_time):"--"))])],1)],2)],1),r("v-uni-view",{staticClass:"title"},[t._v("支付打印")]),r("v-uni-view",{staticClass:"information-box"},[r("v-uni-view",{staticClass:"box-left"},[r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("支付打印：")]),r("v-uni-view",[t._v(t._s(t.detail.order_pay_open?"开启":"关闭"))])],1),t.detail.order_pay_open?r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印模板：")]),r("v-uni-view",[t._v(t._s(t.template[t.detail.order_pay_template_id]?t.template[t.detail.order_pay_template_id].template_name:"--"))])],1):t._e(),t.detail.order_pay_open?r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印联数：")]),r("v-uni-view",[t._v(t._s(t.detail.order_pay_print_num))])],1):t._e(),t.detail.order_pay_open?r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("订单类型：")]),r("v-uni-view",[t._l(t.detail.order_pay_order_type,(function(i,e){return[i?r("v-uni-text",{key:e+"_0",staticClass:"order-type"},[t._v(t._s(t.detail["order_type_list"][i]["name"]))]):t._e()]}))],2)],1):t._e()],1)],1),r("v-uni-view",{staticClass:"title"},[t._v("收货打印")]),r("v-uni-view",{staticClass:"information-box"},[r("v-uni-view",{staticClass:"box-left"},[r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("收货打印：")]),r("v-uni-view",[t._v(t._s(t.detail.take_delivery_open?"开启":"关闭"))])],1),t.detail.take_delivery_open?r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印模板：")]),r("v-uni-view",[t._v(t._s(t.template[t.detail.take_delivery_template_id]?t.template[t.detail.take_delivery_template_id].template_name:"--"))])],1):t._e(),t.detail.take_delivery_open?r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印联数：")]),r("v-uni-view",[t._v(t._s(t.detail.take_delivery_print_num))])],1):t._e(),t.detail.take_delivery_open?r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("订单类型：")]),r("v-uni-view",[t._l(t.detail.take_delivery_order_type,(function(i,e){return[i?r("v-uni-text",{key:e+"_0",staticClass:"order-type"},[t._v(t._s(t.detail["order_type_list"][i]["name"]))]):t._e()]}))],2)],1):t._e()],1)],1),r("v-uni-view",{staticClass:"title"},[t._v("手动打印")]),r("v-uni-view",{staticClass:"information-box"},[r("v-uni-view",{staticClass:"box-left"},[r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("手动打印：")]),r("v-uni-view",[t._v(t._s(t.detail.manual_open?"开启":"关闭"))])],1),t.detail.manual_open?r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印模板：")]),r("v-uni-view",[t._v(t._s(t.template[t.detail.template_id]?t.template[t.detail.template_id].template_name:"--"))])],1):t._e(),t.detail.manual_open?r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印联数：")]),r("v-uni-view",[t._v(t._s(t.detail.print_num))])],1):t._e()],1)],1),r("v-uni-view",{staticClass:"title"},[t._v("充值打印")]),r("v-uni-view",{staticClass:"information-box"},[r("v-uni-view",{staticClass:"box-left"},[r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("充值打印：")]),r("v-uni-view",[t._v(t._s(t.detail.recharge_open?"开启":"关闭"))])],1),t.detail.recharge_open?r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印模板：")]),r("v-uni-view",[t._v(t._s(t.template[t.detail.recharge_template_id]?t.template[t.detail.recharge_template_id].template_name:"--"))])],1):t._e(),t.detail.recharge_open?r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印联数：")]),r("v-uni-view",[t._v(t._s(t.detail.recharge_print_num))])],1):t._e()],1)],1),r("v-uni-view",{staticClass:"title"},[t._v("收银交班打印")]),r("v-uni-view",{staticClass:"information-box"},[r("v-uni-view",{staticClass:"box-left"},[r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("收银交班打印：")]),r("v-uni-view",[t._v(t._s(t.detail.change_shifts_open?"开启":"关闭"))])],1),t.detail.change_shifts_open?r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印模板：")]),r("v-uni-view",[t._v(t._s(t.template[t.detail.change_shifts_template_id]?t.template[t.detail.change_shifts_template_id].template_name:"--"))])],1):t._e(),t.detail.change_shifts_open?r("v-uni-view",{staticClass:"information"},[r("v-uni-view",[t._v("打印联数：")]),r("v-uni-view",[t._v(t._s(t.detail.change_shifts_print_num))])],1):t._e()],1)],1)]:[r("v-uni-image",{staticClass:"cart-empty",attrs:{src:e("be06"),mode:"widthFix"}})]],2),"{}"!=JSON.stringify(t.detail)?r("v-uni-view",{staticClass:"button-box"},[r("v-uni-button",{staticClass:"default-btn",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$refs.deletePop.open()}}},[t._v("删除")]),r("v-uni-button",{staticClass:"default-btn",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.editprinter(t.detail.printer_id)}}},[t._v("修改")])],1):t._e()],1)],1)],1),r("uni-popup",{ref:"deletePop",attrs:{type:"center"}},[r("v-uni-view",{staticClass:"confirm-pop"},[r("v-uni-view",{staticClass:"title"},[t._v("确定要删除吗？")]),r("v-uni-view",{staticClass:"btn"},[r("v-uni-button",{staticClass:"default-btn btn save",attrs:{type:"primary"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$refs.deletePop.close()}}},[t._v("取消")]),r("v-uni-button",{staticClass:"primary-btn btn",attrs:{type:"primary"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.deletePrinterFn(t.detail.printer_id)}}},[t._v("确定")])],1)],1)],1)],1)},a=[]},b704:function(t,i,e){var r=e("3cc3");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=e("967d").default;n("ffa122c4",r,!0,{sourceMap:!1,shadowMode:!1})},be06:function(t,i,e){t.exports=e.p+"static/cashier/cart_empty.png"},bf41:function(t,i,e){"use strict";e("6a54");var r=e("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.addPrinter=function(t){return n.default.post("/printer/storeapi/printer/add",{data:t})},i.deletePrinter=function(t){return n.default.post("/printer/storeapi/printer/deleteprinter",{data:{printer_id:t}})},i.editPrinter=function(t){return n.default.post("/printer/storeapi/printer/edit",{data:t})},i.getOrderType=function(){return n.default.post("/printer/storeapi/printer/getordertype")},i.getPrinterInfo=function(t){return n.default.post("/printer/storeapi/printer/info",{data:{printer_id:t}})},i.getPrinterList=function(t){return n.default.post("/printer/storeapi/printer/lists",{data:t})},i.getTemplate=function(){return n.default.post("/printer/storeapi/printer/template")},i.printTicket=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=a.default.getLocalConfig();return t.printer_ids="all"==i.printerSelectType?"all":i.printerSelectIds.toString(),n.default.post("/cashier/storeapi/cashier/printticket",{data:t})},e("c9b5"),e("bf0f"),e("ab80");var n=r(e("a3b5")),a=r(e("3885"))},e26b:function(t,i,e){"use strict";e.r(i);var r=e("518d"),n=e.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return r[t]}))}(a);i["default"]=n.a}}]);