(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-print_price_tag"],{"070d":function(t,e,a){var i=a("091a");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("413eb9a4",i,!0,{sourceMap:!1,shadowMode:!1})},"091a":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-c0c460c6]{display:none}\r\n/* 收银台相关 */uni-text[data-v-c0c460c6],\r\nuni-view[data-v-c0c460c6]{font-size:.14rem}body[data-v-c0c460c6]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-c0c460c6]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-c0c460c6]::-webkit-scrollbar-button{display:none}body[data-v-c0c460c6]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-c0c460c6]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-c0c460c6]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-c0c460c6]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-c0c460c6]{color:var(--primary-color)!important}.stock-dialog-wrap[data-v-c0c460c6]{background-color:#fff;border-radius:.05rem;width:100%;height:75vh}.stock-dialog-wrap .stock-dialog-head[data-v-c0c460c6]{padding:0 .15rem;display:flex;align-items:center;justify-content:space-between;font-size:.15rem;height:.45rem;border-bottom:.01rem solid #e8eaec}.stock-dialog-wrap .stock-dialog-head .iconguanbi1[data-v-c0c460c6]{font-size:.16rem}.stock-dialog-wrap .stock-dialog-body[data-v-c0c460c6]{width:100%;height:calc(100% - .45rem - .58rem);padding:.1rem .2rem 0 .2rem;box-sizing:border-box;display:flex}.stock-dialog-wrap .stock-dialog-body .tree[data-v-c0c460c6]{width:1.8rem;height:100%;overflow-y:auto;border-right:.01rem solid #e8eaec;flex-shrink:0;flex-basis:auto;flex-grow:0;box-sizing:border-box}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap[data-v-c0c460c6]{width:100%;height:100%}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap > uni-view[data-v-c0c460c6]{box-sizing:border-box;width:100%}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item[data-v-c0c460c6]{display:flex;align-items:center;width:100%;box-sizing:border-box;line-height:.3rem;min-height:.3rem;font-weight:500}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item.active[data-v-c0c460c6]{background-color:#f7f7f7}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item.active .icon[data-v-c0c460c6],\r\n.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item.active uni-view[data-v-c0c460c6]{color:var(--primary-color)!important}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item[data-v-c0c460c6]:hover{background-color:#f7f7f7}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item .icon[data-v-c0c460c6]{width:.2rem;height:.3rem;display:flex;align-items:center;justify-content:center;-webkit-transform:rotate(-90deg);transform:rotate(-90deg);transition:all ease .5s}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item .icon.active[data-v-c0c460c6]{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap .level[data-v-c0c460c6]{width:100%;box-sizing:border-box}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap .level .item[data-v-c0c460c6]{padding-left:.2rem}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap .level .item2[data-v-c0c460c6]{padding-left:.4rem}.stock-dialog-wrap .stock-dialog-body .stock-dialog-table[data-v-c0c460c6]{width:6.6rem;margin-left:.2rem;display:flex;flex-direction:column;height:100%}.stock-dialog-wrap .stock-dialog-body .stock-dialog-table .search[data-v-c0c460c6]{display:flex;justify-content:flex-end}.stock-dialog-wrap .stock-dialog-body .stock-dialog-table .goods-table[data-v-c0c460c6] {flex:1;height:0}.stock-dialog-wrap .stock-dialog-body .stock-dialog-table .goods-table[data-v-c0c460c6] .tbody{height:calc(100% - .5rem - .5rem);overflow-y:auto}.stock-dialog-wrap .stock-dialog-body .stock-dialog-table .goods-table[data-v-c0c460c6] .tbody::-webkit-scrollbar{width:.06rem;height:.06rem;background-color:transparent}.stock-dialog-wrap .stock-dialog-body .stock-dialog-table .goods-table[data-v-c0c460c6] .tbody::-webkit-scrollbar-button{display:none}.stock-dialog-wrap .stock-dialog-body .stock-dialog-table .goods-table[data-v-c0c460c6] .tbody::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}.stock-dialog-wrap .stock-dialog-body .stock-dialog-table .goods-table[data-v-c0c460c6] .tbody::-webkit-scrollbar-track{background-color:initial}.stock-dialog-wrap .btn[data-v-c0c460c6]{display:flex;justify-content:flex-end;border-top:.01rem solid #e8eaec;padding:.1rem .2rem .1rem .2rem;box-sizing:border-box;height:.58rem}.stock-dialog-wrap .btn .default-btn[data-v-c0c460c6],\r\n.stock-dialog-wrap .btn .primary-btn[data-v-c0c460c6]{margin:0}.stock-dialog-wrap .btn .default-btn[data-v-c0c460c6]{border:.01rem solid #e8eaec!important}.stock-dialog-wrap .btn .submit[data-v-c0c460c6]{margin-right:.15rem}.stock-dialog-wrap .btn .default-btn[data-v-c0c460c6]::after{display:none}.stock-dialog-wrap .common-form .common-btn-wrap[data-v-c0c460c6]{margin-left:0}.stock-dialog-wrap .common-form .common-btn-wrap .screen-btn[data-v-c0c460c6]{margin-right:0;padding-left:14px;padding-right:14px}.stock-dialog-wrap .common-form .common-form-item[data-v-c0c460c6]{margin-bottom:.1rem}.stock-dialog-wrap .common-form .common-form-item .form-input-inline[data-v-c0c460c6]{width:1.3rem}.stock-dialog-wrap[data-v-c0c460c6] .goods-content{display:flex}.stock-dialog-wrap[data-v-c0c460c6] .goods-content .goods-img{margin-right:.1rem;width:.5rem;height:.5rem;flex-shrink:0;flex-basis:auto;flex-grow:0}',""]),t.exports=e},"1bf0":function(t,e,a){"use strict";var i=a("39fd"),n=a.n(i);n.a},3460:function(t,e,a){"use strict";var i=a("6537"),n=a.n(i);n.a},3523:function(t,e,a){"use strict";a.r(e);var i=a("6e0b"),n=a("53f4");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("1bf0");var s=a("828b"),l=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"3387bb78",null,!1,i["a"],void 0);e["default"]=l.exports},"39fd":function(t,e,a){var i=a("48fc");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("2cfa7e01",i,!0,{sourceMap:!1,shadowMode:!1})},"3d30":function(t,e,a){"use strict";var i=a("070d"),n=a.n(i);n.a},"3fef":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uniPagination:a("a7c0").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"table-container"},[a("v-uni-view",{staticClass:"thead"},t._l(t.cols,(function(e,i){return a("v-uni-view",{key:i,staticClass:"th",style:{flex:e.width,maxWidth:e.width+"%",textAlign:e.align?e.align:"center"}},[a("v-uni-view",{staticClass:"content"},[e.checkbox?[a("v-uni-view",{staticClass:"all-select",on:{mouseenter:function(e){arguments[0]=e=t.$handleEvent(e),t.allSelectShow()},mouseleave:function(e){arguments[0]=e=t.$handleEvent(e),t.allSelectHide()}}},[a("v-uni-view",{staticClass:"all-select-label"},[a("v-uni-text",[t._v("全选"),t.allSelect.num>0?a("v-uni-text",[t._v("("+t._s(t.allSelect.num)+")")]):t._e()],1),a("v-uni-text",{staticClass:"iconfont iconxiala"})],1),a("v-uni-view",{staticClass:"all-select-option",style:t.allSelect.show?"display:block;":""},t._l(t.allSelect.optionList,(function(e){return a("v-uni-view",{class:t.allSelect.hoverOption==e.id?"on":"",on:{mouseenter:function(a){arguments[0]=a=t.$handleEvent(a),t.allSelect.hoverOption=e.id},click:function(a){arguments[0]=a=t.$handleEvent(a),t.allSelectClick(e)}}},[t._v(t._s(e.name)),"curr"==e.id&&t.allSelect.pageSelected[t.page]||"all"==e.id&&t.allSelect.selected?a("v-uni-text",{staticStyle:{color:"red","margin-left":"4px"}},[t._v("√")]):t._e()],1)})),1)],1)]:a("v-uni-text",[t._v(t._s(e.title))])],2)],1)})),1),a("v-uni-view",{staticClass:"tbody"},[t._l(t.list,(function(e,i){return t.list.length?a("v-uni-view",{key:i,staticClass:"tr"},t._l(t.cols,(function(n,o){return a("v-uni-view",{key:o,staticClass:"td",style:{flex:n.width,maxWidth:n.width+"%",textAlign:n.align?n.align:"center"}},[a("v-uni-view",{staticClass:"content",class:{action:n.action}},[n.checkbox?a("v-uni-view",["function"==typeof n.disabled&&n.disabled(e)?a("v-uni-text",{staticClass:"iconfont iconfuxuankuang2 disabled"}):a("v-uni-text",{staticClass:"iconfont",class:{iconfuxuankuang2:0==Boolean(t.selectedData[e[t.pk]]),iconfuxuankuang1:1==Boolean(t.selectedData[e[t.pk]])},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.single(e,i)}}})],1):n.action?t._t("action",null,{value:e,index:i}):n.templet?a("v-uni-view",{domProps:{innerHTML:t._s(n.templet(e))}}):n.return?a("v-uni-view",[t._v(t._s(n.return(e)))]):a("v-uni-view",[t._v(t._s(e[n.field]))])],2)],1)})),1):t._e()})),t.list.length?t._e():a("v-uni-view",{staticClass:"tr empty"},[a("v-uni-view",{staticClass:"td"},[a("v-uni-view",{staticClass:"iconfont iconwushuju"}),a("v-uni-view",[t._v("暂无数据")])],1)],1)],2),t.list.length&&0==t.classType?a("v-uni-view",{staticClass:"tpage"},[a("v-uni-view",{staticClass:"batch-action"},[t._t("batchaction",null,{value:t.selected})],2),a("uni-pagination",{attrs:{total:t.total,showIcon:!0,pageSize:t.pagesize,value:t.page},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.pageChange.apply(void 0,arguments)}}})],1):t._e()],1)},o=[]},4173:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("dc8a"),a("22b6"),a("bf0f"),a("2797");var i={name:"uniDataTableNew",props:{cols:{type:Array},url:{type:String,default:""},pagesize:{type:Number,default:10},option:{type:Object,default:function(){return{}}},classType:{type:Boolean,default:!1},data:{type:Object|Array,default:function(){return{}}},pk:{type:String,default:""}},created:function(){this.url&&this.load({page:1})},data:function(){return{list:[],selected:[],selectedIndex:[],selectedData:{},unselectedData:{},page:1,total:0,pageCount:0,allSelect:{optionList:[{id:"curr",name:"当前页"},{id:"all",name:"所有页"}],show:!1,num:0,hoverOption:"",selected:!1,pageSelected:{}}}},watch:{data:{handler:function(t,e){Object.keys(t).length&&(this.list=Object.values(t))},deep:!0,immediate:!0}},methods:{allSelectShow:function(){this.allSelect.show=!0},allSelectHide:function(){this.allSelect.show=!1,this.allSelect.hoverOption=""},allSelectInitData:function(){if(this.allSelect.pageSelected={},this.allSelect.selected=!1,this.pageCount>0)for(var t=1;t<this.pageCount;t++)this.allSelect.pageSelected[t]=!1;this.selectedData={},this.unselectedData={},this.allSelect.num=0},allSelectClick:function(t){if("curr"==t.id)this.allSelect.pageSelected[this.page]=!this.allSelect.pageSelected[this.page];else{for(var e in this.allSelect.selected=!this.allSelect.selected,this.allSelect.pageSelected)this.allSelect.pageSelected[e]=this.allSelect.selected;this.allSelect.selected||(this.selectedData={},this.unselectedData={})}this.handleSelectData(),this.allSelectNum(),this.allSelectHide()},allSelectNum:function(){this.allSelect.selected?this.allSelect.num=this.total-Object.values(this.unselectedData).length:this.allSelect.num=Object.values(this.selectedData).length},handleSelectData:function(){var t=this;if(this.list.length){var e=this.cols[0];this.allSelect.pageSelected[this.page]?this.list.forEach((function(a){"function"==typeof e.disabled&&e.disabled(a)||(t.selectedData[a[t.pk]]||(t.selectedData[a[t.pk]]=a),t.unselectedData[a[t.pk]]&&delete t.unselectedData[a[t.pk]])})):this.list.forEach((function(a){"function"==typeof e.disabled&&e.disabled(a)||(t.unselectedData[a[t.pk]]||(t.unselectedData[a[t.pk]]=a),t.selectedData[a[t.pk]]&&delete t.selectedData[a[t.pk]])}))}},single:function(t,e){this.selectedData[t[this.pk]]?delete this.selectedData[t[this.pk]]:this.selectedData[t[this.pk]]=t,this.unselectedData[t[this.pk]]?delete this.unselectedData[t[this.pk]]:this.unselectedData[t[this.pk]]=t,this.allSelectNum()},defaultSelectData:function(t,e){this.selected=t,this.selectedIndex=e},pageChange:function(t){this.page=t.current,this.load(),this.$emit("pageChange",this.page)},load:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a={page:e.page||this.page,page_size:this.pagesize};this.option&&Object.assign(a,this.option),e&&Object.assign(a,e),this.$api.sendRequest({url:this.url,data:a,success:function(a){a.code>=0?(t.list=a.data.list,t.total=a.data.count,t.pageCount=a.data.page_count,t.selected=[],t.selectedIndex=[],t.$emit("tableData",t.list),e.page&&(t.page=e.page,1==e.page&&t.allSelectInitData(),delete e.page),t.handleSelectData()):t.$util.showToast({title:a.message})},fail:function(){t.$util.showToast({title:"请求失败"})}})},clearCheck:function(){this.allSelectInitData()},getSelectData:function(){return{selectedData:this.selectedData,unselectedData:this.unselectedData,allSelected:this.allSelect.selected,selectedNum:this.allSelect.num}}}};e.default=i},4361:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-0812e34e]{display:none}\r\n/* 收银台相关 */uni-text[data-v-0812e34e],\r\nuni-view[data-v-0812e34e]{font-size:.14rem}body[data-v-0812e34e]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-0812e34e]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-0812e34e]::-webkit-scrollbar-button{display:none}body[data-v-0812e34e]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-0812e34e]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-0812e34e]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-0812e34e]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-0812e34e]{color:var(--primary-color)!important}.uni-pagination[data-v-0812e34e]{display:flex;position:relative;overflow:hidden;flex-direction:row;justify-content:center;align-items:center}.uni-pagination__total[data-v-0812e34e]{font-size:.14rem;color:#999;margin-right:.15rem}.uni-pagination__btn[data-v-0812e34e]{display:flex;cursor:pointer;padding:0 .08rem;line-height:.3rem;font-size:.14rem;position:relative;background-color:#f0f0f0;flex-direction:row;justify-content:center;align-items:center;text-align:center;border-radius:.05rem}.uni-pagination__child-btn[data-v-0812e34e]{display:flex;position:relative;flex-direction:row;justify-content:center;align-items:center;text-align:center;color:#0f1214;font-size:.12rem}.uni-pagination__num[data-v-0812e34e]{display:flex;flex:1;flex-direction:row;justify-content:center;align-items:center;height:.3rem;line-height:.3rem;font-size:.14rem;color:#333;margin:0 .05rem}.uni-pagination__num-tag[data-v-0812e34e]{cursor:pointer;min-width:.3rem;margin:0 .05rem;height:.3rem;text-align:center;line-height:.3rem;color:#666;border-radius:.04rem}.uni-pagination__num-current[data-v-0812e34e]{display:flex;flex-direction:row}.uni-pagination__num-current-text[data-v-0812e34e]{font-size:.15rem}.uni-pagination--enabled[data-v-0812e34e]{color:#333;opacity:1}.uni-pagination--disabled[data-v-0812e34e]{opacity:.5;cursor:default}.uni-pagination--hover[data-v-0812e34e]{color:rgba(0,0,0,.6);background-color:#f1f1f1}.tag--active[data-v-0812e34e]:hover{color:var(--primary-color)}.page--active[data-v-0812e34e]{color:#fff;background-color:var(--primary-color)}.page--active[data-v-0812e34e]:hover{color:#fff}.is-pc-hide[data-v-0812e34e]{display:block}.is-phone-hide[data-v-0812e34e]{display:none}@media screen and (min-width:450px){.is-pc-hide[data-v-0812e34e]{display:none}.is-phone-hide[data-v-0812e34e]{display:block}.uni-pagination__num-flex-none[data-v-0812e34e]{flex:none}}',""]),t.exports=e},4547:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-pagination"},[a("v-uni-view",{staticClass:"uni-pagination__total is-phone-hide"},[t._v("共 "+t._s(t.total)+" 条")]),a("v-uni-view",{staticClass:"uni-pagination__btn",class:1===t.currentIndex?"uni-pagination--disabled":"uni-pagination--enabled",attrs:{"hover-class":1===t.currentIndex?"":"uni-pagination--hover","hover-start-time":20,"hover-stay-time":70},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickLeft.apply(void 0,arguments)}}},[!0===t.showIcon||"true"===t.showIcon?[a("v-uni-text",{staticClass:"iconfont iconqianhou1"})]:[a("v-uni-text",{staticClass:"uni-pagination__child-btn"},[t._v(t._s(t.prevPageText))])]],2),a("v-uni-view",{staticClass:"uni-pagination__num uni-pagination__num-flex-none"},[a("v-uni-view",{staticClass:"uni-pagination__num-current"},[a("v-uni-text",{staticClass:"uni-pagination__num-current-text is-pc-hide text-color"},[t._v(t._s(t.currentIndex))]),a("v-uni-text",{staticClass:"uni-pagination__num-current-text is-pc-hide"},[t._v("/"+t._s(t.maxPage||0))]),t._l(t.paper,(function(e,i){return a("v-uni-view",{key:i,staticClass:"uni-pagination__num-tag tag--active is-phone-hide",class:{"page--active":e===t.currentIndex},on:{click:function(a){if(!a.type.indexOf("key")&&t._k(a.keyCode,"top",void 0,a.key,void 0))return null;arguments[0]=a=t.$handleEvent(a),t.selectPage(e,i)}}},[a("v-uni-text",[t._v(t._s(e))])],1)}))],2)],1),a("v-uni-view",{staticClass:"uni-pagination__btn",class:t.currentIndex>=t.maxPage?"uni-pagination--disabled":"uni-pagination--enabled",attrs:{"hover-class":t.currentIndex===t.maxPage?"":"uni-pagination--hover","hover-start-time":20,"hover-stay-time":70},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickRight.apply(void 0,arguments)}}},[!0===t.showIcon||"true"===t.showIcon?[a("v-uni-text",{staticClass:"iconfont iconqianhou2"})]:[a("v-uni-text",{staticClass:"uni-pagination__child-btn"},[t._v(t._s(t.nextPageText))])]],2)],1)},n=[]},"48fc":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-3387bb78]{display:none}\r\n/* 收银台相关 */uni-text[data-v-3387bb78],\r\nuni-view[data-v-3387bb78]{font-size:.14rem}body[data-v-3387bb78]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-3387bb78]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-3387bb78]::-webkit-scrollbar-button{display:none}body[data-v-3387bb78]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-3387bb78]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-3387bb78]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-3387bb78]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-3387bb78]{color:var(--primary-color)!important}.uni-select-lay[data-v-3387bb78]{position:relative;z-index:999;box-sizing:border-box}.uni-select-lay .uni-select-input[data-v-3387bb78]{opacity:0;position:absolute;z-index:-111}.uni-select-lay .uni-select-lay-select[data-v-3387bb78]{-webkit-user-select:none;user-select:none;position:relative;z-index:3;height:.32rem;padding:0 .3rem 0 .1rem;box-sizing:border-box;border-radius:.02rem;border:.01rem solid #e5e5e5;display:flex;align-items:center;font-size:.14rem;color:#999}.uni-select-lay .uni-select-lay-select .uni-disabled[data-v-3387bb78]{position:absolute;left:0;width:100%;height:100%;z-index:19;cursor:no-drop;background:hsla(0,0%,100%,.5)}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close[data-v-3387bb78]{position:absolute;right:.35rem;top:0;height:100%;width:.15rem;display:flex;align-items:center;justify-content:center;z-index:3;cursor:pointer}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]{position:relative;background:#fff;width:.13rem;height:.13rem;border-radius:50%;border:.01rem solid #bbb}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::before, .uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::after{content:"";position:absolute;left:20%;top:50%;height:.01rem;width:60%;-webkit-transform:rotate(45deg);transform:rotate(45deg);background-color:#bbb}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::after{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.uni-select-lay .uni-select-lay-select .uni-select-lay-input[data-v-3387bb78]{font-size:.14rem;color:#999;display:block;width:98%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:.3rem;box-sizing:border-box}.uni-select-lay .uni-select-lay-select .uni-select-lay-input.active[data-v-3387bb78]{color:#333}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon[data-v-3387bb78]{cursor:pointer;position:absolute;right:0;top:0;height:100%;width:.3rem;display:flex;align-items:center;justify-content:center}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon[data-v-3387bb78]::before{content:"";width:.01rem;height:100%;position:absolute;left:0;top:0;background-color:#e5e5e5}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon uni-text[data-v-3387bb78]{display:block;width:0;height:0;border-width:.07rem .07rem 0;border-style:solid;border-color:#bbb transparent transparent;transition:.3s}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled[data-v-3387bb78]{cursor:no-drop}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled uni-text[data-v-3387bb78]{width:.2rem;height:.2rem;border:.02rem solid red;border-radius:50%;transition:.3s;position:relative;z-index:999}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled uni-text[data-v-3387bb78]::after{content:"";position:absolute;top:50%;left:0;width:100%;height:.02rem;margin-top:-.01rem;background-color:red;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.uni-select-lay .uni-select-lay-select.active .uni-select-lay-icon uni-text[data-v-3387bb78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.uni-select-lay .uni-select-lay-options[data-v-3387bb78]{-webkit-user-select:none;user-select:none;position:absolute;top:calc(100% + .05rem);left:0;width:100%;max-height:2.5rem;border-radius:.02rem;border:1px solid #e5e5e5;background:#fff;padding:.05rem 0;box-sizing:border-box;z-index:9}.uni-select-lay .uni-select-lay-options .uni-select-lay-item[data-v-3387bb78]{padding:0 .1rem;box-sizing:border-box;cursor:pointer;line-height:2.5;transition:.3s;font-size:.14rem}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.active[data-v-3387bb78]{background:var(--primary-color);color:#fff}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.active[data-v-3387bb78]:hover{background:var(--primary-color);color:#fff}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.disabled[data-v-3387bb78]{color:#999;cursor:not-allowed}.uni-select-lay .uni-select-lay-options .uni-select-lay-item[data-v-3387bb78]:hover{background-color:#f5f5f5}.uni-select-lay .uni-select-lay-options .nosearch[data-v-3387bb78]{font-size:.16rem;line-height:3;text-align:center;color:#666}.uni-date-mask[data-v-3387bb78]{position:fixed;bottom:0;top:0;left:0;right:0;background-color:transparent;transition-duration:.3s;z-index:8}',""]),t.exports=e},"53f4":function(t,e,a){"use strict";a.r(e);var i=a("6c5f"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"54b6":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.editGoods=function(t){return n.default.post("/cashier/storeapi/goods/editgoods",{data:t})},e.exportPrintPriceTagData=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.default.post("/cashier/storeapi/goods/exportPrintPriceTagData",{data:t})},e.getElectronicScaleInformation=function(){return n.default.post("/scale/storeapi/scale/cashierscale")},e.getGoodsCategory=function(t){return n.default.post("/cashier/storeapi/goods/category",{data:t})},e.getGoodsDetail=function(t){return n.default.post("/cashier/storeapi/goods/detail",{data:{goods_id:t}})},e.getGoodsInfoByCode=function(t){return n.default.post("/cashier/storeapi/goods/skuinfo",{data:{sku_no:t}})},e.getGoodsList=function(t){return n.default.post("/cashier/storeapi/goods/page",{data:t})},e.getGoodsSceen=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.default.post("/cashier/storeapi/goods/screen",{data:t})},e.getGoodsSkuList=function(t){return n.default.post("/cashier/storeapi/goods/skulist",{data:{goods_id:t}})},e.getManageGoodsCategory=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.default.post("/stock/storeapi/manage/getGoodsCategory",{data:t})},e.getServiceCategory=function(t){return n.default.post("/cashier/storeapi/service/category",{data:t})},e.getServiceList=function(t){return n.default.post("/cashier/storeapi/service/page",{data:t})},e.getSkuListBySelect=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.default.post("/cashier/storeapi/goods/getSkuListBySelect",{data:t})},e.setGoodsLocalRestrictions=function(t){return n.default.post("/cashier/storeapi/goods/setGoodsLocalRestrictions",{data:t})},e.setGoodsStatus=function(t){return n.default.post("/cashier/storeapi/goods/setstatus",{data:t})};var n=i(a("4e01"))},"5a63":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("aa9c"),a("bf0f"),a("e966");var i={name:"UniPagination",emits:["update:modelValue","input","change"],props:{value:{type:[Number,String],default:1},modelValue:{type:[Number,String],default:1},prevText:{type:String},nextText:{type:String},current:{type:[Number,String],default:1},total:{type:[Number,String],default:0},pageSize:{type:[Number,String],default:10},showIcon:{type:[Boolean,String],default:!1},pagerCount:{type:Number,default:7}},data:function(){return{currentIndex:1,paperData:[]}},computed:{prevPageText:function(){return this.prevText||"上一页"},nextPageText:function(){return this.nextText||"下一页"},maxPage:function(){var t=1,e=Number(this.total),a=Number(this.pageSize);return e&&a&&(t=Math.ceil(e/a)),t},paper:function(){for(var t=this.currentIndex,e=this.pagerCount,a=this.total,i=this.pageSize,n=[],o=[],s=Math.ceil(a/i),l=0;l<s;l++)n.push(l+1);o.push(1);var c=n[n.length-(e+1)/2];return n.forEach((function(a,i){(e+1)/2>=t?a<e+1&&a>1&&o.push(a):t+2<=c?a>t-(e+1)/2&&a<t+(e+1)/2&&o.push(a):(a>t-(e+1)/2||s-e<a)&&a<n[n.length-1]&&o.push(a)})),s>e?((e+1)/2>=t?o[o.length-1]="...":t+2<=c?(o[1]="...",o[o.length-1]="..."):o[1]="...",o.push(n[n.length-1])):(e+1)/2>=t||t+2<=c||(o.shift(),o.push(n[n.length-1])),o}},watch:{current:{immediate:!0,handler:function(t,e){this.currentIndex=t<1?1:t}},value:{immediate:!0,handler:function(t){1===Number(this.current)&&(this.currentIndex=t<1?1:t)}}},methods:{selectPage:function(t,e){if(parseInt(t))this.currentIndex=t,this.change("current");else{var a=Math.ceil(this.total/this.pageSize);if(e<=1)return void(this.currentIndex-5>1?this.currentIndex-=5:this.currentIndex=1);e>=6&&(this.currentIndex+5>a?this.currentIndex=a:this.currentIndex+=5)}},clickLeft:function(){1!==Number(this.currentIndex)&&(this.currentIndex-=1,this.change("prev"))},clickRight:function(){Number(this.currentIndex)>=this.maxPage||(this.currentIndex+=1,this.change("next"))},change:function(t){this.$emit("input",this.currentIndex),this.$emit("update:modelValue",this.currentIndex),this.$emit("change",{type:t,current:this.currentIndex})}}};e.default=i},"5b74":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={selectLay:a("3523").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("unipopup",{ref:"dialogRef",attrs:{type:"center",maskClick:!1}},[a("v-uni-view",{staticClass:"stock-dialog-wrap"},[a("v-uni-view",{staticClass:"stock-dialog-head"},[a("v-uni-text",[t._v("商品选择")]),a("v-uni-text",{staticClass:"iconfont iconguanbi1",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$emit("change",!1)}}})],1),a("v-uni-view",{staticClass:"stock-dialog-body"},[a("v-uni-view",{staticClass:"tree"},[a("v-uni-scroll-view",{staticClass:"list-wrap",attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"item",class:{active:""===t.option.category_id},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.itemClick({category_id:"",child_num:0})}}},[a("v-uni-view",{staticClass:"icon"}),a("v-uni-view",[t._v("全部分类")])],1),t._l(t.goodsCategoryList,(function(e,i){return a("v-uni-view",{key:i},[a("v-uni-view",{staticClass:"item",class:{active:t.option.category_id===e.category_id},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.itemClick(e)}}},[a("v-uni-view",{staticClass:"icon",class:{active:-1!=t.activeList.indexOf(e.category_id)}},[e.child_num?a("v-uni-text",{staticClass:"iconfont iconsanjiao_xia"}):t._e()],1),a("v-uni-view",[t._v(t._s(e.title))])],1),e.child_num?t._l(e.children,(function(i,n){return a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:-1!=t.activeList.indexOf(e.category_id),expression:"activeList.indexOf(item.category_id) != -1"}],key:n,staticClass:"level"},[a("v-uni-view",{staticClass:"item",class:{active:t.option.category_id===i.category_id},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.itemClick(i)}}},[a("v-uni-view",{staticClass:"icon",class:{active:-1!=t.activeList.indexOf(i.category_id)}},[i.child_num?a("v-uni-text",{staticClass:"iconfont iconsanjiao_xia"}):t._e()],1),a("v-uni-view",[t._v(t._s(i.title))])],1),t._l(i.children,(function(e,n){return a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:-1!=t.activeList.indexOf(i.category_id),expression:"activeList.indexOf(item2.category_id) != -1"}],key:n,staticClass:"level"},[a("v-uni-view",{staticClass:"item item2",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.itemClick(e)}}},[a("v-uni-view",{staticClass:"icon"}),a("v-uni-view",[t._v(t._s(e.title))])],1)],1)}))],2)})):t._e()],2)}))],2)],1),a("v-uni-view",{staticClass:"stock-dialog-table"},[a("v-uni-view",{staticClass:"search  common-form"},[a("v-uni-view",{staticClass:"common-form-item"},[1==t.isInstallSupply?a("v-uni-view",{staticClass:"form-input-inline"},[a("select-lay",{attrs:{zindex:10,value:t.option.supplier_id,name:"supplier_id",placeholder:"请选择供应商",options:t.supplierList},on:{selectitem:function(e){arguments[0]=e=t.$handleEvent(e),t.selectSupplier.apply(void 0,arguments)}}})],1):t._e(),a("v-uni-view",{staticClass:"form-input-inline"},[a("select-lay",{attrs:{zindex:10,value:t.option.brand_id,name:"brand_id",placeholder:"请选择品牌",options:t.brandList},on:{selectitem:function(e){arguments[0]=e=t.$handleEvent(e),t.selectBrand.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("select-lay",{attrs:{zindex:10,value:t.option.goods_class,name:"goods_class",placeholder:"请选择类型",options:t.goodsClassList},on:{selectitem:function(e){arguments[0]=e=t.$handleEvent(e),t.selectGoodsClass.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入名称/编码"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.getStoreGoods.apply(void 0,arguments)}},model:{value:t.option.search_text,callback:function(e){t.$set(t.option,"search_text",e)},expression:"option.search_text"}})],1)],1),a("v-uni-view",{staticClass:"form-inline common-btn-wrap"},[a("v-uni-button",{staticClass:"screen-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getStoreGoods.apply(void 0,arguments)}}},[t._v("筛选")])],1)],1)],1),a("uniDataTable",{ref:"goodsListTable",staticClass:"goods-table",attrs:{pk:"sku_id",url:t.url,option:t.option,cols:t.cols,pagesize:8}})],1)],1),a("v-uni-view",{staticClass:"btn"},[a("v-uni-button",{staticClass:"primary-btn submit",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submit("close")}}},[t._v("选中")]),a("v-uni-button",{staticClass:"default-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$emit("change",!1)}}},[t._v("取消")])],1)],1)],1)},o=[]},"5e0a":function(t,e,a){"use strict";a.r(e);var i=a("5b74"),n=a("80ca");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("3d30");var s=a("828b"),l=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"c0c460c6",null,!1,i["a"],void 0);e["default"]=l.exports},6537:function(t,e,a){var i=a("777f");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("7650f312",i,!0,{sourceMap:!1,shadowMode:!1})},6729:function(t,e,a){"use strict";var i=a("d892"),n=a.n(i);n.a},"6c5f":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("bf0f"),a("2797"),a("8f71"),a("4626"),a("5ac7");var i={name:"select-lay",props:{disabled:{type:Boolean,default:!1},zindex:{type:Number,default:999},options:{type:Array,default:function(){return[]}},name:{type:String,default:""},value:{type:[String,Number],default:""},placeholder:{type:String,default:"请选择"},showplaceholder:{type:Boolean,default:!0},slabel:{type:String,default:"label"},svalue:{type:String,default:"value"}},data:function(){return{active:!1,isfocus:!1,isremove:!1,ismove:!1,changevalue:"",oldvalue:"",changes:!1,changesValue:"",vlist:[],settimer:null}},mounted:function(){this.itemcheck()},watch:{value:function(){this.itemcheck()},options:function(){this.itemcheck()}},methods:{itemcheck:function(){var t=this;""!=this.value?this.options.length>0&&this.options.forEach((function(e){t.value!=e[t.svalue]||(t.oldvalue=t.changevalue=e[t.slabel])})):this.oldvalue=this.changevalue=""},select:function(){this.disabled||(this.active=!this.active,this.active?this.changes=!1:this.changevalue=this.oldvalue)},unifocus:function(){this.disabled||(this.active=!0,this.changes=!1,this.isfocus=!0)},uniblur:function(){var t=this;this.isfocus=!1,setTimeout((function(){t.isremove||t.ismove?(t.isremove=!1,t.ismove=!1):(t.changevalue=t.oldvalue,t.isremove=!1,t.active=!1)}),153)},movetouch:function(){var t=this;setTimeout((function(){t.isfocus?t.ismove=!1:t.ismove||(t.ismove=!0)}),100)},selectmove:function(){var t=this;setTimeout((function(){t.isfocus?t.ismove=!1:t.ismove||(t.ismove=!0)}),100)},removevalue:function(){this.isremove=!0,this.changes=!1,this.changevalue=""},intchange:function(){var t=this;""!=this.changevalue?this.oldvalue!=this.changevalue&&(this.vlist=[],this.changes=!0,this.changesValue="正在搜索...",this.settimer&&clearTimeout(this.settimer),this.settimer=setTimeout((function(){t.vlist=t.options.filter((function(e){return e[t.slabel].includes(t.changevalue)})),0===t.vlist.length&&(t.changesValue="暂无匹配内容！")}),600)):this.changes=!1},selectitem:function(t,e){if(e&&e.disabled)return!1;this.changevalue=this.oldvalue,this.active=!1,this.$emit("selectitem",t,e)}}};e.default=i},"6e0b":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-select-lay",style:{"z-index":t.zindex}},[a("v-uni-input",{staticClass:"uni-select-input",attrs:{type:"text",name:t.name,readonly:!0},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}}),a("v-uni-view",{staticClass:"uni-select-lay-select",class:{active:t.active}},[t.disabled?a("v-uni-view",{staticClass:"uni-disabled"}):t._e(),""!=t.changevalue&&this.active?a("v-uni-view",{staticClass:"uni-select-lay-input-close"},[a("v-uni-text",{on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.removevalue.apply(void 0,arguments)}}})],1):t._e(),a("v-uni-input",{staticClass:"uni-select-lay-input",class:{active:""!=t.changevalue&&t.changevalue!=t.placeholder},attrs:{type:"text",readonly:!0,disabled:"true",placeholder:t.placeholder},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.unifocus.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.intchange.apply(void 0,arguments)},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.uniblur.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.select.apply(void 0,arguments)}},model:{value:t.changevalue,callback:function(e){t.changevalue=e},expression:"changevalue"}}),a("v-uni-view",{staticClass:"uni-select-lay-icon",class:{disabled:t.disabled},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.select.apply(void 0,arguments)}}},[a("v-uni-text")],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.active,expression:"active"}],staticClass:"uni-date-mask",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.select.apply(void 0,arguments)}}}),a("v-uni-scroll-view",{directives:[{name:"show",rawName:"v-show",value:t.active,expression:"active"}],staticClass:"uni-select-lay-options",attrs:{"scroll-y":!0},on:{scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.selectmove.apply(void 0,arguments)},touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.movetouch.apply(void 0,arguments)}}},[t.changes?[t.vlist.length>0?t._l(t.vlist,(function(e,i){return a("v-uni-view",{key:i,staticClass:"uni-select-lay-item",class:{active:t.value==e[t.svalue]},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.selectitem(i,e)}}},[t._v(t._s(e[t.slabel]))])})):[a("v-uni-view",{staticClass:"nosearch"},[t._v(t._s(t.changesValue))])]]:[t.showplaceholder?a("v-uni-view",{staticClass:"uni-select-lay-item",class:{active:""==t.value},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.selectitem(-1,null)}}},[t._v(t._s(t.placeholder))]):t._e(),t._l(t.options,(function(e,i){return a("v-uni-view",{key:i,staticClass:"uni-select-lay-item",class:{active:t.value==e[t.svalue],disabled:e.disabled},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.selectitem(i,e)}}},[t._v(t._s(e[t.slabel]))])}))]],2)],1)},n=[]},"777f":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-54426f41]{display:none}\r\n/* 收银台相关 */uni-text[data-v-54426f41],\r\nuni-view[data-v-54426f41]{font-size:.14rem}body[data-v-54426f41]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-54426f41]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-54426f41]::-webkit-scrollbar-button{display:none}body[data-v-54426f41]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-54426f41]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-54426f41]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-54426f41]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-54426f41]{color:var(--primary-color)!important}.table-container[data-v-54426f41]{width:100%}.table-container .iconcheckbox_weiquanxuan[data-v-54426f41],\r\n.table-container .iconfuxuankuang1[data-v-54426f41],\r\n.table-container .iconfuxuankuang2[data-v-54426f41]{color:var(--primary-color);cursor:pointer;font-size:.16rem;transition:all .3s}.table-container .iconfuxuankuang2[data-v-54426f41]{color:#e6e6e6}.table-container .iconfuxuankuang2[data-v-54426f41]:hover{color:var(--primary-color)}.table-container .disabled[data-v-54426f41]{background:#eee;cursor:not-allowed}.table-container .disabled[data-v-54426f41]:hover{color:#e6e6e6}.thead[data-v-54426f41]{display:flex;width:100%;height:.5rem;background:#f7f8fa;align-items:center}.thead .th[data-v-54426f41]{padding:0 .1rem;box-sizing:border-box}.thead .th .content[data-v-54426f41]{white-space:nowrap;width:100%;\r\n  /* overflow: hidden; */text-overflow:ellipsis}.thead .th .content .all-select[data-v-54426f41]{position:relative}.thead .th .content .all-select .all-select-label[data-v-54426f41]{cursor:pointer;padding:4px}.thead .th .content .all-select .all-select-option[data-v-54426f41]{position:absolute;display:none;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);background:#fff;padding:4px 0;border-radius:2px;box-shadow:0 0 2px #ccc}.thead .th .content .all-select .all-select-option uni-view[data-v-54426f41]{color:#000;cursor:pointer;margin:4px 0;padding:4px 20px}.thead .th .content .all-select .all-select-option uni-view.on[data-v-54426f41]{background:#eee}.tr[data-v-54426f41]{display:flex;border-bottom:.01rem solid #e6e6e6;min-height:.5rem;align-items:center;transition:background-color .3s;padding:.1rem 0;box-sizing:border-box}.tr[data-v-54426f41]:hover{background:#f5f5f5}.tr .td[data-v-54426f41]{padding:0 .1rem;box-sizing:border-box}.tr .td .content[data-v-54426f41]{width:100%;white-space:normal}.tr.empty[data-v-54426f41]{justify-content:center}.tr.empty .td[data-v-54426f41]{text-align:center;color:#909399}.tr.empty .td .iconfont[data-v-54426f41]{font-size:.25rem;margin:.05rem}.tpage[data-v-54426f41]{display:flex;align-items:center;padding:.1rem 0;margin-bottom:.1rem}.tpage .uni-pagination[data-v-54426f41]{justify-content:flex-end;flex:1}',""]),t.exports=e},8076:function(t,e,a){"use strict";a.r(e);var i=a("914d"),n=a("80b7");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("6729");var s=a("828b"),l=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"3f6572c3",null,!1,i["a"],void 0);e["default"]=l.exports},"80b7":function(t,e,a){"use strict";a.r(e);var i=a("b538"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"80ca":function(t,e,a){"use strict";a.r(e);var i=a("eba9"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"8b40":function(t,e,a){var i=a("4361");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("ba13b706",i,!0,{sourceMap:!1,shadowMode:!1})},"8df1":function(t,e,a){"use strict";var i=a("8b40"),n=a.n(i);n.a},"914d":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("base-page",[a("v-uni-view",{staticClass:"stock-body"},[a("v-uni-view",{staticClass:"content-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goodsShow=!1}}},[a("v-uni-view",{staticClass:"title-back flex items-center cursor-pointer",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.backFn.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"iconfont iconqianhou1"}),a("v-uni-text",{staticClass:"left"},[t._v("返回")]),a("v-uni-text",{staticClass:"content"},[t._v("|")]),a("v-uni-text",[t._v("打印价格标签")])],1),0==t.editPrintNum.show?a("v-uni-view",{staticClass:"batch-action"},[a("v-uni-text",{staticClass:"batch-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openSelectGoodsDialog()}}},[t._v("选择商品")]),a("v-uni-text",{staticClass:"batch-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.batchDeleteGoods()}}},[t._v("批量删除")])],1):t._e(),1==t.editPrintNum.show?a("v-uni-view",{staticClass:"screen-warp common-form"},[a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"digit",placeholder:"请输入打印份数"},model:{value:t.editPrintNum.value,callback:function(e){t.$set(t.editPrintNum,"value",e)},expression:"editPrintNum.value"}})],1)],1),a("v-uni-view",{staticClass:"form-inline common-btn-wrap"},[a("v-uni-button",{staticClass:"screen-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editPrintNumConfirm.apply(void 0,arguments)}}},[t._v("确定")]),a("v-uni-button",{attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editPrintNum.show=!1}}},[t._v("取消")])],1)],1)],1):t._e(),a("v-uni-view",{staticClass:"table-wrap"},[a("v-uni-view",{staticClass:"table-head"},[a("v-uni-view",{staticClass:"table-tr"},[a("v-uni-view",{staticClass:"table-th"},[a("v-uni-text",{staticClass:"iconfont",class:!0===t.allSelected?t.selectedIcon:"harf"==t.allSelected?t.harfselectedIcon:t.unselectedIcon,on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeGoodsAllSelected()}}})],1),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"3"}},[t._v("商品名称")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("条码")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("划线价")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("售价")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("单位")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("重量")])],1)],1),a("v-uni-view",{staticClass:"table-body"},[t._l(t.goodsList,(function(e,i){return[a("v-uni-view",{key:i+"_0",staticClass:"table-tr"},[a("v-uni-view",{staticClass:"table-td"},[a("v-uni-text",{staticClass:"iconfont",class:e.selected?t.selectedIcon:t.unselectedIcon,on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeGoodsSelected(i)}}})],1),a("v-uni-view",{staticClass:"table-td goods-name",staticStyle:{flex:"3"}},[t._v(t._s(e.sku_name))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[t._v(t._s(e.sku_no))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[t._v(t._s(e.market_price))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[t._v(t._s(e.price))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[t._v(t._s(e.unit))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[t._v(t._s(e.weight))])],1)]})),t.goodsList.length?t._e():a("v-uni-view",{staticClass:"table-tr table-empty"},[t._v("暂无数据，请选择商品数据")])],2)],1)],1),a("v-uni-view",{staticClass:"action-wrap"},[a("v-uni-view",{staticClass:"table-total"},[t._v("合计：共 "+t._s(t.goodsList.length)+" 种商品")]),a("v-uni-view",{staticClass:"btn-wrap"},[a("v-uni-button",{staticClass:"stockout-btn",attrs:{type:"default",loading:t.isSubmit},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.designFn.apply(void 0,arguments)}}},[t._v("设计模板")]),a("v-uni-button",{staticClass:"stockout-btn",attrs:{type:"default",loading:t.isSubmit},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.printFn.apply(void 0,arguments)}}},[t._v("打印")]),a("v-uni-button",{staticClass:"stockout-btn",attrs:{type:"default",loading:t.isSubmit},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.exportFn.apply(void 0,arguments)}}},[t._v("导出")])],1)],1)],1),a("goods-sku-select",{attrs:{params:t.dialogParams,apiType:"sku",goodsClass:[1,6]},on:{selectGoods:function(e){arguments[0]=e=t.$handleEvent(e),t.selectGoodsComplete.apply(void 0,arguments)}},model:{value:t.dialogVisible,callback:function(e){t.dialogVisible=e},expression:"dialogVisible"}})],1)},n=[]},"988f":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-3f6572c3]{display:none}\r\n/* 收银台相关 */uni-text[data-v-3f6572c3],\r\nuni-view[data-v-3f6572c3]{font-size:.14rem}body[data-v-3f6572c3]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-3f6572c3]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-3f6572c3]::-webkit-scrollbar-button{display:none}body[data-v-3f6572c3]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-3f6572c3]::-webkit-scrollbar-track{background-color:initial}.stock-body .content-wrap[data-v-3f6572c3], .stock-body .content-wrap .table-wrap .table-body[data-v-3f6572c3]{height:100%;overflow:auto}.stock-body .content-wrap[data-v-3f6572c3]::-webkit-scrollbar, .stock-body .content-wrap .table-wrap .table-body[data-v-3f6572c3]::-webkit-scrollbar{width:.06rem;height:.06rem}.stock-body .content-wrap[data-v-3f6572c3]::-webkit-scrollbar-button, .stock-body .content-wrap .table-wrap .table-body[data-v-3f6572c3]::-webkit-scrollbar-button{display:none}.stock-body .content-wrap[data-v-3f6572c3]::-webkit-scrollbar-thumb, .stock-body .content-wrap .table-wrap .table-body[data-v-3f6572c3]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}.stock-body .content-wrap[data-v-3f6572c3]::-webkit-scrollbar-track, .stock-body .content-wrap .table-wrap .table-body[data-v-3f6572c3]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-3f6572c3]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-3f6572c3]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-3f6572c3]{color:var(--primary-color)!important}.form-content[data-v-3f6572c3]{display:flex;flex-wrap:wrap;margin-top:.2rem}.form-content .store-info .form-inline[data-v-3f6572c3]{padding-left:.05rem}.form-content .form-item[data-v-3f6572c3]{margin-bottom:.1rem;display:flex}.form-content .form-item .form-label[data-v-3f6572c3]{width:1.3rem;text-align:right;padding-right:.1rem;box-sizing:border-box;height:.32rem;line-height:.32rem}.form-content .form-item .form-label .required[data-v-3f6572c3]{color:red;margin-right:.03rem}.form-content .form-item .form-inline[data-v-3f6572c3]{width:2.4rem;line-height:.32rem;margin-right:.1rem;box-sizing:border-box}.form-content .form-item .form-inline.input uni-input[data-v-3f6572c3]{padding:0 .1rem}.form-content .form-item .form-inline .form-input[data-v-3f6572c3]{border-width:.01rem;border-style:solid;background-color:#fff;color:rgba(0,0,0,.85);border-radius:.02rem;padding-left:.1rem;height:.32rem;line-height:.32rem;font-size:.14rem;border-color:#e6e6e6}.stock-body[data-v-3f6572c3]{position:relative;height:100%}.stock-body .common-form .common-btn-wrap[data-v-3f6572c3]{margin-left:0}.stock-body .content-wrap[data-v-3f6572c3]{padding:.15rem;background-color:#fff;box-sizing:border-box}.stock-body .content-wrap .title[data-v-3f6572c3]{font-size:.18rem;margin-bottom:.2rem;text-align:center}.stock-body .content-wrap .batch-action .batch-item[data-v-3f6572c3]{margin-right:.15rem;border:.01rem solid rgba(0,0,0,.2);padding:.05rem;border-radius:.03rem;cursor:pointer}.stock-body .content-wrap .table-wrap[data-v-3f6572c3]{position:relative;margin-top:%?40?%;border:%?1?% solid #dcdfe6}.stock-body .content-wrap .table-wrap .iconcheckbox_weiquanxuan[data-v-3f6572c3],\r\n.stock-body .content-wrap .table-wrap .iconfuxuankuang1[data-v-3f6572c3],\r\n.stock-body .content-wrap .table-wrap .iconfuxuankuang2[data-v-3f6572c3]{color:var(--primary-color);cursor:pointer;font-size:.16rem;transition:all .3s}.stock-body .content-wrap .table-wrap .iconfuxuankuang2[data-v-3f6572c3]{color:#e6e6e6}.stock-body .content-wrap .table-wrap .table-head[data-v-3f6572c3]{background-color:#f7f7f7}.stock-body .content-wrap .table-wrap .table-body[data-v-3f6572c3]{max-height:6rem}.stock-body .content-wrap .table-wrap .table-body .table-tr:last-of-type .table-td[data-v-3f6572c3]{border-bottom:0}.stock-body .content-wrap .table-wrap .table-tr[data-v-3f6572c3]{display:flex}.stock-body .content-wrap .table-wrap .table-th[data-v-3f6572c3],\r\n.stock-body .content-wrap .table-wrap .table-td[data-v-3f6572c3]{display:flex;align-items:center;justify-content:center;padding:.07rem .3rem;border-bottom:.01rem solid #dcdfe6;border-right:.01rem solid #dcdfe6;text-align:center}.stock-body .content-wrap .table-wrap .table-th[data-v-3f6572c3]:last-of-type,\r\n.stock-body .content-wrap .table-wrap .table-td[data-v-3f6572c3]:last-of-type{border-right:0;justify-content:flex-end}.stock-body .content-wrap .table-wrap .table-th.goods-name[data-v-3f6572c3],\r\n.stock-body .content-wrap .table-wrap .table-td.goods-name[data-v-3f6572c3]{justify-content:flex-start;text-align:left}.stock-body .content-wrap .table-wrap .table-th.goods-name uni-image[data-v-3f6572c3],\r\n.stock-body .content-wrap .table-wrap .table-td.goods-name uni-image[data-v-3f6572c3]{width:.45rem;height:.45rem;flex-shrink:0}.stock-body .content-wrap .table-wrap .table-th.goods-name .name[data-v-3f6572c3],\r\n.stock-body .content-wrap .table-wrap .table-td.goods-name .name[data-v-3f6572c3]{margin-left:.1rem}.stock-body .content-wrap .table-wrap .delete[data-v-3f6572c3]{margin:0;font-size:.14rem;background-color:var(--primary-color);color:#fff;line-height:.32rem;height:.32rem}.stock-body .content-wrap .table-wrap .delete[data-v-3f6572c3]::after{border-width:0}.stock-body .content-wrap .table-wrap .table-empty[data-v-3f6572c3]{justify-content:center;padding:.1rem;color:#999\r\n  /* border: 0.01rem solid #dcdfe6;\r\n\t\t\t\tborder-top: 0; */}.stock-body .action-wrap[data-v-3f6572c3]{position:absolute;bottom:0;left:0;right:0;display:flex;justify-content:space-between;padding:.24rem .2rem;align-items:center;background-color:#fff;z-index:10}.stock-body .action-wrap .btn-wrap[data-v-3f6572c3]{display:flex;align-items:center;justify-content:center}.stock-body .action-wrap .btn-wrap uni-button[data-v-3f6572c3]{margin:0;min-width:1.2rem;height:.4rem;line-height:.4rem;font-size:.14rem}.stock-body .action-wrap .btn-wrap uni-button.stockout-btn[data-v-3f6572c3]{margin-right:.15rem;background-color:var(--primary-color);color:#fff}.stock-body .action-wrap .btn-wrap uni-button.stockout-btn[data-v-3f6572c3]::after{border-width:0}.stock-body .action-wrap .btn-wrap uni-button.remark[data-v-3f6572c3]{margin-right:.15rem;min-width:1.2rem}',""]),t.exports=e},a7c0:function(t,e,a){"use strict";a.r(e);var i=a("4547"),n=a("fd57");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("8df1");var s=a("828b"),l=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"0812e34e",null,!1,i["a"],void 0);e["default"]=l.exports},ac78:function(t,e,a){"use strict";a.r(e);var i=a("3fef"),n=a("e17d");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("3460");var s=a("828b"),l=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"54426f41",null,!1,i["a"],void 0);e["default"]=l.exports},b538:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("ff4a")),o=i(a("5e0a")),s={components:{goodsSkuSelect:o.default},mixins:[n.default]};e.default=s},d892:function(t,e,a){var i=a("988f");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("3e6baa09",i,!0,{sourceMap:!1,shadowMode:!1})},e17d:function(t,e,a){"use strict";a.r(e);var i=a("4173"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},eba9:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("c223"),a("c9b5"),a("bf0f"),a("ab80"),a("dc8a"),a("2797"),a("aa9c"),a("5ef2"),a("dd2b"),a("22b6");var n=i(a("2166")),o=i(a("ac78")),s=a("54b6"),l={name:"stockDialog",components:{unipopup:n.default,uniDataTable:o.default},model:{prop:"value",event:"change"},props:{value:{type:Boolean,default:!1},params:{type:Object,default:function(){return{}}},goodsClass:{type:Array,default:function(){return[1,4,5,6]}}},data:function(){return{goodsCategoryList:{},activeList:[],option:{category_id:"",search_text:"",is_weigh:0,page_size:8,goods_class_all:"",goods_class:"",supplier_id:"",brand_id:""},checkList:{},cols:[],url:"",goodsClassList:[],isInstallSupply:0,supplierList:[],brandList:[]}},watch:{value:{handler:function(t){var e=this;t?this.$nextTick((function(){e.option=Object.assign(e.option,e.params),e.params.temp_store_id&&""==e.params.temp_store_id&&delete e.option.temp_store_id,e.$refs.dialogRef.open()})):this.$nextTick((function(){e.option=Object(e.option,{category_id:"",search_text:"",is_weigh:0,page:1,page_size:8}),e.checkList={},e.$refs.dialogRef.close()}))},immediate:!0}},mounted:function(){this.skuConfig(),this.getGoodsCategory(),this.getScreen(),this.getGoodsClassList()},methods:{skuConfig:function(){var t=this;this.cols=[{width:20,align:"center",checkbox:!0},{field:"account_data",width:50,title:"商品信息",align:"left",templet:function(e){var a=t.$util.img(e.sku_image),i='\n\t\t\t\t\t\t\t<view class="goods-content">\n\t\t\t\t\t\t\t\t<image class="goods-img" src="'.concat(a,'" mode="aspectFit"/>\n\t\t\t\t\t\t\t\t<text class="goods-name multi-hidden"  title="').concat(e.sku_name,'">').concat(e.sku_name,"</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t");return i}},{field:"stock",width:22,title:"库存",align:"center",templet:function(t){return t.stock||0}},{width:22,title:"单位",templet:function(t){return t.unit||"件"}}],this.url="/cashier/storeapi/goods/getSkuListBySelect"},selectGoodsClass:function(t){this.option.goods_class=-1==t?"":this.goodsClassList[t].value.toString(),this.getStoreGoods()},selectBrand:function(t){this.option.brand_id=-1==t?"":this.brandList[t].value.toString(),this.getStoreGoods()},selectSupplier:function(t){this.option.supplier_id=-1==t?"":this.supplierList[t].value.toString(),this.getStoreGoods()},getGoodsCategory:function(){var t=this;(0,s.getManageGoodsCategory)().then((function(e){uni.hideLoading(),e.data&&Object.keys(e.data)?t.goodsCategoryList=e.data:t.$util.showToast({title:e.message})}))},getScreen:function(){var t=this;(0,s.getGoodsSceen)().then((function(e){t.isInstallSupply=e.data.is_install_supply,(e.data.supplier_list||[]).forEach((function(e){t.supplierList.push({value:e.supplier_id,label:e.title})})),e.data.brand_list.forEach((function(e){t.brandList.push({value:e.brand_id,label:e.brand_name})}))}))},getGoodsClassList:function(){var t=this,e=[{value:this.$util.goodsClassDict.real,label:"实物商品"},{value:this.$util.goodsClassDict.service,label:"服务项目"},{value:this.$util.goodsClassDict.card,label:"卡项套餐"},{value:this.$util.goodsClassDict.weigh,label:"称重商品"}],a=[];e.forEach((function(e){t.goodsClass.indexOf(e.value)>-1&&(t.goodsClassList.push(e),a.push(e.value))})),this.option.goods_class_all=a.toString()},itemClick:function(t){this.option.category_id=t.category_id;var e=this.activeList.indexOf(t.category_id);t.child_num&&-1===e?this.activeList.push(t.category_id):t.child_num&&-1!=e&&this.activeList.splice(e,1),this.$forceUpdate(),this.getStoreGoods()},getStoreGoods:function(){this.$refs.goodsListTable.load({page:1})},submit:function(t){var e=this,a=this.$refs.goodsListTable.getSelectData();if(0==a.selectedNum)return this.$util.showToast({title:"请选择商品"}),!1;if(a.allSelected){var i=this.$util.deepClone(this.option);i.unselected_sku_ids=Object.keys(a.unselectedData).toString(),i.page_size=0,uni.showLoading({title:"数据获取中"}),(0,s.getSkuListBySelect)(i).then((function(a){uni.hideLoading(),e.$emit("selectGoods",a.data.list),e.$refs.goodsListTable.clearCheck(),"close"==t&&e.$emit("change",!1)}))}else this.$emit("selectGoods",Object.values(a.selectedData)),this.$refs.goodsListTable.clearCheck(),"close"==t&&this.$emit("change",!1)}}};e.default=l},fd57:function(t,e,a){"use strict";a.r(e);var i=a("5a63"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},ff4a:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("2797"),a("aa9c"),a("d4b5");var i=a("54b6"),n={data:function(){return{goodsList:[],allSelected:!1,isSubmit:!1,dialogParams:{},dialogVisible:!1,editPrintNum:{show:!1,value:1},selectedIcon:"iconfuxuankuang1",unselectedIcon:"iconfuxuankuang2",harfselectedIcon:"iconcheckbox_weiquanxuan"}},onLoad:function(t){},methods:{openSelectGoodsDialog:function(){this.dialogVisible=!0},selectGoodsComplete:function(t){var e=this;t.forEach((function(t,a){var i=!1;e.goodsList.forEach((function(e,a){e.sku_id!=t.sku_id||(i=!0)})),i||(t.selected=!1,t.print_num=1,e.goodsList.push(t))}))},changeGoodsAllSelected:function(){var t=this;0!=this.goodsList.length&&(this.allSelected=!0!==this.allSelected,this.goodsList.forEach((function(e){e.selected=t.allSelected})),this.$forceUpdate())},changeGoodsSelected:function(t){this.goodsList[t].selected=!this.goodsList[t].selected;var e=0;this.goodsList.forEach((function(t){t.selected&&e++})),e==this.goodsList.length?this.allSelected=!0:this.allSelected=0!=e&&"harf",this.$forceUpdate()},getSelectedNum:function(){var t=0;return this.goodsList.forEach((function(e){e.selected&&t++})),t},batchDeleteGoods:function(){if(0!=this.getSelectedNum()){var t=[];this.goodsList.forEach((function(e){e.selected||t.push(e)})),this.goodsList=t,0==this.goodsList.length&&(this.allSelected=!1),this.$forceUpdate()}else this.$util.showToast({title:"请选择要操作的数据"})},editPrintNumShow:function(){0!=this.getSelectedNum()?(this.editPrintNum.show=!0,this.$forceUpdate()):this.$util.showToast({title:"请选择要操作的数据"})},editPrintNumConfirm:function(){var t=this;this.goodsList.forEach((function(e){e.selected&&(e.print_num=t.editPrintNum.value,e.selected=!1)})),this.allSelected=!1,this.editPrintNum.value=1,this.editPrintNum.show=!1,this.$forceUpdate()},designFn:function(){if(this.isPos())if(0!=this.goodsList.length){try{this.$pos.send("DesignPriceTag",JSON.stringify(this.goodsList))}catch(t){this.$util.showToast({title:"设计错误:"+JSON.stringify(t)})}}else this.$util.showToast({title:"请先选择商品"});else this.$util.showToast({title:"请在客户端程序中执行此操作"})},printFn:function(){if(this.isPos())if(0!=this.goodsList.length)try{this.$pos.send("PrintPriceTag",JSON.stringify(this.goodsList))}catch(t){this.$util.showToast({title:"打印错误:"+JSON.stringify(t)})}else this.$util.showToast({title:"请先选择商品"});else this.$util.showToast({title:"请在客户端程序中执行此操作"})},exportFn:function(){var t=this;if(0!=this.goodsList.length)if(this.isPos())try{this.$pos.send("ExportPriceTag",JSON.stringify(this.goodsList))}catch(e){this.$util.showToast({title:"导出错误:"+JSON.stringify(e)})}else uni.showLoading({title:"导出中"}),(0,i.exportPrintPriceTagData)({data:JSON.stringify(this.goodsList)}).then((function(e){uni.hideLoading(),0==e.code?window.open(t.$util.img(e.data.path)):t.$util.showToast({title:e.message})}));else this.$util.showToast({title:"请先选择商品数据"})},backFn:function(){this.$util.redirectTo("/pages/goods/goodslist")},isPos:function(){return window.POS_||window.ipcRenderer}}};e.default=n}}]);