.form-content {
	display: flex;
	flex-wrap: wrap;
	margin-top: 0.2rem;

	.store-info {
		.form-inline {
			padding-left: 0.05rem;
		}

	}

	.form-item {
		margin-bottom: 0.1rem;
		display: flex;

		.form-label {
			width: 1.3rem;
			text-align: right;
			padding-right: 0.1rem;
			box-sizing: border-box;
			height: 0.32rem;
			line-height: 0.32rem;

			.required {
				color: red;
				margin-right: 0.03rem;
			}
		}

		.form-inline {
			width: 2.4rem;
			line-height: 0.32rem;
			margin-right: 0.1rem;
			box-sizing: border-box;

			&.input {
				input {
					padding: 0 0.1rem;
				}
			}

			.form-input {
				border-width: 0.01rem;
				border-style: solid;
				background-color: #fff;
				color: rgba(0, 0, 0, 0.85);
				border-radius: 0.02rem;
				padding-left: 0.1rem;
				height: 0.32rem;
				line-height: 0.32rem;
				font-size: 0.14rem;
				border-color: #e6e6e6;
			}
		}
	}
}

.stock-body{
	position: relative;
	height: 100%;
	.content-wrap {
		
		padding: 0.15rem;
		background-color: #fff;
		@extend %body-overhide;
		box-sizing: border-box;

		.title {
			font-size: 0.18rem;
			margin-bottom: 0.2rem;
			text-align: center;
		}

		.table-wrap {
			position: relative;
			margin-top: 40rpx;
			border: 1rpx solid #dcdfe6;

			.table-head {
				background-color: #f7f7f7;
			}

			.table-body {
				@extend %body-overhide;
				max-height: 6rem;

				.table-tr {
					&:nth-child(1) {
						position: absolute;
						left: 0;
						right: 0;
						background: #fff;
						z-index: 3;
					}

					&:nth-child(2) {
						margin-top: 0.49rem;
					}

					&:last-of-type .table-td {
						border-bottom: 0;
					}
				}
			}

			.table-tr {
				display: flex;
			}

			.table-th,
			.table-td {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0.07rem 0.3rem;
				border-bottom: 0.01rem solid #dcdfe6;
				border-right: 0.01rem solid #dcdfe6;
				text-align: center;

				&:last-of-type {
					border-right: 0;
					justify-content: flex-end;
				}

				&.goods-name {
					justify-content: flex-start;

					image {
						width: 0.45rem;
						height: 0.45rem;
						flex-shrink: 0;
					}

					.name {
						margin-left: 0.1rem;
					}
				}
			}

			.delete {
				margin: 0;
				font-size: $uni-font-size-base;
				background-color: $primary-color;
				color: #fff;
				line-height: 0.32rem;
				height: 0.32rem;
				&::after{
					border-width: 0;
				}
			}

			.table-empty {
				justify-content: center;
				padding: 0.3rem;
				color: #999;
			}
		}
		.select-goods-input,
		.goods-name {
			position: relative;

			input {
				flex: 1;
				padding: 0 0.2rem;
			}

			.icontuodong {
				font-size: 0.16rem;
				position: absolute;
				top: 0.17rem;
				right: 0.34rem;
				z-index: 2;
				cursor: pointer;
			}
		}

		input {
			font-size: $uni-font-size-base !important;
			border: 0.01rem solid #e6e6e6 !important;
			height: 0.32rem;
		}
	}
	.action-wrap {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-between;
		padding: 0.24rem 0.2rem;
		align-items: center;
		background-color: #fff;
		z-index: 10;
		.btn-wrap {
			display: flex;
			align-items: center;
			justify-content: center;

			button {
				margin: 0;
				min-width: 2.75rem;
				height: 0.4rem;
				line-height: 0.4rem;
				font-size: $uni-font-size-base;

				&.stockout-btn {
					margin-right: 0.15rem;
					background-color: $primary-color;
					color: #fff;
					&::after{
						border-width: 0;
					}
				}

				&.remark {
					margin-right: 0.15rem;
					min-width: 1.2rem;
				}
			}
		}
	}
}
.remark-wrap {
	width: 6rem;
	background-color: #fff;
	border-radius: 0.04rem;
	box-shadow: 0 0.01rem 0.12rem 0 rgba(0, 0, 0, 0.1);

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 0.15rem;
		height: 0.45rem;
		line-height: 0.45rem;
		border-bottom: 0.01rem solid #e8eaec;

		.iconfont {
			font-size: $uni-font-size-lg;
		}
	}

	.body {
		padding: 0.15rem 0.15rem 0.1rem;

		textarea {
			border: 0.01rem solid #e6e6e6;
			width: 100%;
			padding: 0.1rem;
			box-sizing: border-box;
			font-size: 0.14rem;
		}

		.placeholder-class {
			font-size: 0.14rem;
		}
	}

	.footer {
		height: 0.5rem;
		padding-bottom: 0.05rem;
		display: flex;
		align-items: center;
		justify-content: center;

		button.default {
			width: 95%;
		}
	}
}