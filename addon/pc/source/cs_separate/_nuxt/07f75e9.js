(window.webpackJsonp=window.webpackJsonp||[]).push([[1],{0:function(t,e,n){"use strict";n.d(e,"k",(function(){return y})),n.d(e,"m",(function(){return O})),n.d(e,"l",(function(){return j})),n.d(e,"e",(function(){return w})),n.d(e,"b",(function(){return x})),n.d(e,"s",(function(){return C})),n.d(e,"g",(function(){return k})),n.d(e,"h",(function(){return S})),n.d(e,"d",(function(){return E})),n.d(e,"r",(function(){return T})),n.d(e,"j",(function(){return I})),n.d(e,"t",(function(){return P})),n.d(e,"o",(function(){return R})),n.d(e,"q",(function(){return L})),n.d(e,"f",(function(){return D})),n.d(e,"c",(function(){return N})),n.d(e,"i",(function(){return M})),n.d(e,"p",(function(){return U})),n.d(e,"a",(function(){return Q})),n.d(e,"v",(function(){return Y})),n.d(e,"n",(function(){return W})),n.d(e,"u",(function(){return X}));n(83),n(25),n(84),n(85),n(29),n(18),n(30);var r=n(15),o=n(21),c=n(10),l=n(42),d=(n(95),n(7),n(23),n(370),n(56),n(92),n(75),n(24),n(48),n(50),n(65),n(31),n(64),n(253),n(154),n(215),n(74),n(151),n(374),n(107),n(132),n(2)),f=n(54);function m(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function h(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?m(Object(source),!0).forEach((function(e){Object(c.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):m(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}function v(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return _(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,c=!0,l=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return c=t.done,t},e:function(t){l=!0,o=t},f:function(){try{c||null==n.return||n.return()}finally{if(l)throw o}}}}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function y(t){d.default.config.errorHandler&&d.default.config.errorHandler(t)}function O(t){return t.then((function(t){return t.default||t}))}function j(t){return t.$options&&"function"==typeof t.$options.fetch&&!t.$options.fetch.length}function w(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=t.$children||[],o=v(r);try{for(o.s();!(e=o.n()).done;){var c=e.value;c.$fetch?n.push(c):c.$children&&w(c,n)}}catch(t){o.e(t)}finally{o.f()}return n}function x(t,e){if(e||!t.options.__hasNuxtData){var n=t.options._originDataFn||t.options.data||function(){return{}};t.options._originDataFn=n,t.options.data=function(){var data=n.call(this,this);return this.$ssrContext&&(e=this.$ssrContext.asyncData[t.cid]),h(h({},data),e)},t.options.__hasNuxtData=!0,t._Ctor&&t._Ctor.options&&(t._Ctor.options.data=t.options.data)}}function C(t){return t.options&&t._Ctor===t||(t.options?(t._Ctor=t,t.extendOptions=t.options):(t=d.default.extend(t))._Ctor=t,!t.options.name&&t.options.__file&&(t.options.name=t.options.__file)),t}function k(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"components";return Array.prototype.concat.apply([],t.matched.map((function(t,r){return Object.keys(t[n]).map((function(o){return e&&e.push(r),t[n][o]}))})))}function S(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return k(t,e,"instances")}function E(t,e){return Array.prototype.concat.apply([],t.matched.map((function(t,n){return Object.keys(t.components).reduce((function(r,o){return t.components[o]?r.push(e(t.components[o],t.instances[o],t,o,n)):delete t.components[o],r}),[])})))}function T(t,e){return Promise.all(E(t,function(){var t=Object(o.a)(regeneratorRuntime.mark((function t(n,r,o,c){var l,d;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("function"!=typeof n||n.options){t.next=11;break}return t.prev=1,t.next=4,n();case 4:n=t.sent,t.next=11;break;case 7:throw t.prev=7,t.t0=t.catch(1),t.t0&&"ChunkLoadError"===t.t0.name&&"undefined"!=typeof window&&window.sessionStorage&&(l=Date.now(),(!(d=parseInt(window.sessionStorage.getItem("nuxt-reload")))||d+6e4<l)&&(window.sessionStorage.setItem("nuxt-reload",l),window.location.reload(!0))),t.t0;case 11:return o.components[c]=n=C(n),t.abrupt("return","function"==typeof e?e(n,r,o,c):n);case 13:case"end":return t.stop()}}),t,null,[[1,7]])})));return function(e,n,r,o){return t.apply(this,arguments)}}()))}function I(t){return $.apply(this,arguments)}function $(){return($=Object(o.a)(regeneratorRuntime.mark((function t(e){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,T(e);case 4:return t.abrupt("return",h(h({},e),{},{meta:k(e).map((function(t,n){return h(h({},t.options.meta),(e.matched[n]||{}).meta)}))}));case 5:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function P(t,e){return A.apply(this,arguments)}function A(){return(A=Object(o.a)(regeneratorRuntime.mark((function t(e,n){var o,c,d,m;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.context||(e.context={isStatic:!0,isDev:!1,isHMR:!1,app:e,store:e.store,payload:n.payload,error:n.error,base:e.router.options.base,env:{}},n.req&&(e.context.req=n.req),n.res&&(e.context.res=n.res),n.ssrContext&&(e.context.ssrContext=n.ssrContext),e.context.redirect=function(t,path,n){if(t){e.context._redirected=!0;var o=Object(r.a)(path);if("number"==typeof t||"undefined"!==o&&"object"!==o||(n=path||{},path=t,o=Object(r.a)(path),t=302),"object"===o&&(path=e.router.resolve(path).route.fullPath),!/(^[.]{1,2}\/)|(^\/(?!\/))/.test(path))throw path=Object(f.d)(path,n),window.location.replace(path),new Error("ERR_REDIRECT");e.context.next({path:path,query:n,status:t})}},e.context.nuxtState=window.__NUXT__),t.next=3,Promise.all([I(n.route),I(n.from)]);case 3:o=t.sent,c=Object(l.a)(o,2),d=c[0],m=c[1],n.route&&(e.context.route=d),n.from&&(e.context.from=m),e.context.next=n.next,e.context._redirected=!1,e.context._errored=!1,e.context.isHMR=!1,e.context.params=e.context.route.params||{},e.context.query=e.context.route.query||{};case 15:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function R(t,e){return!t.length||e._redirected||e._errored?Promise.resolve():L(t[0],e).then((function(){return R(t.slice(1),e)}))}function L(t,e){var n;return(n=2===t.length?new Promise((function(n){t(e,(function(t,data){t&&e.error(t),n(data=data||{})}))})):t(e))&&n instanceof Promise&&"function"==typeof n.then?n:Promise.resolve(n)}function D(base,t){if("hash"===t)return window.location.hash.replace(/^#\//,"");base=decodeURI(base).slice(0,-1);var path=decodeURI(window.location.pathname);base&&path.startsWith(base)&&(path=path.slice(base.length));var e=(path||"/")+window.location.search+window.location.hash;return Object(f.c)(e)}function N(t,e){return function(t,e){for(var n=new Array(t.length),i=0;i<t.length;i++)"object"===Object(r.a)(t[i])&&(n[i]=new RegExp("^(?:"+t[i].pattern+")$",J(e)));return function(e,r){for(var path="",data=e||{},o=(r||{}).pretty?H:encodeURIComponent,c=0;c<t.length;c++){var l=t[c];if("string"!=typeof l){var d=data[l.name||"pathMatch"],f=void 0;if(null==d){if(l.optional){l.partial&&(path+=l.prefix);continue}throw new TypeError('Expected "'+l.name+'" to be defined')}if(Array.isArray(d)){if(!l.repeat)throw new TypeError('Expected "'+l.name+'" to not repeat, but received `'+JSON.stringify(d)+"`");if(0===d.length){if(l.optional)continue;throw new TypeError('Expected "'+l.name+'" to not be empty')}for(var m=0;m<d.length;m++){if(f=o(d[m]),!n[c].test(f))throw new TypeError('Expected all "'+l.name+'" to match "'+l.pattern+'", but received `'+JSON.stringify(f)+"`");path+=(0===m?l.prefix:l.delimiter)+f}}else{if(f=l.asterisk?B(d):o(d),!n[c].test(f))throw new TypeError('Expected "'+l.name+'" to match "'+l.pattern+'", but received "'+f+'"');path+=l.prefix+f}}else path+=l}return path}}(function(t,e){var n,r=[],o=0,c=0,path="",l=e&&e.delimiter||"/";for(;null!=(n=F.exec(t));){var d=n[0],f=n[1],m=n.index;if(path+=t.slice(c,m),c=m+d.length,f)path+=f[1];else{var h=t[c],v=n[2],_=n[3],y=n[4],O=n[5],j=n[6],w=n[7];path&&(r.push(path),path="");var x=null!=v&&null!=h&&h!==v,C="+"===j||"*"===j,k="?"===j||"*"===j,S=n[2]||l,pattern=y||O;r.push({name:_||o++,prefix:v||"",delimiter:S,optional:k,repeat:C,partial:x,asterisk:Boolean(w),pattern:pattern?K(pattern):w?".*":"[^"+G(S)+"]+?"})}}c<t.length&&(path+=t.substr(c));path&&r.push(path);return r}(t,e),e)}function M(t,e){var n={},r=h(h({},t),e);for(var o in r)String(t[o])!==String(e[o])&&(n[o]=!0);return n}function U(t){var e;if(t.message||"string"==typeof t)e=t.message||t;else try{e=JSON.stringify(t,null,2)}catch(n){e="[".concat(t.constructor.name,"]")}return h(h({},t),{},{message:e,statusCode:t.statusCode||t.status||t.response&&t.response.status||500})}window.onNuxtReadyCbs=[],window.onNuxtReady=function(t){window.onNuxtReadyCbs.push(t)};var F=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function H(t,e){var n=e?/[?#]/g:/[/?#]/g;return encodeURI(t).replace(n,(function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()}))}function B(t){return H(t,!0)}function G(t){return t.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function K(t){return t.replace(/([=!:$/()])/g,"\\$1")}function J(t){return t&&t.sensitive?"":"i"}function Q(t,e,n){t.$options[e]||(t.$options[e]=[]),t.$options[e].includes(n)||t.$options[e].push(n)}var Y=f.b,W=(f.e,f.a);function X(t){try{window.history.scrollRestoration=t}catch(t){}}},1:function(t,e,n){"use strict";n.d(e,"a",(function(){return d}));n(56),n(7);var r=n(33),o=n(34),c=n.n(o),l=n(22);function d(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"POST",r=t.url,data={app_type:"pc",app_type_name:"PC"},o=Object(l.a)();return o&&(data.token=o),null!=t.data&&Object.assign(data,t.data),c()({url:r,method:n,data:data}).then((function(t){var code=(t.data||{}).code;if(-3!=code||"close"==window.$nuxt.$route.name)return code==e?t.data:Promise.reject(t.data);window.$nuxt.$router.push("/close")})).catch((function(e){return"TOKEN_ERROR"===e.error_code&&(e.message="登录错误",vue.$store.dispatch("member/remove_token"),t.forceLogin&&vue.$router.push("/auth/login?redirect=".concat(encodeURIComponent(vue.$router.history.current.fullPath)))),Promise.reject(e)}))}c.a.defaults.baseURL=r.a.baseUrl,c.a.defaults.headers={"X-Requested-With":"XMLHttpRequest","content-type":"application/json"},c.a.defaults.responseType="json",c.a.defaults.timeout=6e4},146:function(t,e,n){"use strict";n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return c})),n.d(e,"d",(function(){return l})),n.d(e,"b",(function(){return d}));var r=n(1);function o(t){return Object(r.a)({url:"/wechat/api/wechat/logincode",data:t})}function c(t){return Object(r.a)({url:"/api/login/checklogin",data:t})}function l(t){return Object(r.a)({url:"/api/login/wechatLogin",data:t})}function d(t){return Object(r.a)({url:"/api/config/init",data:t})}},150:function(t,e,n){"use strict";n(73),n(7),n(18),n(107),n(132),n(75),n(23),n(65),n(56),n(83),n(48),n(31),n(25),n(84),n(85),n(50);var r=n(2);function o(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return c(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,d=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return l=t.done,t},e:function(t){d=!0,o=t},f:function(){try{l||null==n.return||n.return()}finally{if(d)throw o}}}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}var l=window.requestIdleCallback||function(t){var e=Date.now();return setTimeout((function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})}),1)},d=window.cancelIdleCallback||function(t){clearTimeout(t)},f=window.IntersectionObserver&&new window.IntersectionObserver((function(t){t.forEach((function(t){var e=t.intersectionRatio,link=t.target;e<=0||!link.__prefetch||link.__prefetch()}))}));e.a={name:"NuxtLink",extends:r.default.component("RouterLink"),props:{prefetch:{type:Boolean,default:!0},noPrefetch:{type:Boolean,default:!1}},mounted:function(){this.prefetch&&!this.noPrefetch&&(this.handleId=l(this.observe,{timeout:2e3}))},beforeDestroy:function(){d(this.handleId),this.__observed&&(f.unobserve(this.$el),delete this.$el.__prefetch)},methods:{observe:function(){f&&this.shouldPrefetch()&&(this.$el.__prefetch=this.prefetchLink.bind(this),f.observe(this.$el),this.__observed=!0)},shouldPrefetch:function(){return this.getPrefetchComponents().length>0},canPrefetch:function(){var t=navigator.connection;return!(this.$nuxt.isOffline||t&&((t.effectiveType||"").includes("2g")||t.saveData))},getPrefetchComponents:function(){return this.$router.resolve(this.to,this.$route,this.append).resolved.matched.map((function(t){return t.components.default})).filter((function(t){return"function"==typeof t&&!t.options&&!t.__prefetched}))},prefetchLink:function(){if(this.canPrefetch()){f.unobserve(this.$el);var t,e=o(this.getPrefetchComponents());try{for(e.s();!(t=e.n()).done;){var n=t.value,r=n();r instanceof Promise&&r.catch((function(){})),n.__prefetched=!0}}catch(t){e.e(t)}finally{e.f()}}}}}},152:function(t,e,n){"use strict";n.d(e,"d",(function(){return o})),n.d(e,"b",(function(){return c})),n.d(e,"a",(function(){return l})),n.d(e,"c",(function(){return d}));var r=n(1);function o(t){return Object(r.a)({url:"/api/goodscategory/tree",data:t})}function c(t){return Object(r.a)({url:"/api/goodscategory/info",data:t})}function l(t){return Object(r.a)({url:"/api/config/categoryconfig",data:t})}function d(t){return Object(r.a)({url:"/api/goodscategory/lists",data:t})}},175:function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"c",(function(){return c})),n.d(e,"b",(function(){return l})),n.d(e,"e",(function(){return d})),n.d(e,"d",(function(){return f})),n.d(e,"g",(function(){return m})),n.d(e,"f",(function(){return h})),n.d(e,"h",(function(){return v}));var r=n(1);function o(t){return Object(r.a)({url:"/api/login/login",data:t})}function c(t){return Object(r.a)({url:"/api/login/mobile",data:t})}function l(t){return Object(r.a)({url:"/api/login/mobileCode",data:t})}function d(t){return Object(r.a)({url:"/api/findpassword/mobile",data:t})}function f(t){return Object(r.a)({url:"/api/member/checkmobile",data:t},-1)}function m(t){return Object(r.a)({url:"/api/findpassword/mobilecode",data:t})}function h(t){return Object(r.a)({url:"/api/register/config",data:t})}function v(t){return Object(r.a)({url:"/api/login/getMobileCode",data:t})}},199:function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return c})),n.d(e,"c",(function(){return l})),n.d(e,"e",(function(){return d})),n.d(e,"f",(function(){return f})),n.d(e,"d",(function(){return m}));var r=n(1);function o(t){return Object(r.a)({url:"/api/register/aggrement",data:t})}function c(t){return Object(r.a)({url:"/memberregister/api/Config/Config",data:t})}function l(t){return Object(r.a)({url:"/api/register/username",data:t})}function d(t){return Object(r.a)({url:"/api/register/mobile",data:t})}function f(t){return Object(r.a)({url:"/api/register/mobileCode",data:t})}function m(t){return Object(r.a)({url:"/api/register/config",data:t})}},200:function(t,e,n){"use strict";var r={};r.auth=n(369),r.auth=r.auth.default||r.auth,e.a=r},204:function(t,e,n){"use strict";n.d(e,"d",(function(){return o})),n.d(e,"f",(function(){return c})),n.d(e,"b",(function(){return l})),n.d(e,"g",(function(){return d})),n.d(e,"c",(function(){return f})),n.d(e,"e",(function(){return m})),n.d(e,"h",(function(){return h})),n.d(e,"i",(function(){return v})),n.d(e,"a",(function(){return _}));var r=n(1);function o(t){return Object(r.a)({url:"/api/order/lists",data:t,forceLogin:!0})}function c(t){return Object(r.a)({url:"/api/order/pay",data:t,forceLogin:!0})}function l(t){return Object(r.a)({url:"/api/order/close",data:t,forceLogin:!0})}function d(t){return Object(r.a)({url:"/api/order/takedelivery",data:t,forceLogin:!0})}function f(t){return Object(r.a)({url:"/api/order/detail",data:t,forceLogin:!0})}function m(t){return Object(r.a)({url:"/api/order/package",data:t,forceLogin:!0})}function h(t){return Object(r.a)({url:"/api/order/evluateinfo",data:t,forceLogin:!0})}function v(t){var e="";return e=t.isEvaluate?"/api/goodsevaluate/again":"/api/goodsevaluate/add",Object(r.a)({url:e,data:t,forceLogin:!0})}function _(t){return Object(r.a)({url:"/api/order/membervirtualtakedelivery",data:t,forceLogin:!0})}},206:function(t,e,n){"use strict";n.d(e,"h",(function(){return o})),n.d(e,"f",(function(){return c})),n.d(e,"g",(function(){return l})),n.d(e,"d",(function(){return d})),n.d(e,"b",(function(){return f})),n.d(e,"i",(function(){return m})),n.d(e,"a",(function(){return h})),n.d(e,"e",(function(){return v})),n.d(e,"c",(function(){return _}));var r=n(1);function o(t){return Object(r.a)({url:"/api/goodssku/page",data:t})}function c(t){return Object(r.a)({url:"/api/goodssku/detail",data:t})}function l(t){return Object(r.a)({url:"/api/goodssku/info",data:t})}function d(t){return Object(r.a)({url:"/api/goodssku/goodsqrcode",data:t})}function f(t){return Object(r.a)({url:"/api/goods/aftersale",data:t})}function m(t){return Object(r.a)({url:"/api/goods/modifyclicks",data:t})}function h(t){return Object(r.a)({url:"/api/goodsbrowse/add",data:t})}function v(t){return Object(r.a)({url:"/api/goodssku/recommend",data:t})}function _(t){return Object(r.a)({url:"/api/goodsbrand/page",data:t})}},207:function(t,e,n){"use strict";n.d(e,"k",(function(){return o})),n.d(e,"e",(function(){return c})),n.d(e,"n",(function(){return l})),n.d(e,"i",(function(){return d})),n.d(e,"d",(function(){return f})),n.d(e,"m",(function(){return m})),n.d(e,"f",(function(){return h})),n.d(e,"j",(function(){return v})),n.d(e,"h",(function(){return _})),n.d(e,"c",(function(){return y})),n.d(e,"a",(function(){return O})),n.d(e,"g",(function(){return j})),n.d(e,"o",(function(){return w})),n.d(e,"b",(function(){return x})),n.d(e,"l",(function(){return C}));var r=n(1);function o(t){return Object(r.a)({url:"/api/member/info",data:t})}function c(t){return Object(r.a)({url:"/api/memberaddress/page",data:t,forceLogin:!0})}function l(t){return Object(r.a)({url:"/api/memberaddress/setdefault",data:t,forceLogin:!0})}function d(t){return Object(r.a)({url:"/api/memberaddress/delete",data:t,forceLogin:!0})}function f(t){return Object(r.a)({url:"/api/memberaddress/info",data:t,forceLogin:!0})}function m(t){return Object(r.a)({url:"/api/memberaddress/"+t.url,data:t,forceLogin:!0})}function h(t){return Object(r.a)({url:"/coupon/api/coupon/memberpage",data:t,forceLogin:!0})}function v(t){return Object(r.a)({url:"/api/goodsbrowse/page",data:t,forceLogin:!0})}function _(t){return Object(r.a)({url:"/api/goodsbrowse/delete",data:t,forceLogin:!0})}function y(t){return Object(r.a)({url:"/api/memberbankaccount/page",data:t})}function O(t){return Object(r.a)({url:"/api/memberbankaccount/setdefault",data:t})}function j(t){return Object(r.a)({url:"/api/memberbankaccount/delete",data:t})}function w(t){return Object(r.a)({url:"/api/memberwithdraw/transferType",data:t})}function x(t){return Object(r.a)({url:"/api/memberbankaccount/info",data:t})}function C(t){return Object(r.a)({url:"/api/memberbankaccount/"+t.url,data:t})}},208:function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return c}));var r=n(1);function o(t){return Object(r.a)({url:"/api/address/lists",data:t})}function c(t){return Object(r.a)({url:"/api/address/city",data:t})}},216:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return c})),n.d(e,"c",(function(){return l}));var r=n(1);function o(t){return Object(r.a)({url:"/api/helpclass/lists",data:t})}function c(t){return Object(r.a)({url:"/api/help/info",data:t})}function l(t){return Object(r.a)({url:"/api/help/page",data:t})}},217:function(t,e,n){"use strict";n.r(e);n(24),n(25),n(23),n(29),n(30);var r=n(10),o=(n(73),n(7),n(18),n(92),n(31),n(64),n(209),n(317),n(33)),c=n(313),l=n(314),d=n(1);function f(t){return Object(d.a)({url:"/servicer/api/chat/say",data:t})}function m(t){return Object(d.a)({url:"/servicer/api/chat/bye",data:t})}var h=n(12),v=(n(215),n(74),{emjoyList:{"[emjoy_01]":"public/static/img/emojy/emjoy_01.gif","[emjoy_02]":"public/static/img/emojy/emjoy_02.gif","[emjoy_03]":"public/static/img/emojy/emjoy_03.gif","[emjoy_04]":"public/static/img/emojy/emjoy_04.gif","[emjoy_05]":"public/static/img/emojy/emjoy_05.gif","[emjoy_06]":"public/static/img/emojy/emjoy_06.gif","[emjoy_07]":"public/static/img/emojy/emjoy_07.gif","[emjoy_08]":"public/static/img/emojy/emjoy_08.gif","[emjoy_09]":"public/static/img/emojy/emjoy_09.gif","[emjoy_10]":"public/static/img/emojy/emjoy_10.gif","[emjoy_11]":"public/static/img/emojy/emjoy_11.gif","[emjoy_12]":"public/static/img/emojy/emjoy_12.gif","[emjoy_13]":"public/static/img/emojy/emjoy_13.gif","[emjoy_14]":"public/static/img/emojy/emjoy_14.gif","[emjoy_15]":"public/static/img/emojy/emjoy_15.gif","[emjoy_16]":"public/static/img/emojy/emjoy_16.gif","[emjoy_17]":"public/static/img/emojy/emjoy_17.gif","[emjoy_18]":"public/static/img/emojy/emjoy_18.gif","[emjoy_19]":"public/static/img/emojy/emjoy_19.gif","[emjoy_20]":"public/static/img/emojy/emjoy_20.gif","[emjoy_21]":"public/static/img/emojy/emjoy_21.gif","[emjoy_22]":"public/static/img/emojy/emjoy_22.gif","[emjoy_23]":"public/static/img/emojy/emjoy_23.gif","[emjoy_24]":"public/static/img/emojy/emjoy_24.gif","[emjoy_25]":"public/static/img/emojy/emjoy_25.gif","[emjoy_26]":"public/static/img/emojy/emjoy_26.gif","[emjoy_27]":"public/static/img/emojy/emjoy_27.gif","[emjoy_28]":"public/static/img/emojy/emjoy_28.gif","[emjoy_29]":"public/static/img/emojy/emjoy_29.gif","[emjoy_30]":"public/static/img/emojy/emjoy_30.gif","[emjoy_31]":"public/static/img/emojy/emjoy_31.gif","[emjoy_32]":"public/static/img/emojy/emjoy_32.gif","[emjoy_33]":"public/static/img/emojy/emjoy_33.gif","[emjoy_34]":"public/static/img/emojy/emjoy_34.gif","[emjoy_35]":"public/static/img/emojy/emjoy_35.gif","[emjoy_36]":"public/static/img/emojy/emjoy_36.gif","[emjoy_37]":"public/static/img/emojy/emjoy_37.gif","[emjoy_38]":"public/static/img/emojy/emjoy_38.gif","[emjoy_39]":"public/static/img/emojy/emjoy_39.gif","[emjoy_40]":"public/static/img/emojy/emjoy_40.gif","[emjoy_41]":"public/static/img/emojy/emjoy_41.gif","[emjoy_42]":"public/static/img/emojy/emjoy_42.gif","[emjoy_43]":"public/static/img/emojy/emjoy_43.gif","[emjoy_44]":"public/static/img/emojy/emjoy_44.gif","[emjoy_45]":"public/static/img/emojy/emjoy_45.gif","[emjoy_46]":"public/static/img/emojy/emjoy_46.gif","[emjoy_47]":"public/static/img/emojy/emjoy_47.gif"},stringToEmjoy:function(t){var e=this;if(t){var n=t,r=new RegExp("\\[emjoy_(.+?)\\]","g"),o=n.replace(r,(function(t){var n="";for(var r in e.emjoyList){if(t==r)n="<img style='margin:0 3px;' src='"+util.img(e.emjoyList[r])+"'/>"}return n||t}));return o}}});function _(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var y={components:{goodsItem:c.default,orderItem:l.default},props:{skuId:{default:0},orderId:{default:0},logo:{default:""},shop:{type:Object,default:function(){return{shop_name:"",shop_id:"",logo:""}}}},data:function(){return{isFirstInit:!0,showEmoji:!1,emjoyList:v.emjoyList,showService:!1,search_text:"",websock:null,messageList:[],servicerId:0,message:"",image:"",sendSwitch:!0,page:1,total:0,getSwitch:!0,contentEmpty:!1,sessionList:[],currShop:{shop_name:"",site_logo:""},keyWordsConfig:{},canSend:!0,siteIdMap:0,timeoutObj:null,uploadActionUrl:o.a.baseUrl+"/api/upload/chatimg",text_num:0,is_first:!0,shopinfo:{name:"",logo:""}}},filters:{Time:function(time){var t=parseInt(time.substr(0,4)),e=parseInt(time.substr(5,2)),n=parseInt(time.substr(8,2)),r=d.getFullYear(),o=d.getMonth()+1,c=d.getDate(),l=new Date(t,e-1,n),d=new Date(r,o-1,c),f=l.getTime()-d.getTime();return Math.abs(f)<864e5?"今天"+time.substr(11,5):f>0&&f<1728e5?"明天"+time.substr(11,5):f<0&&f>-1728e5?"昨天"+time.substr(11,5):time.substr(5,16)}},created:function(){var t=this;this.siteIdMap=this.shop.shop_id,setTimeout((function(){t.addonIsExit.servicer&&(t.getKeywords(),t.getSiteInfo())}),1e3)},watch:{showService:function(t){!t&&this.websock&&this.websock.close()},groupId:function(t){this.group_id=this.groupId},servicerId:function(t){}},mounted:function(){},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?_(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):_(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(h.b)(["token","defaultHeadImage","defaultShopImage","defaultGoodsImage","addonIsExit","locationRegion","member"])),beforeDestroy:function(){var t=this;clearInterval(this.timeoutObj),m().then((function(e){0==e.code&&t.websock&&t.websock.close()}))},methods:{dealMessage:function(t){return v.stringToEmjoy(t)},getSiteInfo:function(){var t=this;Object(d.a)({url:"/api/site/info"}).then((function(e){e&&e.code>=0&&e.data&&(t.shopinfo=e.data)}))},getKeywords:function(){var t=this;Object(d.a)({url:"/servicer/api/chat/keyword"}).then((function(e){e.code>=0&&e.data&&(t.keyWordsConfig=e.data)}))},sendEmoji:function(t,e){this.showEmoji=!1,this.$refs.msgInputContainer.focus();var n=this.$img(e),r='<img src="'.concat(n,'" width="20" height="20">');document.execCommand("insertHTML",!1,r)},show:function(){this.is_first=!0,this.siteIdMap=this.shop.shop_id,this.is_first&&(this.currShop=this.shop),this.showService=!0,this.initWebSocket()},initData:function(){this.page=1,this.messageList.length=0,this.contentEmpty=!1},selectShop:function(t){var e=this;console.log("切换联系人"),this.initData(),this.siteIdMap=t.shop_id,this.currShop=t,this.servicerId=t.servicer_id,this.sessionList.forEach((function(n){n.shop_id==t.shop_id&&(n.unread=0,e.isRead(n))})),this.messageList=[],this.closeDialog(),this.initWebSocket(),this.getMessageList(),this.text_num=0,this.$refs.msgInputContainer.innerHTML=""},isRead:function(t){var e;console.log(t,"item"),(e={site_id:t.shop_id},Object(d.a)({url:"/servicer/api/chat/setRead",data:e})).then((function(t){})).catch((function(t){}))},test_online:function(){var t,e=this;(t={site_id:this.currShop.shop_id},Object(d.a)({url:"/servicer/api/chat/hasServicers",data:t})).then((function(t){t.data.online_count>0?(e.currShop.online=1,e.sessionList.forEach((function(t){t.shop_id==e.currShop.shop_id&&(t.online=1)}))):e.currShop.online=0}))},scrollBottom:function(){var div=document.getElementById("content");setTimeout((function(){div.scrollTop=div.scrollHeight}),0)},getMessageList:function(){var t,e=this;(t={page:this.page,limit:4,servicer_id:this.servicerId,site_id:this.siteIdMap},Object(d.a)({url:"/servicer/api/chat/dialogs",data:t})).then((function(t){var code=t.code;t.data,t.messge;if(0==code){var n=[];t.data.list.forEach((function(t){var e={};0==t.content_type?(e.content=0==t.type?t.consumer_say:t.servicer_say,e.isItMe=0==t.type,e.contentType="string",e.nickname=t.nickname,e.shop_name=t.shop_name,e.time=t.create_day+"  "+t.create_time,t.avatar?e.logo=t.avatar:e.logo=t.logo):1==t.content_type?(e.isItMe=0==t.type,e.contentType="goods",e.sku_id=t.goods_sku_id,e.nickname=t.nickname,e.shop_name=t.shop_name,e.time=t.create_day+"  "+t.create_time,t.avatar?e.logo=t.avatar:e.logo=t.logo):2==t.content_type?(e.isItMe=0==t.type,e.contentType="order",e.order_id=t.order_id,e.nickname=t.nickname,e.shop_name=t.shop_name,e.time=t.create_day+"  "+t.create_time,t.avatar?e.logo=t.avatar:e.logo=t.logo):3==t.content_type&&(e.isItMe=0==t.type,e.contentType="image",e.content=0==t.type?t.consumer_say:t.servicer_say,e.nickname=t.nickname,e.shop_name=t.shop_name,e.time=t.create_day+"  "+t.create_time,t.avatar?e.logo=t.avatar:e.logo=t.logo),n.push(e)})),e.messageList=n.concat(e.contentEmpty?[]:e.messageList),1==e.page&&(e.skuId&&e.joinNewFakeMessage("mine","goods"),e.orderId&&e.joinNewFakeMessage("mine","order"),e.scrollBottom()),e.page>=t.data.page_count?e.contentEmpty=!0:e.page+=1}}))},joinNewFakeMessage:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mine",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"string",data={isItMe:"mine"==t,contentType:e};"string"==e&&(data.content=this.message),"goods"==e&&(data.sku_id=this.skuId,data.canSend=!0),"order"==e&&(data.order_id=this.orderId,data.canSend=!0),"image"==e&&(data.content=this.image),this.messageList.unshift(data),this.scrollBottom()},joinMessage:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mine",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"string",data={isItMe:"mine"==t,contentType:e};"string"==e&&(data.content=this.message),"goods"==e&&(data.sku_id=this.skuId,data.canSend=!1),"order"==e&&(data.order_id=this.orderId,data.canSend=!1),"image"==e&&(data.content=this.image),this.messageList.push(data),this.scrollBottom()},input:function(t){var a=this.$refs.msgInputContainer.innerHTML.replace(/<\s?img.*?src\s*=\s*[\"|\']?(.*?)[\"|\']\s.*?>/gi,"0");this.text_num=a.length,this.text_num>=150&&this.$message({message:"最多一次可以发送150个字~",type:"warning"})},sendMessage:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"string",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(-1!=n&&this.messageList.splice(n,1),this.sendSwitch){this.sendSwitch=!1;var data={servicer_id:this.servicerId,site_id:this.siteIdMap},o=(r||this.$refs.msgInputContainer.innerHTML).replace(/<div>/g,""),c=o.replace(/<\/div>/g,""),l=c.replace(/<br>/g,"");if(this.message=l,"string"==e){if(!this.message.trim())return this.sendSwitch=!0,void this.$message({message:"不能发送空白内容",type:"warning"});Object.assign(data,{message:this.message,content_type:0})}"goods"==e&&Object.assign(data,{goods_id:this.skuId,content_type:1}),"order"==e&&Object.assign(data,{order_id:this.orderId,content_type:2}),"image"==e&&Object.assign(data,{message:this.image,content_type:3}),this.joinMessage("mine",e),f(data).then((function(e){t.sendSwitch=!0,t.message="",t.text_num=0,t.$refs.msgInputContainer.innerHTML=""})),this.scrollBottom()}},initWebSocket:function(){var t=o.a.webSocket;this.websock=new WebSocket(t),this.websock.onmessage=this.websocketonmessage,this.websock.onopen=this.websocketonopen,this.websock.onerror=this.websocketonerror,this.websock.onclose=this.websocketclose},websocketonopen:function(){console.log("连接建立")},websocketonerror:function(){this.initWebSocket()},websocketonmessage:function(t){var e,n=this,data=JSON.parse(t.data);if(console.log(data,"data"),this.pingInterval=o.a.pingInterval,"close"==data.type&&this.websock&&this.websock.close(),clearInterval(this.timeoutObj),this.reset(),"init"==data.type)this.messageList=[],(e={client_id:data.data.client_id},Object(d.a)({url:"/servicer/api/chat/bind",data:e})).then((function(t){var code=t.code,data=t.data,e=(t.message,{});0==code?(n.servicerId=data.servicer_id,e={contentType:"online"},n.isFirstInit&&n.initData(),n.isFirstInit=!1):e={contentType:"noline"},n.messageList.unshift(e)})).catch((function(t){n.messageList.unshift({contentType:"noline"})}));else if("connect"==data.type){if(data.data.shop_id==this.siteIdMap)if(data.data.shop_id){this.servicerId=data.servicer_id;var r={contentType:"online"};this.messageList.unshift(r)}else{this.servicerId=0;r={contentType:"noline"};this.messageList.unshift(r)}}else{var c={isItMe:!1};"string"==data.type?(c.content=data.data.servicer_say,c.time=data.data.create_day+data.data.create_time,c.logo=data.data.avatar,c.nickname=data.data.nickname,c.contentType="string"):"order"==data.type?(c.order_id=data.data.order_id,c.time=data.data.create_day+data.data.create_time,c.logo=data.data.avatar,c.nickname=data.data.nickname,c.contentType="order"):"goodssku"==data.type?(c.sku_id=data.data.goods_sku_id,c.time=data.data.create_day+data.data.create_time,c.logo=data.data.avatar,c.nickname=data.data.nickname,c.contentType="goods"):"image"==data.type&&(c.content=data.data.servicer_say,c.time=data.data.create_day+data.data.create_time,c.logo=data.data.avatar,c.nickname=data.data.nickname,c.contentType="image"),this.messageList.push(c)}this.scrollBottom()},reset:function(){console.log("检测心跳"),console.log(this.timeoutObj,"this.timeoutObj"),clearInterval(this.timeoutObj),this.start()},start:function(){var t=this;0!=o.a.pingInterval&&(console.log("启动心跳"),this.timeoutObj=setInterval((function(){console.log("检测..."),t.websock.send("ping")}),this.pingInterval))},websocketsend:function(t){this.websock.send(t)},websocketclose:function(t){console.log("断开连接",t)},closeDialog:function(){var t=this;clearInterval(this.timeoutObj),console.log("关闭链接"),this.websock.close();var data={servicer_id:this.servicerId,site_id:this.siteIdMap};this.servicerId&&this.websock&&m(data).then((function(e){0==e.code&&clearInterval(t.timeoutObj)}))},handleAvatarSuccess:function(t,e){this.image=t.data.pic_path,this.sendMessage("image")}}},O=y,j=(n(418),n(419),n(6)),component=Object(j.a)(O,(function(){var t=this,e=t._self._c;return e("el-dialog",{staticClass:"service",attrs:{visible:t.showService,width:"48%","append-to-body":!0},on:{"update:visible":function(e){t.showService=e},close:t.closeDialog}},[e("div",{staticClass:"header-box ns-bg-color"},[e("div",{staticClass:"header-logo"},[e("img",{staticClass:"header-img",attrs:{src:n(412)}}),t._v(" "),e("span",{staticClass:"serve-name"},[t._v("客服咨询")])])]),t._v(" "),e("div",{staticClass:"service-box"},[e("div",{staticClass:"service-box-right"},[e("div",{staticClass:"service-box-right-top test-1"},[e("div",{staticClass:"servicer-content",attrs:{id:"content"}},[t.contentEmpty?e("div",{staticClass:"text-center"},[t._v("没有聊天记录了")]):e("div",{staticClass:"time text-center",staticStyle:{cursor:"pointer"},on:{click:t.getMessageList}},[t._v("点击加载")]),t._v(" "),t._l(t.messageList,(function(n,r){return e("div",{key:r},[e("div",["online"==n.contentType?e("div",{staticClass:"onlineStatus"},[t._v("客服在线")]):t._e(),t._v(" "),"noline"==n.contentType?e("div",{staticClass:"onlineStatus"},[t._v("客服不在线")]):t._e()]),t._v(" "),n.isItMe?e("div",{staticClass:"message my-message"},[e("div",[e("div",{staticClass:"my-nickname"},[t._v(t._s(t.member.nickname))]),t._v(" "),"goods"==n.contentType?e("goodsItem",{attrs:{skuId:n.sku_id},on:{sendMessage:function(e){return t.sendMessage("goods",r)}}}):t._e(),t._v(" "),"order"==n.contentType?e("orderItem",{attrs:{orderId:n.order_id},on:{sendMessage:function(e){return t.sendMessage("order",r)}}}):t._e(),t._v(" "),"string"==n.contentType?e("div",{staticClass:"word-message",domProps:{innerHTML:t._s(t.dealMessage(n.content))}}):t._e(),t._v(" "),"image"==n.contentType?e("div",{staticClass:"word-message"},[e("el-image",{attrs:{src:t.$util.img(n.content),"preview-src-list":[t.$util.img(n.content)]}})],1):t._e()],1),t._v(" "),e("div",{staticClass:"headimg",style:{backgroundImage:"url("+t.$util.img(t.member.headimg?t.member.headimg:t.defaultHeadImage)+")"}})]):!1===n.isItMe?e("div",{staticClass:"message your-message"},[e("div",{staticClass:"headimg",style:{backgroundImage:"url("+t.$util.img(t.shopinfo.logo)+")"}}),t._v(" "),e("div",[e("div",{staticClass:"my-nickname"},[t._v(t._s(n.nickname)+" "),e("span",[t._v(t._s(n.time))])]),t._v(" "),"image"==n.contentType?e("div",{staticClass:"word-message your-word-message"},[e("el-image",{attrs:{src:t.$util.img(n.content),"preview-src-list":[t.$util.img(n.content)]}})],1):t._e(),t._v(" "),"goods"==n.contentType?e("goodsItem",{attrs:{skuId:n.sku_id},on:{sendMessage:function(e){return t.sendMessage("goods",r)}}}):t._e(),t._v(" "),"order"==n.contentType?e("orderItem",{attrs:{orderId:n.order_id},on:{sendMessage:function(e){return t.sendMessage("order",r)}}}):t._e(),t._v(" "),"string"==n.contentType?e("div",{staticClass:"word-message your-word-message",domProps:{innerHTML:t._s(t.dealMessage(n.content))}}):t._e()],1)]):t._e()])})),t._v(" "),e("div",{staticStyle:{height:"40px"}})],2)]),t._v(" "),e("div",{staticClass:"service-box-right-bottom"},[1==t.keyWordsConfig.is_open?e("div",{staticClass:"key-word"},t._l(t.keyWordsConfig.keyword_list,(function(n,r){return e("span",{key:r,on:{click:function(e){return t.sendMessage("string",-1,n.keyword)}}},[t._v(t._s(n.keyword))])})),0):t._e(),t._v(" "),e("div",{staticClass:"operation"},[e("img",{staticClass:"emjoy",attrs:{src:n(413)},on:{click:function(e){t.showEmoji=!t.showEmoji}}}),t._v(" "),e("el-upload",{staticClass:"upload",attrs:{action:t.uploadActionUrl,"show-file-list":!1,"on-success":t.handleAvatarSuccess}},[e("img",{staticClass:"emjoy",attrs:{src:n(414)},on:{click:function(e){t.showEmoji=!1}}})])],1),t._v(" "),t.showEmoji?e("div",{staticClass:"emoji-box"},t._l(t.emjoyList,(function(n,r){return e("img",{key:r,staticClass:"text item",staticStyle:{cursor:"pointer"},attrs:{src:t.$util.img(n)},on:{click:function(e){return t.sendEmoji(r,n)}}})})),0):t._e(),t._v(" "),e("div",{ref:"msgInputContainer",staticClass:"input-panel",class:1==t.keyWordsConfig.is_open?"":"active",attrs:{contenteditable:"true",spellcheck:"false"},on:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")||e.ctrlKey||e.shiftKey||e.altKey||e.metaKey?null:t.sendMessage()},input:function(e){return t.input(e)}}}),t._v(" "),e("div",{staticClass:"num-box"},[t._v("\n            "+t._s(t.text_num)+"/150\n          ")]),t._v(" "),e("el-button",{staticClass:"send-btn",attrs:{size:"small"},on:{click:function(e){return t.sendMessage()}}},[t._v("发送")])],1)])])])}),[],!1,null,"00ff589d",null);e.default=component.exports},22:function(t,e,n){"use strict";n.d(e,"a",(function(){return l})),n.d(e,"c",(function(){return d})),n.d(e,"b",(function(){return f}));var r=n(111),o=n.n(r),c="SDrxEA%_tWW6ezd3";function l(){return o.a.get(c)}function d(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e?o.a.set(c,t,{expires:e}):o.a.set(c,t)}function f(){return o.a.remove(c)}},258:function(t,e,n){},259:function(t,e,n){},268:function(t,e,n){},269:function(t,e,n){},27:function(t,e,n){"use strict";n.d(e,"l",(function(){return o})),n.d(e,"c",(function(){return c})),n.d(e,"j",(function(){return l})),n.d(e,"i",(function(){return d})),n.d(e,"k",(function(){return f})),n.d(e,"a",(function(){return m})),n.d(e,"g",(function(){return h})),n.d(e,"d",(function(){return v})),n.d(e,"f",(function(){return _})),n.d(e,"b",(function(){return y})),n.d(e,"e",(function(){return O})),n.d(e,"h",(function(){return j}));var r=n(1);function o(t){return Object(r.a)({url:"/api/site/info",data:t})}function c(t){return Object(r.a)({url:"/api/config/copyright",data:t})}function l(t){return Object(r.a)({url:"/api/site/wapqrcode",data:t})}function d(t){return Object(r.a)({url:"/api/config/defaultimg",data:t})}function f(t){return Object(r.a)({url:"weapp/api/weapp/qrcode",data:t})}function m(t){return Object(r.a)({url:"/api/adv/detail",data:t})}function h(t){return Object(r.a)({url:"/api/goods/service",data:t})}function v(t){return Object(r.a)({url:"/api/pc/friendlyLink",data:t})}function _(t){return Object(r.a)({url:"/api/pc/navList",data:t})}function y(t){return Object(r.a)({url:"/api/captcha/captcha",data:t})}function O(){return Object(r.a)({url:"/api/site/status"})}function j(t){return Object(r.a)({url:"/api/config/servicer",data:t})}},270:function(t,e,n){"use strict";n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return c})),n.d(e,"b",(function(){return l}));var r=n(1);function o(t){return Object(r.a)({url:"/api/pc/floors",data:t})}function c(t){return Object(r.a)({url:"/api/goods/defaultSearchWords",data:t})}function l(t){return Object(r.a)({url:"/api/pc/floatLayer",data:t})}},271:function(t,e,n){},272:function(t,e,n){},273:function(t,e,n){},274:function(t,e,n){},275:function(t,e,n){},276:function(t,e,n){},277:function(t,e,n){},278:function(t,e,n){},279:function(t,e,n){},280:function(t,e,n){},281:function(t,e,n){},282:function(t,e,n){},283:function(t,e,n){},284:function(t,e,n){},285:function(t,e,n){},312:function(t,e,n){"use strict";var r=n(21),o=(n(95),n(7),n(73),n(2)),c=n(0),l=window.__NUXT__;function d(){if(!this._hydrated)return this.$fetch()}function f(){if((t=this).$vnode&&t.$vnode.elm&&t.$vnode.elm.dataset&&t.$vnode.elm.dataset.fetchKey){var t;this._hydrated=!0,this._fetchKey=this.$vnode.elm.dataset.fetchKey;var data=l.fetch[this._fetchKey];if(data&&data._error)this.$fetchState.error=data._error;else for(var e in data)o.default.set(this.$data,e,data[e])}}function m(){var t=this;return this._fetchPromise||(this._fetchPromise=h.call(this).then((function(){delete t._fetchPromise}))),this._fetchPromise}function h(){return v.apply(this,arguments)}function v(){return(v=Object(r.a)(regeneratorRuntime.mark((function t(){var e,n,r,o=this;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.$nuxt.nbFetching++,this.$fetchState.pending=!0,this.$fetchState.error=null,this._hydrated=!1,e=null,n=Date.now(),t.prev=6,t.next=9,this.$options.fetch.call(this);case 9:t.next=15;break;case 11:t.prev=11,t.t0=t.catch(6),e=Object(c.p)(t.t0);case 15:if(!((r=this._fetchDelay-(Date.now()-n))>0)){t.next=19;break}return t.next=19,new Promise((function(t){return setTimeout(t,r)}));case 19:this.$fetchState.error=e,this.$fetchState.pending=!1,this.$fetchState.timestamp=Date.now(),this.$nextTick((function(){return o.$nuxt.nbFetching--}));case 23:case"end":return t.stop()}}),t,this,[[6,11]])})))).apply(this,arguments)}e.a={beforeCreate:function(){Object(c.l)(this)&&(this._fetchDelay="number"==typeof this.$options.fetchDelay?this.$options.fetchDelay:200,o.default.util.defineReactive(this,"$fetchState",{pending:!1,error:null,timestamp:Date.now()}),this.$fetch=m.bind(this),Object(c.a)(this,"created",f),Object(c.a)(this,"beforeMount",d))}}},313:function(t,e,n){"use strict";n.r(e);n(24),n(25),n(23),n(7),n(29),n(18),n(30);var r=n(10),o=n(206),c=n(12);function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var d={name:"goods_item",props:{skuId:0},data:function(){return{goodsInfo:{}}},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(c.b)(["defaultGoodsImage"])),created:function(){this.skuId&&this.getGoodsInfo()},methods:{sendMessage:function(){this.$emit("sendMessage")},jump_shop:function(){this.$util.pushToTab("/sku/"+this.goodsInfo.sku_id)},getGoodsInfo:function(){var t=this;Object(o.f)({sku_id:this.skuId}).then((function(e){e.code>=0&&(t.goodsInfo=e.data.goods_sku_detail)}))}}},f=d,m=(n(416),n(6)),component=Object(m.a)(f,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"goods-info"},[e("div",{staticClass:"goods-box"},[e("div",{staticClass:"goods-img"},[e("img",{attrs:{src:t.goodsInfo.sku_image?t.$util.img(t.goodsInfo.sku_image):t.$util.img(t.defaultGoodsImage),alt:""}})]),t._v(" "),e("div",{staticClass:"goods-desc"},[e("div",{staticClass:"text-hidden-two-row"},[t._v(t._s(t.goodsInfo.sku_name))]),t._v(" "),e("div",{staticClass:"text-hidden-two-row"},[e("span",{staticClass:"sale-num"},[t._v("库存："+t._s(t.goodsInfo.stock))]),t._v(" "),e("span",{staticClass:"sale-num"},[t._v("销量："+t._s(t.goodsInfo.sale_num))])]),t._v(" "),e("div",{staticClass:"price-box"},[e("span",{staticClass:"sale-num price-num"},[t._v("¥"+t._s(t.goodsInfo.price))]),t._v(" "),e("span",{on:{click:function(e){return t.jump_shop()}}},[t._v("查看商品"),e("i",{staticClass:"el-icon-arrow-right"})])])])])])}),[],!1,null,"b4e40c54",null);e.default=component.exports},314:function(t,e,n){"use strict";n.r(e);n(24),n(25),n(23),n(7),n(29),n(18),n(30);var r=n(10),o=n(204),c=n(12);function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var d={name:"orderItem",props:{orderId:0,canSend:{type:Boolean,default:!1}},data:function(){return{orderInfo:{order_status_name:"",delivery_type_name:"",order_money:"",order_goods:[{sku_image:""}]}}},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(c.b)(["defaultGoodsImage"])),created:function(){this.orderId&&this.getOrderInfo()},methods:{sendMessage:function(){this.$emit("sendMessage")},jumo_order:function(){this.$util.pushToTab("/member/order_detail?order_id="+this.orderId)},getOrderInfo:function(){var t=this;Object(o.c)({order_id:this.orderId}).then((function(e){e.code>=0&&(t.orderInfo=e.data)}))}}},f=d,m=(n(417),n(6)),component=Object(m.a)(f,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"goods-info"},[e("div",{staticClass:"goods-box"},[e("div",{staticClass:"goods-img"},[e("img",{attrs:{src:t.orderInfo.order_goods[0].sku_image?t.$util.img(t.orderInfo.order_goods[0].sku_image):t.$util.img(t.defaultGoodsImage),alt:""}})]),t._v(" "),e("div",{staticClass:"goods-desc"},[e("div",{staticClass:"text-hidden-two-row"},[t._v(t._s(t.orderInfo.order_name))]),t._v(" "),e("div",[t._v("订单状态："+t._s(t.orderInfo.order_status_name))]),t._v(" "),e("div",{staticClass:"flex-box"},[e("span",{staticClass:"ns-text-color"},[t._v("¥"+t._s(t.orderInfo.order_money))]),t._v(" "),e("span",[t._v(t._s(t.orderInfo.delivery_type_name))])])])]),t._v(" "),e("div",{staticClass:"more text-center"},[e("div",{staticClass:"order-num"},[t._v("订单号："+t._s(t.orderInfo.order_no))]),t._v(" "),e("div",{staticClass:"see-order",on:{click:function(e){return t.jumo_order()}}},[t._v("\n\t\t\t查看订单"),e("i",{staticClass:"el-icon-arrow-right"})])])])}),[],!1,null,"50f16487",null);e.default=component.exports},33:function(t,e,n){"use strict";var r={baseUrl:"{{$baseUrl}}",imgDomain:"{{$imgDomain}}",webDomain:"{{$webDomain}}",mpKey:"{{$mpKey}}",webSocket:"{{$webSocket}}",pingInterval:1500,version:"5.5.0"};n(2).default.prototype.$config=r,e.a=r},331:function(t,e,n){t.exports=n.p+"img/closed.2b60712.png"},332:function(t,e,n){n(333),t.exports=n(334)},334:function(t,e,n){"use strict";n.r(e),function(t){n(65),n(83),n(25),n(84),n(85);var e=n(15),r=n(21),o=(n(163),n(348),n(362),n(363),n(95),n(56),n(7),n(18),n(23),n(24),n(107),n(132),n(92),n(75),n(31),n(48),n(50),n(73),n(2)),c=n(305),l=n(200),d=n(0),f=n(55),m=n(312),h=n(150);function v(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return _(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,c=!0,l=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return c=t.done,t},e:function(t){l=!0,o=t},f:function(){try{c||null==n.return||n.return()}finally{if(l)throw o}}}}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}o.default.__nuxt__fetch__mixin__||(o.default.mixin(m.a),o.default.__nuxt__fetch__mixin__=!0),o.default.component(h.a.name,h.a),o.default.component("NLink",h.a),t.fetch||(t.fetch=c.a);var y,O,j=[],w=window.__NUXT__||{},x=w.config||{};x._app&&(n.p=Object(d.v)(x._app.cdnURL,x._app.assetsPath)),Object.assign(o.default.config,{silent:!0,performance:!1});var C=o.default.config.errorHandler||console.error;function k(t,e,n){for(var r=function(component){var t=function(component,t){if(!component||!component.options||!component.options[t])return{};var option=component.options[t];if("function"==typeof option){for(var e=arguments.length,n=new Array(e>2?e-2:0),r=2;r<e;r++)n[r-2]=arguments[r];return option.apply(void 0,n)}return option}(component,"transition",e,n)||{};return"string"==typeof t?{name:t}:t},o=n?Object(d.g)(n):[],c=Math.max(t.length,o.length),l=[],f=function(){var e=Object.assign({},r(t[i])),n=Object.assign({},r(o[i]));Object.keys(e).filter((function(t){return void 0!==e[t]&&!t.toLowerCase().includes("leave")})).forEach((function(t){n[t]=e[t]})),l.push(n)},i=0;i<c;i++)f();return l}function S(t,e,n){return E.apply(this,arguments)}function E(){return(E=Object(r.a)(regeneratorRuntime.mark((function t(e,n,r){var o,c,l,f,m=this;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._routeChanged=Boolean(y.nuxt.err)||n.name!==e.name,this._paramChanged=!this._routeChanged&&n.path!==e.path,this._queryChanged=!this._paramChanged&&n.fullPath!==e.fullPath,this._diffQuery=this._queryChanged?Object(d.i)(e.query,n.query):[],(this._routeChanged||this._paramChanged)&&this.$loading.start&&!this.$loading.manual&&this.$loading.start(),t.prev=5,!this._queryChanged){t.next=12;break}return t.next=9,Object(d.r)(e,(function(t,e){return{Component:t,instance:e}}));case 9:o=t.sent,o.some((function(t){var r=t.Component,o=t.instance,c=r.options.watchQuery;return!0===c||(Array.isArray(c)?c.some((function(t){return m._diffQuery[t]})):"function"==typeof c&&c.apply(o,[e.query,n.query]))}))&&this.$loading.start&&!this.$loading.manual&&this.$loading.start();case 12:r(),t.next=26;break;case 15:if(t.prev=15,t.t0=t.catch(5),c=t.t0||{},l=c.statusCode||c.status||c.response&&c.response.status||500,f=c.message||"",!/^Loading( CSS)? chunk (\d)+ failed\./.test(f)){t.next=23;break}return window.location.reload(!0),t.abrupt("return");case 23:this.error({statusCode:l,message:f}),this.$nuxt.$emit("routeChanged",e,n,c),r();case 26:case"end":return t.stop()}}),t,this,[[5,15]])})))).apply(this,arguments)}function T(t,e){return w.serverRendered&&e&&Object(d.b)(t,e),t._Ctor=t,t}function I(t){return Object(d.d)(t,function(){var t=Object(r.a)(regeneratorRuntime.mark((function t(e,n,r,o,c){var l;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("function"!=typeof e||e.options){t.next=4;break}return t.next=3,e();case 3:e=t.sent;case 4:return l=T(Object(d.s)(e),w.data?w.data[c]:null),r.components[o]=l,t.abrupt("return",l);case 7:case"end":return t.stop()}}),t)})));return function(e,n,r,o,c){return t.apply(this,arguments)}}())}function $(t,e,n){var r=this,o=[],c=!1;if(void 0!==n&&(o=[],(n=Object(d.s)(n)).options.middleware&&(o=o.concat(n.options.middleware)),t.forEach((function(t){t.options.middleware&&(o=o.concat(t.options.middleware))}))),o=o.map((function(t){return"function"==typeof t?t:("function"!=typeof l.a[t]&&(c=!0,r.error({statusCode:500,message:"Unknown middleware "+t})),l.a[t])})),!c)return Object(d.o)(o,e)}function P(t,e,n){return A.apply(this,arguments)}function A(){return A=Object(r.a)(regeneratorRuntime.mark((function t(e,n,o){var c,l,m,h,_,O,w,x,C,S,E,T,I,P,A,R=this;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!1!==this._routeChanged||!1!==this._paramChanged||!1!==this._queryChanged){t.next=2;break}return t.abrupt("return",o());case 2:return!1,e===n?(j=[],!0):(c=[],j=Object(d.g)(n,c).map((function(t,i){return Object(d.c)(n.matched[c[i]].path)(n.params)}))),l=!1,m=function(path){n.path===path.path&&R.$loading.finish&&R.$loading.finish(),n.path!==path.path&&R.$loading.pause&&R.$loading.pause(),l||(l=!0,o(path))},t.next=8,Object(d.t)(y,{route:e,from:n,next:m.bind(this)});case 8:if(this._dateLastError=y.nuxt.dateErr,this._hadError=Boolean(y.nuxt.err),h=[],(_=Object(d.g)(e,h)).length){t.next=27;break}return t.next=15,$.call(this,_,y.context);case 15:if(!l){t.next=17;break}return t.abrupt("return");case 17:return O=(f.a.options||f.a).layout,t.next=20,this.loadLayout("function"==typeof O?O.call(f.a,y.context):O);case 20:return w=t.sent,t.next=23,$.call(this,_,y.context,w);case 23:if(!l){t.next=25;break}return t.abrupt("return");case 25:return y.context.error({statusCode:404,message:"This page could not be found"}),t.abrupt("return",o());case 27:return _.forEach((function(t){t._Ctor&&t._Ctor.options&&(t.options.asyncData=t._Ctor.options.asyncData,t.options.fetch=t._Ctor.options.fetch)})),this.setTransitions(k(_,e,n)),t.prev=29,t.next=32,$.call(this,_,y.context);case 32:if(!l){t.next=34;break}return t.abrupt("return");case 34:if(!y.context._errored){t.next=36;break}return t.abrupt("return",o());case 36:return"function"==typeof(x=_[0].options.layout)&&(x=x(y.context)),t.next=40,this.loadLayout(x);case 40:return x=t.sent,t.next=43,$.call(this,_,y.context,x);case 43:if(!l){t.next=45;break}return t.abrupt("return");case 45:if(!y.context._errored){t.next=47;break}return t.abrupt("return",o());case 47:C=!0,t.prev=48,S=v(_),t.prev=50,S.s();case 52:if((E=S.n()).done){t.next=63;break}if("function"==typeof(T=E.value).options.validate){t.next=56;break}return t.abrupt("continue",61);case 56:return t.next=58,T.options.validate(y.context);case 58:if(C=t.sent){t.next=61;break}return t.abrupt("break",63);case 61:t.next=52;break;case 63:t.next=68;break;case 65:t.prev=65,t.t0=t.catch(50),S.e(t.t0);case 68:return t.prev=68,S.f(),t.finish(68);case 71:t.next=77;break;case 73:return t.prev=73,t.t1=t.catch(48),this.error({statusCode:t.t1.statusCode||"500",message:t.t1.message}),t.abrupt("return",o());case 77:if(C){t.next=80;break}return this.error({statusCode:404,message:"This page could not be found"}),t.abrupt("return",o());case 80:return t.next=82,Promise.all(_.map(function(){var t=Object(r.a)(regeneratorRuntime.mark((function t(r,i){var o,c,l,f,m,v,_,O,p;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r._path=Object(d.c)(e.matched[h[i]].path)(e.params),r._dataRefresh=!1,o=r._path!==j[i],R._routeChanged&&o?r._dataRefresh=!0:R._paramChanged&&o?(c=r.options.watchParam,r._dataRefresh=!1!==c):R._queryChanged&&(!0===(l=r.options.watchQuery)?r._dataRefresh=!0:Array.isArray(l)?r._dataRefresh=l.some((function(t){return R._diffQuery[t]})):"function"==typeof l&&(I||(I=Object(d.h)(e)),r._dataRefresh=l.apply(I[i],[e.query,n.query]))),R._hadError||!R._isMounted||r._dataRefresh){t.next=6;break}return t.abrupt("return");case 6:return f=[],m=r.options.asyncData&&"function"==typeof r.options.asyncData,v=Boolean(r.options.fetch)&&r.options.fetch.length,_=m&&v?30:45,m&&((O=Object(d.q)(r.options.asyncData,y.context)).then((function(t){Object(d.b)(r,t),R.$loading.increase&&R.$loading.increase(_)})),f.push(O)),R.$loading.manual=!1===r.options.loading,v&&((p=r.options.fetch(y.context))&&(p instanceof Promise||"function"==typeof p.then)||(p=Promise.resolve(p)),p.then((function(t){R.$loading.increase&&R.$loading.increase(_)})),f.push(p)),t.abrupt("return",Promise.all(f));case 14:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()));case 82:l||(this.$loading.finish&&!this.$loading.manual&&this.$loading.finish(),o()),t.next=99;break;case 85:if(t.prev=85,t.t2=t.catch(29),"ERR_REDIRECT"!==(P=t.t2||{}).message){t.next=90;break}return t.abrupt("return",this.$nuxt.$emit("routeChanged",e,n,P));case 90:return j=[],Object(d.k)(P),"function"==typeof(A=(f.a.options||f.a).layout)&&(A=A(y.context)),t.next=96,this.loadLayout(A);case 96:this.error(P),this.$nuxt.$emit("routeChanged",e,n,P),o();case 99:case"end":return t.stop()}}),t,this,[[29,85],[48,73],[50,65,68,71]])}))),A.apply(this,arguments)}function R(t,n){Object(d.d)(t,(function(t,n,r,c){return"object"!==Object(e.a)(t)||t.options||((t=o.default.extend(t))._Ctor=t,r.components[c]=t),t}))}function L(t){var e=Boolean(this.$options.nuxt.err);this._hadError&&this._dateLastError===this.$options.nuxt.dateErr&&(e=!1);var n=e?(f.a.options||f.a).layout:t.matched[0].components.default.options.layout;"function"==typeof n&&(n=n(y.context)),this.setLayout(n)}function D(t){t._hadError&&t._dateLastError===t.$options.nuxt.dateErr&&t.error()}function N(t,e){var n=this;if(!1!==this._routeChanged||!1!==this._paramChanged||!1!==this._queryChanged){var r=Object(d.h)(t),c=Object(d.g)(t),l=!1;o.default.nextTick((function(){r.forEach((function(t,i){if(t&&!t._isDestroyed&&t.constructor._dataRefresh&&c[i]===t.constructor&&!0!==t.$vnode.data.keepAlive&&"function"==typeof t.constructor.options.data){var e=t.constructor.options.data.call(t);for(var n in e)o.default.set(t.$data,n,e[n]);l=!0}})),l&&window.$nuxt.$nextTick((function(){window.$nuxt.$emit("triggerScroll")})),D(n)}))}}function M(t){window.onNuxtReadyCbs.forEach((function(e){"function"==typeof e&&e(t)})),"function"==typeof window._onNuxtLoaded&&window._onNuxtLoaded(t),O.afterEach((function(e,n){o.default.nextTick((function(){return t.$nuxt.$emit("routeChanged",e,n)}))}))}function U(){return(U=Object(r.a)(regeneratorRuntime.mark((function t(e){var n,r,c,l;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return y=e.app,O=e.router,e.store,n=new o.default(y),r=function(){n.$mount("#__nuxt"),O.afterEach(R),O.afterEach(L.bind(n)),O.afterEach(N.bind(n)),o.default.nextTick((function(){M(n)}))},t.next=7,Promise.all(I(y.context.route));case 7:if(c=t.sent,n.setTransitions=n.$options.nuxt.setTransitions.bind(n),c.length&&(n.setTransitions(k(c,O.currentRoute)),j=O.currentRoute.matched.map((function(t){return Object(d.c)(t.path)(O.currentRoute.params)}))),n.$loading={},w.error&&n.error(w.error),O.beforeEach(S.bind(n)),O.beforeEach(P.bind(n)),!w.serverRendered||!Object(d.n)(w.routePath,n.context.route.path)){t.next=16;break}return t.abrupt("return",r());case 16:return l=function(){R(O.currentRoute,O.currentRoute),L.call(n,O.currentRoute),D(n),r()},t.next=19,new Promise((function(t){return setTimeout(t,0)}));case 19:P.call(n,O.currentRoute,O.currentRoute,(function(path){if(path){var t=O.afterEach((function(e,n){t(),l()}));O.push(path,void 0,(function(t){t&&C(t)}))}else l()}));case 20:case"end":return t.stop()}}),t)})))).apply(this,arguments)}Object(f.b)(null,w.config).then((function(t){return U.apply(this,arguments)})).catch(C)}.call(this,n(43))},369:function(t,e,n){"use strict";n.r(e);var r=n(22);e.default=function(t){Object(r.a)()||t.redirect(302,{path:"/auth/login?redirect=".concat(encodeURIComponent(t.route.fullPath))})}},380:function(t,e,n){"use strict";n(258)},381:function(t,e,n){"use strict";n(259)},382:function(t,e,n){},383:function(t,e,n){},384:function(t,e,n){t.exports=n.p+"img/gov_record.c774173.png"},403:function(t,e,n){"use strict";n(268)},404:function(t,e,n){"use strict";n(269)},405:function(t,e,n){"use strict";n(271)},406:function(t,e,n){"use strict";n(272)},407:function(t,e,n){t.exports=n.p+"img/index_card.36aa731.png"},408:function(t,e,n){t.exports=n.p+"img/index_service.e766525.png"},409:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAoxJREFUaEPtlz2r01AYx58nhmRxEYS7KeLo4hsiOPhCFz/BXRUuJ20qdnJzCCgO0rsYLMkZnHSpH8HW4SLIFS74AeqmdROEOrQ1eSTSXnJrXs5JTqS9pGObc/L7Pc8/T08QNvyDG84PRwQ6nc7WdDq9CwDmOoqFYUi6rn/q9Xqfl3yHAo7jaOPxeAQA59YRPsYU6Lp+dSlxKGDb9skgCH4SkbbmAqBp2rbnef2I80iEGGMPAaCJiGsZISIiRPxoGMaO67rTfwTWvfJJfMdrCtUdiFWg3W5fmM/nzwBgCwB2OedvqyhQJRFqtVpXwjB8R0SnYtAdzvkL1RLKBVLgl9zKJZQK5MBXIqFMQBBeuYQSAUl4pRKlBVLgozPVEACsBe0BIo6IaHvlIS79TJQSSIM3TfP2bDa7R0RPFsD7jUbjxnA4fKNaorBAFrzrul8ty3ocF+CcX+/3+ydUSxQSyIOPqp4kEH2vWkJaQAQ+S0C1hJSAKHyegEoJYQEZeBEBVRJCArLwogIqJHIFisDLCJSVyBQoCi8rUEYiVcC27YtBELxfORKPoj+paM7nHYvTxmjWuowR+4Bz/lLqldKyrH0iuhZb9MU0zVsi8EU6sLxPkgQi/iaiM5zz76sSqR1gjB0AwOXFAin4MgJJcULE0DCMs0nFSxVoNpuXwjDcBYAfmqZ1PM/7lheb+O9FIhRfH3ViMBg8RcQ7iOh7nvdKKkIysEnXlhUQvX/uGBXdaPW6WkCwcnUH0gpVR6iOkGAF6giVLFQ9heoI1REqWYFjGyHG2CMAeB4JIuKe7/s3q6hVZVOIMXYeET8AwGkAuO/7/uuNEohgHcfRJ5OJ2e12f1UB/7e7VW38v/bdeIE/9tiBTxkFbdgAAAAASUVORK5CYII="},410:function(t,e,n){t.exports=n.p+"img/index_code.d6eca04.png"},411:function(t,e,n){t.exports=n.p+"img/index_member.268be89.png"},412:function(t,e,n){t.exports=n.p+"img/kefupng.5695537.png"},413:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAADIUlEQVRIibXWXYxdUxQH8N89HZOSvnRS0Sci/VAJgraU0ki8SMVXJaipVNSDJx8dQvVhSKiJ0gpeBA061McDKdo03lRUMGnSEBOdRniSoo0YaSPTqYe1jnvnZO65RnUlJ3vvddb6/8/Za++1VuO5F17SQRq4EtdiKeZiZr47jBF8jo+wG8frwLpq3hW4E+uSZDI5I5+leCjJn8JrGJ8K4dl4B4tbdGMYwj78im7MxnxclFhz8Sruwa344d8QXoX3MCvXBzGA13GozQf2YDUeFn+8GF/iZnzaalhUHK/BriQ7jk2Yg801ZPLdZvGHm9J3Fj5JzEkJz8k/68YR9KIPozVEVRlNn97E6E7MBVXCadiKGSLYt2HbFIiqsi0xxhNza3L8Q7ha84BsxPYTICtlO57O+SJx4hXinq3LFz/hsf+BrJTH8WPOH0GjwDLNe/YsjlacnsQfObaTdjZHxSGSHMsKLE/FGAYnAXtQxOHeGsI6m8HEhuUFLs/FkMmP/jP4E8/XENbZHEpsuKzAvFzsawO2Pr9+fQ1hJ5sSe36hmYh/qQE8USmxZ1YzzUmXQpQYOP0k8pTYhwvsz8UFFaOvRAJuTBH8ZbxY0Z2f4/cF9uRiocj6pcwW2ae1RHWSC3E3VrboekSmgT0FPs5FF1a1GL6d4waZBztIA0/k/N0Wfa9mGdxRiHo1koo+TM/5RnG6rsYbOK2G7FS8ItqQ30VKk1h9OR/B7kLUroFUnon+nB/ECpGybsd3WItzRdk5RVT7+/At7hKXfwV+Tox+nJXzAYw3somahi/EXo/jJs2KcR626BzLvViTI1yP98VNGMKlOFbew2O4QxTQQsSvDPw3WJLbNYgDorgeET3LW7ghP7YkW5kYRWKuSo4JPc0wbsEHIiZvJkh/Ou3Ip05miPg9IA7RX4k5XBpUM81O3JgEDRGz/QnQo7304P60XZu+o4m1s9Ww0aYRXiC2b2GLbgxfi0T8m2abOA8Xm7hbQ2Ibh1WkXV86jEtEW/Co6Ny6RCyXtPEh4rvBf2iEpcOWdL4C14naOcfEVv+AaPU/xGftiEr5G0ePtx+RSttCAAAAAElFTkSuQmCC"},414:function(t,e,n){t.exports=n.p+"img/upload.65b6dc8.png"},416:function(t,e,n){"use strict";n(273)},417:function(t,e,n){"use strict";n(274)},418:function(t,e,n){"use strict";n(275)},419:function(t,e,n){"use strict";n(276)},420:function(t,e,n){"use strict";n(277)},423:function(t,e,n){"use strict";n(278)},424:function(t,e,n){"use strict";n(279)},425:function(t,e,n){"use strict";n(280)},426:function(t,e,n){"use strict";n(281)},427:function(t,e,n){"use strict";n(282)},428:function(t,e,n){"use strict";n(283)},429:function(t,e,n){"use strict";n(284)},430:function(t,e,n){"use strict";n(285)},431:function(t,e,n){"use strict";n.r(e);n(7);var r=n(208),o={SET_CITY:function(t,e){t.city=e},SET_LANG:function(t,e){t.lang=e},SET_FLOAT_LAYER:function(t,e){t.indexFloatLayerNum=e,localStorage.setItem("indexFloatLayerNum",e)},SET_INDEXTOPADNUM:function(t,e){t.indexTopAdNum=e},SET_LOCATION_REGION:function(t,e){t.locationRegion=e},IS_SHOW_JUDGE:function(t,e){t.is_show=e}},c={setCity:function(t,e){(0,t.commit)("SET_CITY",e)},lang:function(t,e){(0,t.commit)("SET_LANG",e)},get_city:function(t,e){t.commit;return new Promise((function(t,e){return Object(r.b)({}).then((function(e){t(e)})).catch((function(t){e(t)}))}))},is_show:function(t,e){(0,t.commit)("IS_SHOW_JUDGE",e.is_show)}};e.default={namespaced:!0,state:{city:"",lang:"zh-cn",indexFloatLayerNum:0,indexTopAdNum:0,locationRegion:null,is_show:!1},mutations:o,actions:c}},432:function(t,e,n){"use strict";n.r(e);n(7);var r=n(62),o={add_to_cart:function(t,e){var n=t.commit;return new Promise((function(t,o){return Object(r.a)({site_id:e.site_id,num:e.num||1,sku_id:e.sku_id}).then((function(e){Object(r.e)({}).then((function(t){n("SET_CART_COUNT",t.data)})),t(e)})).catch((function(t){o(t)}))}))},delete_cart:function(t,e){var n=t.commit;return new Promise((function(t,o){return Object(r.c)({cart_id:e.cart_id}).then((function(e){Object(r.e)({}).then((function(t){n("SET_CART_COUNT",t.data)})),t(e)})).catch((function(t){o(t)}))}))},cart_count:function(t,e){var n=t.commit;if(this.state.member.token)return new Promise((function(t,e){return Object(r.e)({}).then((function(e){n("SET_CART_COUNT",e.data),t(e)})).catch((function(t){e(t)}))}))},edit_cart_num:function(t,e){var n=t.commit;return new Promise((function(t,o){return Object(r.d)({num:e.num,cart_id:e.cart_id}).then((function(e){Object(r.e)({}).then((function(t){n("SET_CART_COUNT",t.data)})),t(e)})).catch((function(t){o(t)}))}))}};e.default={namespaced:!0,state:{cartCount:0},mutations:{SET_CART_COUNT:function(t,e){t.cartCount=e}},actions:o}},433:function(t,e,n){"use strict";n.r(e);var r={token:function(t){return t.member.token},lang:function(t){return t.app.lang},city:function(t){return t.app.city},locationRegion:function(t){return t.app.locationRegion},autoLoginRange:function(t){return t.member.autoLoginRange},wapQrcode:function(t){return t.site.siteQrCode},member:function(t){return t.member.member},copyRight:function(t){return t.site.copyRight},siteInfo:function(t){return t.site.siteInfo},addonIsExit:function(t){return t.site.addons},cartCount:function(t){return t.cart.cartCount},is_show:function(t){return t.app.is_show},defaultGoodsImage:function(t){return t.site.defaultFiles.goods},defaultHeadImage:function(t){return t.site.defaultFiles.head},defaultShopImage:function(t){return t.site.defaultFiles.store},defaultCityImage:function(t){return t.site.defaultFiles.default_city_img},defaultSupplyImage:function(t){return t.site.defaultFiles.default_supply_img},defaultStoreImage:function(t){return t.site.defaultFiles.store},defaultCategoryImage:function(t){return t.site.defaultFiles.goods},defaultBrandImage:function(t){return t.site.defaultFiles.goods},defaultArticleImage:function(t){return t.site.defaultFiles.article},orderCreateGoodsData:function(t){var e=localStorage.getItem("orderCreateGoodsData");return e&&(e=JSON.parse(e)),e},groupbuyOrderCreateData:function(t){var e=localStorage.getItem("groupbuyOrderCreateData");return e&&(e=JSON.parse(e)),e},seckillOrderCreateData:function(t){var e=localStorage.getItem("seckillOrderCreateData");return e&&(e=JSON.parse(e)),e},comboOrderCreateData:function(t){var e=localStorage.getItem("comboOrderCreateData");return e&&(e=JSON.parse(e)),e}};e.default=r},434:function(t,e,n){"use strict";n.r(e);var r=n(15),o=(n(7),n(175)),c=n(199),l=n(22),d=n(207),f={token:Object(l.a)(),autoLoginRange:0,member:""},m={SET_TOKEN:function(t,e){t.token=e},SET_AUTOLOGIN_FLAG:function(t,e){t.autoLogin=e},SET_MEMBER:function(t,e){"object"==Object(r.a)(e)?(t.member={},Object.assign(t.member,e)):t.member=e}},h={login:function(t,e){var n=t.commit,r=e.username,c=e.password,d=e.captcha_id,f=e.captcha_code,m=e.autoLoginRange;return new Promise((function(t,h){return Object(o.a)({username:r,password:c,captcha_id:d,captcha_code:f,autoLoginRange:m}).then((function(r){var code=r.code,data=(r.message,r.data);0==code&&(n("SET_TOKEN",data.token),void 0!==e.autoLoginRange&&n("SET_AUTOLOGIN_FLAG",e.autoLoginRange),Object(l.c)(data.token,e.autoLoginRange),t(r)),h()})).catch((function(t){h(t)}))}))},mobile_login:function(t,e){var n=t.commit,r=e.mobile,c=e.key,code=e.code;return new Promise((function(t,d){return Object(o.c)({mobile:r,key:c,code:code}).then((function(r){var code=r.code,data=(r.message,r.data);0==code&&(n("SET_TOKEN",data.token),Object(l.c)(data.token,e.autoLoginRange),t(r)),d()})).catch((function(t){d(t)}))}))},remove_token:function(t){(0,t.commit)("SET_TOKEN",""),Object(l.b)()},register_token:function(t,e){var n=t.commit,r=e.username,o=e.password,d=e.captcha_id,f=e.captcha_code;return new Promise((function(t,e){return Object(c.c)({username:r,password:o,captcha_id:d,captcha_code:f}).then((function(r){var code=r.code,data=(r.message,r.data);0==code&&(n("SET_TOKEN",data.token),Object(l.c)(data.token),t(r)),e()})).catch((function(t){e(t)}))}))},registerMobile_token:function(t,e){var n=t.commit,r=e.mobile,o=e.key,code=e.code,d=e.captcha_id,f=e.captcha_code;return new Promise((function(t,e){return Object(c.e)({mobile:r,key:o,code:code,captcha_id:d,captcha_code:f}).then((function(r){var code=r.code,data=(r.message,r.data);0==code&&(n("SET_TOKEN",data.token),Object(l.c)(data.token),t(r)),e()})).catch((function(t){e(t)}))}))},auto_login_range:function(t,e){(0,t.commit)("SET_AUTOLOGIN_FLAG",e)},logout:function(t){var e=t.commit;e("SET_TOKEN",""),e("SET_MEMBER",""),Object(l.b)()},member_detail:function(t,e){var n=t.commit;if(!t.state.member||e&&e.refresh)return new Promise((function(t,e){return Object(d.k)({token:Object(l.a)()}).then((function(e){if(e){var data=e.data;return n("SET_MEMBER",data),t(e)}})).catch((function(t){return e(t)}))}))}};e.default={namespaced:!0,state:f,mutations:m,actions:h}},435:function(t,e,n){"use strict";n.r(e);var r={SET_ORDER_CREATE_DATA:function(t,e){t.orderCreateGoodsData=e,e?localStorage.setItem("orderCreateGoodsData",JSON.stringify(e)):localStorage.removeItem("orderCreateGoodsData")},SET_GROUPBUY_ORDER_CREATE_DATA:function(t,e){t.groupbuyOrderCreateData=e,e?localStorage.setItem("groupbuyOrderCreateData",JSON.stringify(e)):localStorage.removeItem("groupbuyOrderCreateData")},SET_SECKILL_ORDER_CREATE_DATA:function(t,e){t.seckillOrderCreateData=e,e?localStorage.setItem("seckillOrderCreateData",JSON.stringify(e)):localStorage.removeItem("seckillOrderCreateData")},SET_COMBO_ORDER_CREATE_DATA:function(t,e){t.comboOrderCreateData=e,e?localStorage.setItem("comboOrderCreateData",JSON.stringify(e)):localStorage.removeItem("comboOrderCreateData")}};e.default={namespaced:!0,state:{orderCreateGoodsData:"",groupbuyOrderCreateData:"",seckillOrderCreateData:"",comboOrderCreateData:""},mutations:r,actions:{setOrderCreateData:function(t,data){var e=t.commit;t.state;e("SET_ORDER_CREATE_DATA",data)},removeOrderCreateData:function(t){(0,t.commit)("SET_ORDER_CREATE_DATA","")},setGroupbuyOrderCreateData:function(t,data){var e=t.commit;t.state;e("SET_GROUPBUY_ORDER_CREATE_DATA",data)},removeGroupbuyOrderCreateData:function(t){(0,t.commit)("SET_GROUPBUY_ORDER_CREATE_DATA","")},setSeckillOrderCreateData:function(t,data){var e=t.commit;t.state;e("SET_SECKILL_ORDER_CREATE_DATA",data)},removeSeckillOrderCreateData:function(t){(0,t.commit)("SET_SECKILL_ORDER_CREATE_DATA","")},setComboOrderCreateData:function(t,data){var e=t.commit;t.state;e("SET_COMBO_ORDER_CREATE_DATA",data)},removeComboOrderCreateData:function(t){(0,t.commit)("SET_COMBO_ORDER_CREATE_DATA","")}}}},436:function(t,e,n){"use strict";n.r(e);n(7);var r=n(146),o=n(22),c={token:Object(o.a)(),autoLoginRange:0,member:""},l={loginCode:function(t){t.commit;return new Promise((function(t,e){return Object(r.c)().then((function(n){var code=n.code;n.message,n.data;0==code&&t(n),e()})).catch((function(t){e(t)}))}))},checkLogin:function(t,e){var n=t.commit;e.key;return new Promise((function(t,c){return Object(r.a)(e).then((function(r){var code=r.code,data=(r.message,r.data);0==code&&(n("SET_TOKEN",data.token),Object(o.c)(data.token,e.autoLoginRange),t(r)),c()})).catch((function(t){c(t)}))}))},wechatLogin:function(t,e){var n=t.commit;e.mobile,e.key,e.code,e.captcha_id,e.captcha_code;return new Promise((function(t,c){return Object(r.d)(e).then((function(r){var code=r.code,data=(r.message,r.data);0==code&&(n("SET_TOKEN",data.token),Object(o.c)(data.token,e.autoLoginRange),t(r)),c()})).catch((function(t){c(t)}))}))}};e.default={namespaced:!0,state:c,mutations:{SET_TOKEN:function(t,e){t.token=e}},actions:l}},521:function(t,e){},534:function(t,e,n){"use strict";n.r(e);n(7);var r=n(27),o=n(1);var c={qrCodes:function(t){var e=t.commit;t.state;return new Promise((function(t,n){return Object(r.j)({}).then((function(r){var code=r.code,data=(r.message,r.data);0==code&&(e("SET_SITE_QRCODE",data),t(r)),n({})})).catch((function(t){n(t)}))}))},copyRight:function(t){var e=t.commit;t.state;return new Promise((function(t,n){return Object(r.c)({}).then((function(r){var code=r.code,data=(r.message,r.data);0==code&&(e("SET_COPY_RIGHT",data),t(r)),n({})})).catch((function(t){n(t)}))}))},siteInfo:function(t){var e=t.commit;t.state;return new Promise((function(t,n){return Object(r.l)({}).then((function(r){if(r){var code=r.code,data=(r.message,r.data);0==code&&(e("SET_SITE_INFO",data),t(r))}n({})})).catch((function(t){n(t)}))}))},defaultFiles:function(t){var e=t.commit;t.state;return new Promise((function(t,n){return Object(r.i)({}).then((function(r){if(r){var code=r.code,data=(r.message,r.data);0==code&&(e("SET_SITE_DEFAULT_FILES",data),t(r))}n({})})).catch((function(t){n(t)}))}))},addons:function(t){var e=t.commit;t.state;return new Promise((function(t,n){return(r={},Object(o.a)({url:"/api/addon/addonisexit",data:r})).then((function(r){if(r){var code=r.code,data=(r.message,r.data);0==code&&(e("SET_ADDONS",data),t(r))}n({})})).catch((function(t){n(t)}));var r}))}};e.default={namespaced:!0,state:{siteQrCode:"",copyRight:"",siteInfo:{logo:""},defaultFiles:"",addons:""},mutations:{SET_SITE_QRCODE:function(t,e){t.siteQrCode=e},SET_COPY_RIGHT:function(t,e){t.copyRight=e},SET_SITE_INFO:function(t,e){t.siteInfo=e},SET_SITE_DEFAULT_FILES:function(t,e){t.defaultFiles=e},SET_ADDONS:function(t,e){t.addons=e}},actions:c}},55:function(t,e,n){"use strict";n.d(e,"b",(function(){return pe})),n.d(e,"a",(function(){return A}));var r={};n.r(r),n.d(r,"BreadCrumbs",(function(){return At})),n.d(r,"GoodsRecommend",(function(){return Rt})),n.d(r,"MessageGoodsItem",(function(){return Lt})),n.d(r,"MessageOrderItem",(function(){return Dt})),n.d(r,"MessageServicerMessage",(function(){return Nt}));n(24),n(25),n(23),n(29),n(18),n(30);var o=n(21),c=n(10),l=(n(95),n(56),n(7),n(75),n(31),n(64),n(2)),d=n(12),f=n(308),m=n(201),h=n.n(m),v=n(91),_=n.n(v),y=(n(48),n(50),n(202)),O=n(54),j=n(0);"scrollRestoration"in window.history&&(Object(j.u)("manual"),window.addEventListener("beforeunload",(function(){Object(j.u)("auto")})),window.addEventListener("load",(function(){Object(j.u)("manual")})));function w(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function x(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?w(Object(source),!0).forEach((function(e){Object(c.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):w(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var C=function(){};l.default.use(y.a);var k={mode:"history",base:"/",linkActiveClass:"nuxt-link-active",linkExactActiveClass:"nuxt-link-exact-active",scrollBehavior:function(t,e,n){var r=!1,o=t!==e;n?r=n:o&&function(t){var e=Object(j.g)(t);if(1===e.length){var n=e[0].options;return!1!==(void 0===n?{}:n).scrollToTop}return e.some((function(t){var e=t.options;return e&&e.scrollToTop}))}(t)&&(r={x:0,y:0});var c=window.$nuxt;return(!o||t.path===e.path&&t.hash!==e.hash)&&c.$nextTick((function(){return c.$emit("triggerScroll")})),new Promise((function(e){c.$once("triggerScroll",(function(){if(t.hash){var n=t.hash;void 0!==window.CSS&&void 0!==window.CSS.escape&&(n="#"+window.CSS.escape(n.substr(1)));try{document.querySelector(n)&&(r={selector:n})}catch(t){console.warn("Failed to save scroll position. Please add CSS.escape() polyfill (https://github.com/mathiasbynens/CSS.escape).")}}e(r)}))}))},routes:[{path:"/close",component:function(){return Object(j.m)(n.e(9).then(n.bind(null,762)))},name:"close"},{path:"/member",component:function(){return Object(j.m)(n.e(36).then(n.bind(null,763)))},name:"member"},{path:"/pay",component:function(){return Object(j.m)(n.e(62).then(n.bind(null,764)))},name:"pay"},{path:"/auth/find",component:function(){return Object(j.m)(n.e(6).then(n.bind(null,748)))},name:"auth-find"},{path:"/auth/login",component:function(){return Object(j.m)(n.e(7).then(n.bind(null,749)))},name:"auth-login"},{path:"/auth/register",component:function(){return Object(j.m)(n.e(8).then(n.bind(null,765)))},name:"auth-register"},{path:"/goods/brand",component:function(){return Object(j.m)(n.e(16).then(n.bind(null,766)))},name:"goods-brand"},{path:"/goods/cart",component:function(){return Object(j.m)(n.e(17).then(n.bind(null,750)))},name:"goods-cart"},{path:"/goods/category",component:function(){return Object(j.m)(n.e(18).then(n.bind(null,767)))},name:"goods-category"},{path:"/goods/coupon",component:function(){return Object(j.m)(n.e(19).then(n.bind(null,768)))},name:"goods-coupon"},{path:"/goods/list",component:function(){return Object(j.m)(n.e(20).then(n.bind(null,751)))},name:"goods-list"},{path:"/member/account",component:function(){return Object(j.m)(n.e(26).then(n.bind(null,769)))},name:"member-account"},{path:"/member/account_edit",component:function(){return Object(j.m)(n.e(27).then(n.bind(null,770)))},name:"member-account_edit"},{path:"/member/account_list",component:function(){return Object(j.m)(n.e(28).then(n.bind(null,771)))},name:"member-account_list"},{path:"/member/activist",component:function(){return Object(j.m)(n.e(29).then(n.bind(null,772)))},name:"member-activist"},{path:"/member/address_edit",component:function(){return Object(j.m)(n.e(30).then(n.bind(null,773)))},name:"member-address_edit"},{path:"/member/apply_withdrawal",component:function(){return Object(j.m)(n.e(31).then(n.bind(null,774)))},name:"member-apply_withdrawal"},{path:"/member/collection",component:function(){return Object(j.m)(n.e(32).then(n.bind(null,775)))},name:"member-collection"},{path:"/member/coupon",component:function(){return Object(j.m)(n.e(33).then(n.bind(null,776)))},name:"member-coupon"},{path:"/member/delivery_address",component:function(){return Object(j.m)(n.e(34).then(n.bind(null,777)))},name:"member-delivery_address"},{path:"/member/footprint",component:function(){return Object(j.m)(n.e(35).then(n.bind(null,778)))},name:"member-footprint"},{path:"/member/info",component:function(){return Object(j.m)(Promise.all([n.e(3),n.e(37)]).then(n.bind(null,779)))},name:"member-info"},{path:"/member/my_point",component:function(){return Object(j.m)(n.e(38).then(n.bind(null,752)))},name:"member-my_point"},{path:"/member/order_detail",component:function(){return Object(j.m)(n.e(39).then(n.bind(null,780)))},name:"member-order_detail"},{path:"/member/order_detail_local_delivery",component:function(){return Object(j.m)(n.e(40).then(n.bind(null,781)))},name:"member-order_detail_local_delivery"},{path:"/member/order_detail_pickup",component:function(){return Object(j.m)(n.e(41).then(n.bind(null,782)))},name:"member-order_detail_pickup"},{path:"/member/order_detail_virtual",component:function(){return Object(j.m)(n.e(42).then(n.bind(null,783)))},name:"member-order_detail_virtual"},{path:"/member/order_list",component:function(){return Object(j.m)(n.e(43).then(n.bind(null,784)))},name:"member-order_list"},{path:"/member/recharge_detail",component:function(){return Object(j.m)(n.e(44).then(n.bind(null,785)))},name:"member-recharge_detail"},{path:"/member/recharge_list",component:function(){return Object(j.m)(n.e(45).then(n.bind(null,786)))},name:"member-recharge_list"},{path:"/member/recharge_order",component:function(){return Object(j.m)(n.e(46).then(n.bind(null,787)))},name:"member-recharge_order"},{path:"/member/security",component:function(){return Object(j.m)(n.e(47).then(n.bind(null,788)))},name:"member-security"},{path:"/member/withdrawal",component:function(){return Object(j.m)(n.e(49).then(n.bind(null,789)))},name:"member-withdrawal"},{path:"/member/withdrawal_detail",component:function(){return Object(j.m)(n.e(50).then(n.bind(null,790)))},name:"member-withdrawal_detail"},{path:"/order/batchrefund",component:function(){return Object(j.m)(n.e(51).then(n.bind(null,791)))},name:"order-batchrefund"},{path:"/order/complain",component:function(){return Object(j.m)(n.e(52).then(n.bind(null,792)))},name:"order-complain"},{path:"/order/evaluate",component:function(){return Object(j.m)(n.e(53).then(n.bind(null,793)))},name:"order-evaluate"},{path:"/order/logistics",component:function(){return Object(j.m)(n.e(54).then(n.bind(null,794)))},name:"order-logistics"},{path:"/order/orderbatch_refund",component:function(){return Object(j.m)(n.e(55).then(n.bind(null,795)))},name:"order-orderbatch_refund"},{path:"/order/payment",component:function(){return Object(j.m)(n.e(56).then(n.bind(null,753)))},name:"order-payment"},{path:"/order/refund",component:function(){return Object(j.m)(n.e(57).then(n.bind(null,796)))},name:"order-refund"},{path:"/order/refund_detail",component:function(){return Object(j.m)(n.e(58).then(n.bind(null,797)))},name:"order-refund_detail"},{path:"/order/verification",component:function(){return Object(j.m)(n.e(59).then(n.bind(null,798)))},name:"order-verification"},{path:"/order/verification_detail",component:function(){return Object(j.m)(n.e(60).then(n.bind(null,799)))},name:"order-verification_detail"},{path:"/order/verification_list",component:function(){return Object(j.m)(n.e(61).then(n.bind(null,800)))},name:"order-verification_list"},{path:"/pay/result",component:function(){return Object(j.m)(n.e(63).then(n.bind(null,801)))},name:"pay-result"},{path:"/promotion/groupbuy",component:function(){return Object(j.m)(n.e(67).then(n.bind(null,802)))},name:"promotion-groupbuy"},{path:"/promotion/seckill",component:function(){return Object(j.m)(n.e(70).then(n.bind(null,754)))},name:"promotion-seckill"},{path:"/cms/article/detail",component:function(){return Object(j.m)(n.e(10).then(n.bind(null,803)))},name:"cms-article-detail"},{path:"/cms/article/list",component:function(){return Object(j.m)(n.e(11).then(n.bind(null,804)))},name:"cms-article-list"},{path:"/cms/help/detail",component:function(){return Object(j.m)(n.e(12).then(n.bind(null,805)))},name:"cms-help-detail"},{path:"/cms/help/list",component:function(){return Object(j.m)(n.e(13).then(n.bind(null,806)))},name:"cms-help-list"},{path:"/cms/notice/detail",component:function(){return Object(j.m)(n.e(14).then(n.bind(null,807)))},name:"cms-notice-detail"},{path:"/cms/notice/list",component:function(){return Object(j.m)(n.e(15).then(n.bind(null,808)))},name:"cms-notice-list"},{path:"/member/security/security",component:function(){return Object(j.m)(n.e(48).then(n.bind(null,616)))},name:"member-security-security"},{path:"/promotion/combo/payment",component:function(){return Object(j.m)(n.e(65).then(n.bind(null,755)))},name:"promotion-combo-payment"},{path:"/promotion/groupbuy/payment",component:function(){return Object(j.m)(n.e(68).then(n.bind(null,756)))},name:"promotion-groupbuy-payment"},{path:"/promotion/seckill/payment",component:function(){return Object(j.m)(n.e(71).then(n.bind(null,757)))},name:"promotion-seckill-payment"},{path:"/promotion/combo/:id",component:function(){return Object(j.m)(n.e(64).then(n.bind(null,809)))},name:"promotion-combo-id"},{path:"/promotion/groupbuy/:id",component:function(){return Object(j.m)(Promise.all([n.e(0),n.e(66)]).then(n.bind(null,758)))},name:"promotion-groupbuy-id"},{path:"/promotion/seckill/:id",component:function(){return Object(j.m)(Promise.all([n.e(0),n.e(69)]).then(n.bind(null,759)))},name:"promotion-seckill-id"},{path:"/sku/:id",component:function(){return Object(j.m)(Promise.all([n.e(0),n.e(72)]).then(n.bind(null,760)))},name:"sku-id"},{path:"/",component:function(){return Object(j.m)(n.e(21).then(n.bind(null,761)))},name:"index",children:[{path:"components/floor-style-1",component:function(){return Object(j.m)(n.e(22).then(n.bind(null,656)))},name:"index-components-floor-style-1"},{path:"components/floor-style-2",component:function(){return Object(j.m)(n.e(23).then(n.bind(null,657)))},name:"index-components-floor-style-2"},{path:"components/floor-style-3",component:function(){return Object(j.m)(n.e(24).then(n.bind(null,658)))},name:"index-components-floor-style-3"},{path:"components/floor-style-4",component:function(){return Object(j.m)(n.e(25).then(n.bind(null,659)))},name:"index-components-floor-style-4"}]}],fallback:!1};function S(t,e){var base=e._app&&e._app.basePath||k.base,n=new y.a(x(x({},k),{},{base:base})),r=n.push;n.push=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:C,n=arguments.length>2?arguments[2]:void 0;return r.call(this,t,e,n)};var o=n.resolve.bind(n);return n.resolve=function(t,e,n){return"string"==typeof t&&(t=Object(O.c)(t)),o(t,e,n)},n}var E={name:"NuxtChild",functional:!0,props:{nuxtChildKey:{type:String,default:""},keepAlive:Boolean,keepAliveProps:{type:Object,default:void 0}},render:function(t,e){var n=e.parent,data=e.data,r=e.props,o=n.$createElement;data.nuxtChild=!0;for(var c=n,l=n.$nuxt.nuxt.transitions,d=n.$nuxt.nuxt.defaultTransition,f=0;n;)n.$vnode&&n.$vnode.data.nuxtChild&&f++,n=n.$parent;data.nuxtChildDepth=f;var m=l[f]||d,h={};T.forEach((function(t){void 0!==m[t]&&(h[t]=m[t])}));var v={};I.forEach((function(t){"function"==typeof m[t]&&(v[t]=m[t].bind(c))}));var _=v.beforeEnter;if(v.beforeEnter=function(t){if(window.$nuxt.$nextTick((function(){window.$nuxt.$emit("triggerScroll")})),_)return _.call(c,t)},!1===m.css){var y=v.leave;(!y||y.length<2)&&(v.leave=function(t,e){y&&y.call(c,t),c.$nextTick(e)})}var O=o("routerView",data);return r.keepAlive&&(O=o("keep-alive",{props:r.keepAliveProps},[O])),o("transition",{props:h,on:v},[O])}},T=["name","mode","appear","css","type","duration","enterClass","leaveClass","appearClass","enterActiveClass","enterActiveClass","leaveActiveClass","appearActiveClass","enterToClass","leaveToClass","appearToClass"],I=["beforeEnter","enter","afterEnter","enterCancelled","beforeLeave","leave","afterLeave","leaveCancelled","beforeAppear","appear","afterAppear","appearCancelled"],$={props:["error"],layout:"empty",created:function(){},methods:{}},P=(n(380),n(6)),A=Object(P.a)($,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"error-wrap"},[e("img",{attrs:{src:n(331)}}),t._v(" "),404===t.error.statusCode?e("h1",[t._v("页面不存在")]):e("h1",[t._v("应用发生错误异常")]),t._v(" "),e("router-link",{staticClass:"ns-text-color",attrs:{to:"/"}},[t._v("返回首页")])],1)}),[],!1,null,"18783d9d",null).exports,R=n(42),L=(n(74),{name:"Nuxt",components:{NuxtChild:E,NuxtError:A},props:{nuxtChildKey:{type:String,default:void 0},keepAlive:Boolean,keepAliveProps:{type:Object,default:void 0},name:{type:String,default:"default"}},errorCaptured:function(t){this.displayingNuxtError&&(this.errorFromNuxtError=t,this.$forceUpdate())},computed:{routerViewKey:function(){if(void 0!==this.nuxtChildKey||this.$route.matched.length>1)return this.nuxtChildKey||Object(j.c)(this.$route.matched[0].path)(this.$route.params);var t=Object(R.a)(this.$route.matched,1)[0];if(!t)return this.$route.path;var e=t.components.default;if(e&&e.options){var n=e.options;if(n.key)return"function"==typeof n.key?n.key(this.$route):n.key}return/\/$/.test(t.path)?this.$route.path:this.$route.path.replace(/\/$/,"")}},beforeCreate:function(){l.default.util.defineReactive(this,"nuxt",this.$root.$options.nuxt)},render:function(t){var e=this;return this.nuxt.err?this.errorFromNuxtError?(this.$nextTick((function(){return e.errorFromNuxtError=!1})),t("div",{},[t("h2","An error occurred while showing the error page"),t("p","Unfortunately an error occurred and while showing the error page another error occurred"),t("p","Error details: ".concat(this.errorFromNuxtError.toString())),t("nuxt-link",{props:{to:"/"}},"Go back to home")])):(this.displayingNuxtError=!0,this.$nextTick((function(){return e.displayingNuxtError=!1})),t(A,{props:{error:this.nuxt.err}})):t("NuxtChild",{key:this.routerViewKey,props:this.$props})}}),D=(n(65),n(83),n(84),n(85),n(73),{name:"NuxtLoading",data:function(){return{percent:0,show:!1,canSucceed:!0,reversed:!1,skipTimerCount:0,rtl:!1,throttle:200,duration:5e3,continuous:!1}},computed:{left:function(){return!(!this.continuous&&!this.rtl)&&(this.rtl?this.reversed?"0px":"auto":this.reversed?"auto":"0px")}},beforeDestroy:function(){this.clear()},methods:{clear:function(){clearInterval(this._timer),clearTimeout(this._throttle),this._timer=null},start:function(){var t=this;return this.clear(),this.percent=0,this.reversed=!1,this.skipTimerCount=0,this.canSucceed=!0,this.throttle?this._throttle=setTimeout((function(){return t.startTimer()}),this.throttle):this.startTimer(),this},set:function(t){return this.show=!0,this.canSucceed=!0,this.percent=Math.min(100,Math.max(0,Math.floor(t))),this},get:function(){return this.percent},increase:function(t){return this.percent=Math.min(100,Math.floor(this.percent+t)),this},decrease:function(t){return this.percent=Math.max(0,Math.floor(this.percent-t)),this},pause:function(){return clearInterval(this._timer),this},resume:function(){return this.startTimer(),this},finish:function(){return this.percent=this.reversed?0:100,this.hide(),this},hide:function(){var t=this;return this.clear(),setTimeout((function(){t.show=!1,t.$nextTick((function(){t.percent=0,t.reversed=!1}))}),500),this},fail:function(t){return this.canSucceed=!1,this},startTimer:function(){var t=this;this.show||(this.show=!0),void 0===this._cut&&(this._cut=1e4/Math.floor(this.duration)),this._timer=setInterval((function(){t.skipTimerCount>0?t.skipTimerCount--:(t.reversed?t.decrease(t._cut):t.increase(t._cut),t.continuous&&(t.percent>=100||t.percent<=0)&&(t.skipTimerCount=1,t.reversed=!t.reversed))}),100)}},render:function(t){var e=t(!1);return this.show&&(e=t("div",{staticClass:"nuxt-progress",class:{"nuxt-progress-notransition":this.skipTimerCount>0,"nuxt-progress-failed":!this.canSucceed},style:{width:this.percent+"%",left:this.left}})),e}}),N=(n(381),Object(P.a)(D,undefined,undefined,!1,null,null,null).exports),M=(n(382),n(383),n(27));function U(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var F={props:{},data:function(){return{}},created:function(){this.$store.dispatch("site/copyRight")},mounted:function(){},watch:{},methods:{},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?U(Object(source),!0).forEach((function(e){Object(c.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):U(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(d.b)(["copyRight","siteInfo"]))},H=F,B=(n(403),Object(P.a)(H,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"footer-bottom"},[e("div",{staticClass:"site-info"},[t.siteInfo.web_phone?e("p",[e("i",{staticClass:"iconfont icondianhua"}),t._v(t._s(t.siteInfo.web_phone))]):t._e(),t._v(" "),t.siteInfo.web_email?e("p",[e("i",{staticClass:"iconfont iconyouxiang"}),t._v(" "),e("el-link",{attrs:{href:"mailto:".concat(t.siteInfo.web_email)}},[t._v(t._s(t.siteInfo.web_email))])],1):t._e()]),t._v(" "),e("p",[t._v("\n        "+t._s(t.copyRight.copyright_desc)+"\n        "),t.copyRight.icp?e("a",{staticClass:"footer-link",attrs:{href:"https://beian.miit.gov.cn",target:"_blank"}},[t._v("备案号："+t._s(t.copyRight.icp))]):t._e()]),t._v(" "),e("p",[t.copyRight.gov_record?e("a",{staticClass:"footer-link",attrs:{href:t.copyRight.gov_url,target:"_blank"}},[e("img",{attrs:{src:n(384),alt:"公安备案"}}),t._v(" "),e("span",[t._v(t._s(t.copyRight.gov_record))])]):t._e(),t._v(" "),t.copyRight.business_show_link?e("a",{staticClass:"footer-link",attrs:{href:t.copyRight.business_show_link,target:"_blank"}},[e("img",{attrs:{src:t.$img("public/static/img/business_show.png"),alt:"营业执照"}}),t._v(" "),e("span",[t._v("电子营业执照")])]):t._e()])])}),[],!1,null,"77a9a27d",null).exports);n(22);function G(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function K(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?G(Object(source),!0).forEach((function(e){Object(c.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):G(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var J={props:{},data:function(){return{}},created:function(){this.$store.dispatch("member/member_detail"),this.$store.dispatch("site/defaultFiles"),this.$store.dispatch("site/addons")},mounted:function(){},watch:{},methods:{logout:function(){this.$store.dispatch("member/logout"),this.$router.push("/")},addFavorite:function(){var title="Niushop PC端",t=window.location.href;try{window.external?window.external.addFavorite(t,title):window.sidebar&&window.sidebar.addPanel(title,t,"")}catch(t){alert("抱歉，您所使用的浏览器无法完成此操作。\n\n加入收藏失败，请使用Ctrl+D进行添加")}}},components:{},computed:K(K({},Object(d.b)(["wapQrcode","member","addonIsExit"])),{},{logined:function(){return void 0!==this.member&&""!==this.member&&this.member!=={}}})},Q=(n(404),Object(P.a)(J,(function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"header-top"},[e("div",{staticClass:"top-content"},[e("div",{staticClass:"top-left"},[e("div",{staticClass:"left-item collect",on:{click:t.addFavorite}},[e("span",{staticClass:"iconfont icon-shouzang"}),t._v(" "),e("span",[t._v("收藏本站")])])]),t._v(" "),e("div",{staticClass:"top-right"},[t.logined?e("div",{staticClass:"member-info"},[e("router-link",{attrs:{to:"/member"}},[t._v(t._s(t.member.nickname||t.member.username))]),t._v(" "),e("a",{on:{click:t.logout}},[t._v("退出")])],1):t._e(),t._v(" "),t.logined?t._e():e("div",{staticClass:"member-info"},[e("router-link",{attrs:{to:"/auth/login"}},[t._v("登录")]),t._v(" "),e("router-link",{attrs:{to:"/auth/register"}},[t._v("注册")])],1),t._v(" "),e("router-link",{attrs:{to:"/cms/notice/list"}},[e("span",{staticClass:"announcement iconfont icon-xiaoxi"})]),t._v(" "),e("router-link",{attrs:{to:"/member/order_list"}},[t._v("我的订单")])],1)])])])}),[],!1,null,"4602e7d4",null).exports),Y=n(61),W={components:{NsHeaderTop:Q,NsHeaderMid:Y.a},created:function(){this.$store.dispatch("cart/cart_count")}},X=(n(406),Object(P.a)(W,(function(){var t=this._self._c;return t("div",{staticClass:"header"},[t("ns-header-top"),this._v(" "),t("ns-header-mid")],1)}),[],!1,null,"339e2f02",null).exports),z=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"li-item"},[e("img",{attrs:{src:n(410)}}),t._v(" "),e("span",[t._v("手机购买")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"li-item"},[e("img",{attrs:{src:n(411)}}),t._v(" "),e("span",[t._v("会员中心")])])},function(){var t=this._self._c;return t("div",{staticClass:"main-sidebar-right"},[t("div",{staticClass:"history-product",attrs:{id:"mainSidebarHistoryProduct"}})])}];function V(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function Z(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?V(Object(source),!0).forEach((function(e){Object(c.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):V(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var tt={props:{},data:function(){return{visible:!1,hackReset:!1,serviceConfig:{type:"nome"}}},components:{servicerMessage:n(217).default},computed:Z(Z({},Object(d.b)(["wapQrcode","cartCount","siteInfo","member"])),{},{qrcode:function(){return""===this.wapQrcode?"":this.wapQrcode.path.h5.img},logined:function(){return void 0!==this.member&&""!==this.member&&this.member!=={}}}),created:function(){this.$store.dispatch("site/qrCodes"),this.shopServiceOpen()},mounted:function(){window.addEventListener("scroll",this.handleScroll)},beforeDestroy:function(){window.removeEventListener("scroll",this.handleScroll)},watch:{},methods:{handleScroll:function(){this.visible=window.pageYOffset>300},shopServiceOpen:function(){var t=this;Object(M.h)().then((function(e){e&&0==e.code&&(t.serviceConfig=e.data.pc)}))},toTop:function(){var t=setInterval((function(){var e=document.documentElement.scrollTop||document.body.scrollTop,n=Math.floor(-e/5);document.documentElement.scrollTop=document.body.scrollTop=e+n,this.isTop=!0,0===e&&clearInterval(t)}),20)},showServiceFn:function(){if(this.logined)switch(this.serviceConfig.type){case"third":window.open(this.serviceConfig.third_url,"_blank");break;case"niushop":this.hackReset=!0,this.$refs.servicer.show()}else this.$message({message:"您还未登录",type:"warning"})}}},et=(n(420),Object(P.a)(tt,(function(){var t=this,e=t._self._c;return e("aside",{staticClass:"main-sidebar clearfix"},[e("div",{staticClass:"main-sidebar-body"},[e("ul",[e("li",{on:{click:function(e){return t.$router.push("/goods/cart")}}},[e("div",{staticClass:"li-item"},[e("img",{attrs:{src:n(407)}}),t._v(" "),e("span",[t._v("购物车")]),t._v(" "),e("em",{directives:[{name:"show",rawName:"v-show",value:t.cartCount,expression:"cartCount"}]},[t._v(t._s(t.cartCount))])])]),t._v(" "),e("li",{staticClass:"mobile"},[e("div",{staticClass:"mobile-qrcode-wrap"},[t.qrcode?e("div",{staticClass:"mobile-qrcode"},[e("img",{attrs:{src:t.$img(t.qrcode)}})]):t._e()]),t._v(" "),t._m(0)]),t._v(" "),"none"!=t.serviceConfig.type?e("li",{staticClass:"kefuTip"},[e("div",{staticClass:"li-item",on:{click:function(e){return t.showServiceFn()}}},[e("img",{attrs:{src:n(408)}}),t._v(" "),e("span",[t._v("联系客服")])])]):t._e(),t._v(" "),e("li",{on:{click:function(e){return t.$router.push("/member")}}},[t._m(1)])]),t._v(" "),e("div",{class:["back-top",{showBtn:t.visible}],on:{click:t.toTop}},[e("img",{attrs:{src:n(409)}}),t._v(" "),e("span",[t._v("回到顶部")])])]),t._v(" "),t._m(2),t._v(" "),e("servicerMessage",{ref:"servicer",staticClass:"kefu",attrs:{shop:{shop_id:0,logo:t.siteInfo.logo,shop_name:"平台客服"}}})],1)}),z,!1,null,"c5eaf342",null).exports),nt=(n(321),n(216)),ot={props:{},data:function(){return{shopServiceList:[],linkList:[],helpList:[],ishide:!1,activeName:"first",qrcode:""}},computed:{},created:function(){this.getShopServiceLists(),this.link(),this.getHelpList(),this.getqrcodeimg()},mounted:function(){},watch:{},methods:{getqrcodeimg:function(){var t=this;Object(M.k)({}).then((function(e){0==e.code&&e.data&&(t.qrcode=e.data)})).catch((function(e){t.$message.error(e.message)}))},getShopServiceLists:function(){var t=this;Object(M.g)({}).then((function(e){0==e.code&&e.data&&(t.shopServiceList=e.data)})).catch((function(e){t.shopServiceList=[]}))},link:function(){var t=this;Object(M.d)({}).then((function(e){0==e.code&&e.data&&(t.linkList=e.data)})).catch((function(e){t.$message.error(e.message)}))},linkUrl:function(t,e){t&&(-1==t.indexOf("http")&&-1==t.indexOf("https")?e?this.$util.pushToTab({path:t}):this.$router.push({path:t}):e?window.open(t):window.location.href=t)},getHelpList:function(){var t=this;Object(nt.b)().then((function(e){if(0==e.code&&e.data){var n=[];n=e.data.slice(0,4);for(var i=0;i<n.length;i++)n[i].child_list=n[i].child_list.slice(0,4);t.helpList=n}})).catch((function(t){}))},clickToHelp:function(t){this.$router.push("/cms/help/listother-"+t)},clickToHelpDetail:function(t){this.$router.push("/cms/help-"+t)},clickJump:function(address){location.href=address}},components:{CopyRight:B}},it=(n(423),n(424),Object(P.a)(ot,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"footer"},[t.linkList.length>0?e("el-tabs",{staticClass:"friendly-link",model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[e("el-tab-pane",{attrs:{label:"友情链接",name:"first"}},t._l(t.linkList,(function(n,r){return e("div",{key:r,staticClass:"link-item",attrs:{title:n.link_title},on:{click:function(e){return t.linkUrl(n.link_url,n.is_blank)}}},[e("img",{attrs:{src:t.$img(n.link_pic)}})])})),0)],1):t._e(),t._v(" "),t.shopServiceList.length>0?e("div",{staticClass:"footer-top"},[e("ul",{staticClass:"service"},t._l(t.shopServiceList,(function(n,r){return e("li",{key:r},[e("div",{staticClass:"item-head"},["img"==n.icon.iconType?e("img",{attrs:{src:t.$img(n.icon.imageUrl),alt:""}}):e("span",{class:n.icon.icon})]),t._v(" "),e("div",{staticClass:"item-content"},[e("p",{staticClass:"name"},[t._v(t._s(n.service_name))]),t._v(" "),e("p",{staticClass:"desc"},[t._v(t._s(n.desc))])])])})),0)]):t._e(),t._v(" "),e("div",{staticClass:"footer-bot"},[e("copy-right")],1)],1)}),[],!1,null,"6cc7184f",null).exports),at=n(152);function st(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var ct={props:{forceExpand:{type:Boolean,default:!1}},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?st(Object(source),!0).forEach((function(e){Object(c.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):st(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(d.b)(["is_show"])),data:function(){return{isShopHover:!1,isIndex:!1,thisRoute:"",goodsCategoryTree:[],categoryConfig:{},selectedCategory:-1,isHide:!1}},components:{NsHeaderTop:Q,NsHeaderMid:Y.a,mapGetters:d.b},beforeCreate:function(){},created:function(){this.$store.dispatch("cart/cart_count"),this.getCategoryConfig(),"/"!=this.$route.path&&"/index"!=this.$route.path||(this.isIndex=!0)},watch:{$route:function(t){localStorage.getItem("isAdList");"/"==this.$route.path||"/index"==this.$route.path?this.isIndex=!0:this.isIndex=!1}},methods:{getCategoryConfig:function(){var t=this;Object(at.a)({}).then((function(e){0==e.code&&e.data&&(t.categoryConfig=e.data,t.getTree(e.data))})).catch((function(e){t.$message.error(e.message)}))},getTree:function(t){var e=this;Object(at.d)({level:3,template:2}).then((function(t){if(0==t.code&&t.data){e.goodsCategoryTree=t.data||[];for(var i=0;i<e.goodsCategoryTree.length;i++)e.goodsCategoryTree[i].child_list>3&&(e.isHide=!0)}})).catch((function(t){e.$message.error(t.message)}))},shopHover:function(){this.isShopHover=!0},shopLeave:function(){this.isShopHover=!1}}},ut=ct,lt=(n(425),Object(P.a)(ut,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"header"},[e("ns-header-top"),t._v(" "),e("ns-header-mid"),t._v(" "),e("div",{staticClass:"nav"},[e("div",{staticClass:"shop-list-content",class:t.forceExpand||t.isShopHover||t.isIndex&&t.is_show?"shop-list-active":"shadow"},[1==t.categoryConfig.category?e("div",{staticClass:"shop-list",on:{mouseover:t.shopHover,mouseleave:t.shopLeave}},t._l(t.goodsCategoryTree,(function(n,r){return e("div",{key:r,staticClass:"list-item",on:{mouseover:function(e){t.selectedCategory=n.category_id},mouseleave:function(e){t.selectedCategory=-1}}},[e("router-link",{attrs:{to:{path:"/goods/list",query:{category_id:n.category_id,level:n.level}},target:"_blank"}},[e("div",[1==t.categoryConfig.img&&n.image?e("img",{staticClass:"category-img",attrs:{src:t.$util.img(n.image)}}):t._e(),t._v(" "),e("p",{staticClass:"category-name"},[t._v(t._s(n.category_name))])]),t._v(" "),e("i",{staticClass:"el-icon-arrow-right",attrs:{"aria-hidden":"true"}})])],1)})),0):2==t.categoryConfig.category?e("div",{staticClass:"shop-list",class:t.forceExpand||t.isShopHover||t.isIndex&&t.is_show?"shop-list-active":"shadow",on:{mouseover:t.shopHover,mouseleave:t.shopLeave}},t._l(t.goodsCategoryTree,(function(n,r){return e("div",{key:r,staticClass:"list-item",on:{mouseover:function(e){t.selectedCategory=n.category_id},mouseleave:function(e){t.selectedCategory=-1}}},[e("router-link",{attrs:{to:{path:"/goods/list",query:{category_id:n.category_id,level:n.level}},target:"_blank"}},[e("div",[1==t.categoryConfig.img&&n.image?e("img",{staticClass:"category-img",attrs:{src:t.$util.img(n.image)}}):t._e(),t._v(" "),e("p",{staticClass:"category-name"},[t._v(t._s(n.category_name))])]),t._v(" "),e("i",{staticClass:"el-icon-arrow-right",attrs:{"aria-hidden":"true"}})]),t._v(" "),n.child_list?e("div",{staticClass:"cate-part",class:{show:t.selectedCategory==n.category_id}},[e("div",{staticClass:"cate-part-col1"},[e("div",{staticClass:"cate-detail"},[e("div",{staticClass:"cate-detail-item"},[e("div",{staticClass:"cate-detail-con"},t._l(n.child_list,(function(n,r){return e("router-link",{key:r,attrs:{target:"_blank",to:{path:"/goods/list",query:{category_id:n.category_id,level:n.level}}}},[1==t.categoryConfig.img?e("img",{staticClass:"cate-detail-img",attrs:{src:t.$util.img(n.image)}}):t._e(),t._v(" "),e("p",{staticClass:"category-name"},[t._v(t._s(n.category_name))])])})),1)])])])]):t._e()],1)})),0):3==t.categoryConfig.category?e("div",{staticClass:"shop-list",class:t.forceExpand||t.isShopHover||t.isIndex?"shop-list-active":"shadow",on:{mouseover:t.shopHover,mouseleave:t.shopLeave}},t._l(t.goodsCategoryTree,(function(n,r){return e("div",{key:r,staticClass:"list-item",on:{mouseover:function(e){t.selectedCategory=n.category_id},mouseleave:function(e){t.selectedCategory=-1}}},[e("router-link",{attrs:{to:{path:"/goods/list",query:{category_id:n.category_id,level:n.level}},target:"_blank"}},[e("div",{staticClass:"list-item-left"},[1==t.categoryConfig.img&&n.image?e("img",{staticClass:"category-img",attrs:{src:t.$util.img(n.image)}}):t._e(),t._v(" "),e("p",{staticClass:"category-name"},[t._v(t._s(n.category_name))])])]),t._v(" "),e("div",{staticClass:"item-itm",class:{"item-itm-img":1==t.categoryConfig.img}},t._l(n.child_list,(function(n,r){return e("router-link",{directives:[{name:"show",rawName:"v-show",value:r<3,expression:"second_index < 3"}],key:r,staticStyle:{display:"inline-block"},attrs:{to:{path:"/goods/list",query:{category_id:n.category_id,level:n.level}},target:"_blank"}},[t._v("\n              "+t._s(n.short_name?n.short_name:n.category_name)+"\n            ")])})),1),t._v(" "),n.child_list?e("div",{staticClass:"cate-part",class:{show:t.selectedCategory==n.category_id}},[e("div",{staticClass:"cate-part-col1"},[e("div",{staticClass:"cate-detail"},t._l(n.child_list,(function(n,r){return e("dl",{key:r,staticClass:"cate-detail-item"},[e("dt",{staticClass:"cate-detail-tit"},[e("router-link",{attrs:{target:"_blank",to:{path:"/goods/list",query:{category_id:n.category_id,level:n.level}}}},[t._v("\n                      "+t._s(n.category_name)+"\n                    ")])],1),t._v(" "),n.child_list?e("dd",{staticClass:"cate-detail-con"},t._l(n.child_list,(function(n,r){return e("router-link",{key:r,attrs:{target:"_blank",to:{path:"/goods/list",query:{category_id:n.category_id,level:n.level}}}},[1==t.categoryConfig.img?e("img",{staticClass:"cate-detail-img",attrs:{src:t.$util.img(n.image)}}):t._e(),t._v(" "),e("p",{staticClass:"category-name"},[t._v(t._s(n.category_name))])])})),1):t._e()])})),0)])]):t._e()],1)})),0):t._e()])])],1)}),[],!1,null,"3f15d160",null).exports),ft={name:"Layout",components:{NsHeader:lt,NsFooter:it,NsAside:et},created:function(){this.getAdList()},data:function(){return{loadingAd:!0,adList:[],is_show:!0,indexTopAdNum:0,adv_position:{}}},mounted:function(){},computed:{},watch:{},methods:{getAdList:function(){var t=this;if(this.$store.state.app.indexTopAdNum>=3)return this.loadingAd=!1,void(this.is_show=!1);Object(M.a)({keyword:"NS_PC_INDEX_TOP"}).then((function(e){t.adList=e.data.adv_list,t.adv_position=e.data.adv_position;for(var i=0;i<t.adList.length;i++)t.adList[i].adv_url&&(t.adList[i].adv_url=JSON.parse(t.adList[i].adv_url));t.loadingAd=!1})).catch((function(e){t.loadingAd=!1}))},closeAd:function(){this.is_show=!1,this.indexTopAdNum=this.$store.state.app.indexTopAdNum,this.indexTopAdNum++,this.$store.commit("app/SET_INDEXTOPADNUM",this.indexTopAdNum)}},head:function(){return{title:this.$store.state.site.siteInfo.site_name}}},pt=(n(426),Object(P.a)(ft,(function(){var t=this,e=t._self._c;return e("el-container",{staticClass:"default-container"},[t.is_show&&t.adList.length&&t.adv_position?e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingAd,expression:"loadingAd"}],staticClass:"banner"},[e("el-carousel",{attrs:{height:"70px",arrow:"hover",direction:"vertical","indicator-position":"none"}},t._l(t.adList,(function(n){return e("el-carousel-item",{key:n.adv_id,style:{backgroundColor:n.background}},[e("el-image",{attrs:{src:t.$img(n.adv_image),fit:"cover"},on:{click:function(e){return t.$util.pushToTab(n.adv_url.url)}}})],1)})),1),t._v(" "),e("i",{staticClass:"el-icon-circle-close",on:{click:t.closeAd}})],1):t._e(),t._v(" "),e("el-header",[e("ns-header")],1),t._v(" "),e("el-main",[e("transition",{attrs:{name:"slide"}},[e("nuxt")],1),t._v(" "),e("ns-aside")],1),t._v(" "),e("el-footer",[e("ns-footer")],1)],1)}),[],!1,null,"7cda98e6",null).exports),mt={head:function(){return{title:this.$store.state.site.siteInfo.site_name}}},ht=Object(P.a)(mt,(function(){return(0,this._self._c)("nuxt")}),[],!1,null,null,null).exports;function gt(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var bt={components:{CopyRight:B,NsHeaderMid:Y.a},created:function(){this.$store.dispatch("site/siteInfo")},mounted:function(){},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?gt(Object(source),!0).forEach((function(e){Object(c.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):gt(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(d.b)(["siteInfo"])),watch:{},methods:{},head:function(){return{title:this.$store.state.site.siteInfo.site_name}}},vt=bt,_t=(n(427),n(428),Object(P.a)(vt,(function(){var t=this,e=t._self._c;return e("el-container",{staticClass:"auth-container"},[e("el-header",{staticClass:"ns-login-header"},[e("div",{staticClass:"header-in"},[e("el-row",[e("el-col",{attrs:{span:6}},[e("router-link",{staticClass:"logo-wrap",attrs:{to:"/"}},[e("img",{attrs:{src:t.$img(t.siteInfo.logo),alt:""}})])],1)],1)],1)]),t._v(" "),e("el-main",[e("transition",{attrs:{name:"slide"}},[e("nuxt")],1)],1)],1)}),[],!1,null,"70bb6330",null).exports),yt={created:function(){this.activeIndex=this.$route.meta.parentRouter||this.$route.path},data:function(){return{defaultOpeneds:["1"],activeIndex:"member"}},mounted:function(){},computed:{},watch:{$route:function(t){this.activeIndex=t.meta.parentRouter||this.$route.path}},methods:{handleOpen:function(t,e){this.defaultOpeneds=e}},components:{MemberHeader:X,NsAside:et,NsFooter:it},middleware:"auth",head:function(){return{title:this.$store.state.site.siteInfo.site_name}}},Ot=(n(429),n(430),Object(P.a)(yt,(function(){var t=this,e=t._self._c;return e("el-container",[e("el-header",{staticClass:"header",attrs:{height:"auto"}},[e("member-header")],1),t._v(" "),e("transition",{attrs:{name:"slide"}},[e("el-container",{staticClass:"member-content-wrap"},[e("el-aside",{attrs:{width:"200px"}},[e("el-menu",{staticClass:"menu",attrs:{"default-active":t.activeIndex,router:"","default-openeds":t.defaultOpeneds,"unique-opened":""},on:{open:t.handleOpen}},[e("el-submenu",{attrs:{index:"1",title:""}},[e("template",{slot:"title"},[e("span",[t._v("会员中心")])]),t._v(" "),e("el-menu-item",{attrs:{index:"/member"}},[t._v("欢迎页")]),t._v(" "),e("el-menu-item",{attrs:{index:"/member/info"}},[t._v("个人信息")]),t._v(" "),e("el-menu-item",{attrs:{index:"/member/security"}},[t._v("账户安全")]),t._v(" "),e("el-menu-item",{attrs:{index:"/member/delivery_address"}},[t._v("收货地址")]),t._v(" "),e("el-menu-item",{attrs:{index:"/member/collection"}},[t._v("我的关注")]),t._v(" "),e("el-menu-item",{attrs:{index:"/member/footprint"}},[t._v("我的足迹")])],2),t._v(" "),e("el-submenu",{attrs:{index:"2",title:""}},[e("template",{slot:"title"},[e("span",[t._v("交易中心")])]),t._v(" "),e("el-menu-item",{attrs:{index:"/member/order_list"}},[t._v("我的订单")]),t._v(" "),e("el-menu-item",{attrs:{index:"/member/activist"}},[t._v("退款/售后")])],2),t._v(" "),e("el-submenu",{attrs:{index:"3",title:""}},[e("template",{slot:"title"},[e("span",[t._v("账户中心")])]),t._v(" "),e("el-menu-item",{attrs:{index:"/member/account"}},[t._v("账户余额")]),t._v(" "),e("el-menu-item",{attrs:{index:"/member/withdrawal"}},[t._v("提现记录")]),t._v(" "),e("el-menu-item",{attrs:{index:"/member/coupon"}},[t._v("我的优惠券")]),t._v(" "),e("el-menu-item",{attrs:{index:"/member/my_point"}},[t._v("我的积分")]),t._v(" "),e("el-menu-item",{attrs:{index:"/member/account_list"}},[t._v("账户列表")])],2)],1)],1),t._v(" "),e("el-main",{staticClass:"member"},[e("transition",{attrs:{name:"slide"}},[e("nuxt")],1)],1)],1)],1),t._v(" "),e("ns-aside"),t._v(" "),e("el-footer",[e("ns-footer")],1)],1)}),[],!1,null,"79e221b8",null).exports);function jt(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return wt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return wt(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,c=!0,l=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return c=t.done,t},e:function(t){l=!0,o=t},f:function(){try{c||null==n.return||n.return()}finally{if(l)throw o}}}}function wt(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}var xt={"_components/CopyRight":Object(j.s)(B),"_components/MemberHeader":Object(j.s)(X),"_components/NsAside":Object(j.s)(et),"_components/NsFooter":Object(j.s)(it),"_components/NsHeader":Object(j.s)(lt),"_components/NsHeaderMid":Object(j.s)(Y.a),"_components/NsHeaderTop":Object(j.s)(Q),_default:Object(j.s)(pt),_empty:Object(j.s)(ht),_login:Object(j.s)(_t),_member:Object(j.s)(Ot)},Ct={render:function(t,e){var n=t("NuxtLoading",{ref:"loading"}),r=t(this.layout||"nuxt"),o=t("div",{domProps:{id:"__layout"},key:this.layoutName},[r]),c=t("transition",{props:{name:"layout",mode:"out-in"},on:{beforeEnter:function(t){window.$nuxt.$nextTick((function(){window.$nuxt.$emit("triggerScroll")}))}}},[o]);return t("div",{domProps:{id:"__nuxt"}},[n,c])},data:function(){return{isOnline:!0,layout:null,layoutName:"",nbFetching:0}},beforeCreate:function(){l.default.util.defineReactive(this,"nuxt",this.$options.nuxt)},created:function(){this.$root.$options.$nuxt=this,window.$nuxt=this,this.refreshOnlineStatus(),window.addEventListener("online",this.refreshOnlineStatus),window.addEventListener("offline",this.refreshOnlineStatus),this.error=this.nuxt.error,this.context=this.$options.context},mounted:function(){var t=this;return Object(o.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.$loading=t.$refs.loading;case 1:case"end":return e.stop()}}),e)})))()},watch:{"nuxt.err":"errorChanged"},computed:{isOffline:function(){return!this.isOnline},isFetching:function(){return this.nbFetching>0},isPreview:function(){return Boolean(this.$options.previewData)}},methods:{refreshOnlineStatus:function(){void 0===window.navigator.onLine?this.isOnline=!0:this.isOnline=window.navigator.onLine},refresh:function(){var t=this;return Object(o.a)(regeneratorRuntime.mark((function e(){var n,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((n=Object(j.h)(t.$route)).length){e.next=3;break}return e.abrupt("return");case 3:return t.$loading.start(),r=n.map((function(e){var p=[];if(e.$options.fetch&&e.$options.fetch.length&&p.push(Object(j.q)(e.$options.fetch,t.context)),e.$fetch)p.push(e.$fetch());else{var n,r=jt(Object(j.e)(e.$vnode.componentInstance));try{for(r.s();!(n=r.n()).done;){var component=n.value;p.push(component.$fetch())}}catch(t){r.e(t)}finally{r.f()}}return e.$options.asyncData&&p.push(Object(j.q)(e.$options.asyncData,t.context).then((function(t){for(var n in t)l.default.set(e.$data,n,t[n])}))),Promise.all(p)})),e.prev=5,e.next=8,Promise.all(r);case 8:e.next=15;break;case 10:e.prev=10,e.t0=e.catch(5),t.$loading.fail(e.t0),Object(j.k)(e.t0),t.error(e.t0);case 15:t.$loading.finish();case 16:case"end":return e.stop()}}),e,null,[[5,10]])})))()},errorChanged:function(){if(this.nuxt.err){this.$loading&&(this.$loading.fail&&this.$loading.fail(this.nuxt.err),this.$loading.finish&&this.$loading.finish());var t=(A.options||A).layout;"function"==typeof t&&(t=t(this.context)),this.setLayout(t)}},setLayout:function(t){return t&&xt["_"+t]||(t="default"),this.layoutName=t,this.layout=xt["_"+t],this.layout},loadLayout:function(t){return t&&xt["_"+t]||(t="default"),Promise.resolve(xt["_"+t])}},components:{NuxtLoading:N}};n(107);l.default.use(d.a);var kt=["state","getters","actions","mutations"],St={};St.modules=St.modules||{},It(n(431),"app.js"),It(n(432),"cart.js"),It(n(433),"getters.js"),It(n(434),"member.js"),It(n(435),"order.js"),It(n(534),"site.js"),It(n(436),"wechat.js");var Et=St instanceof Function?St:function(){return new d.a.Store(Object.assign({strict:!1},St))};function Tt(t,e){if(t.state&&"function"!=typeof t.state){console.warn("'state' should be a method that returns an object in ".concat(e));var n=Object.assign({},t.state);t=Object.assign({},t,{state:function(){return n}})}return t}function It(t,e){t=t.default||t;var n=e.replace(/\.(js|mjs)$/,"").split("/"),r=n[n.length-1],o="store/".concat(e);if(t="state"===r?function(t,e){if("function"!=typeof t){console.warn("".concat(e," should export a method that returns an object"));var n=Object.assign({},t);return function(){return n}}return Tt(t,e)}(t,o):Tt(t,o),kt.includes(r)){var c=r;Pt($t(St,n,{isProperty:!0}),t,c)}else{"index"===r&&(n.pop(),r=n[n.length-1]);for(var l=$t(St,n),d=0,f=kt;d<f.length;d++){var m=f[d];Pt(l,t[m],m)}!1===t.namespaced&&delete l.namespaced}}function $t(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.isProperty,o=void 0!==r&&r;if(!e.length||o&&1===e.length)return t;var c=e.shift();return t.modules[c]=t.modules[c]||{},t.modules[c].namespaced=!0,t.modules[c].modules=t.modules[c].modules||{},$t(t.modules[c],e,{isProperty:o})}function Pt(t,e,n){e&&("state"===n?t.state=e||t.state:t[n]=Object.assign({},t[n],e))}n(132);var At=function(){return n.e(4).then(n.bind(null,655)).then((function(t){return Mt(t.default||t)}))},Rt=function(){return n.e(5).then(n.bind(null,544)).then((function(t){return Mt(t.default||t)}))},Lt=function(){return Promise.resolve().then(n.bind(null,313)).then((function(t){return Mt(t.default||t)}))},Dt=function(){return Promise.resolve().then(n.bind(null,314)).then((function(t){return Mt(t.default||t)}))},Nt=function(){return Promise.resolve().then(n.bind(null,217)).then((function(t){return Mt(t.default||t)}))};function Mt(t){if(!t||!t.functional)return t;var e=Array.isArray(t.props)?t.props:Object.keys(t.props||{});return{render:function(n){var r={},o={};for(var c in this.$attrs)e.includes(c)?o[c]=this.$attrs[c]:r[c]=this.$attrs[c];return n(t,{on:this.$listeners,attrs:r,props:o,scopedSlots:this.$scopedSlots},this.$slots.default)}}}for(var Ut in r)l.default.component(Ut,r[Ut]),l.default.component("Lazy"+Ut,r[Ut]);var Ft=n(34),qt=n.n(Ft),Ht=n(310);function Bt(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function Gt(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?Bt(Object(source),!0).forEach((function(e){Object(c.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):Bt(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}function Kt(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return Jt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Jt(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,c=!0,l=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return c=t.done,t},e:function(t){l=!0,o=t},f:function(){try{c||null==n.return||n.return()}finally{if(l)throw o}}}}function Jt(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}for(var Qt={setBaseURL:function(t){this.defaults.baseURL=t},setHeader:function(t,e){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"common",o=Kt(Array.isArray(r)?r:[r]);try{for(o.s();!(n=o.n()).done;){var c=n.value;e?this.defaults.headers[c][t]=e:delete this.defaults.headers[c][t]}}catch(t){o.e(t)}finally{o.f()}},setToken:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"common",r=t?(e?e+" ":"")+t:null;this.setHeader("Authorization",r,n)},onRequest:function(t){this.interceptors.request.use((function(e){return t(e)||e}))},onResponse:function(t){this.interceptors.response.use((function(e){return t(e)||e}))},onRequestError:function(t){this.interceptors.request.use(void 0,(function(e){return t(e)||Promise.reject(e)}))},onResponseError:function(t){this.interceptors.response.use(void 0,(function(e){return t(e)||Promise.reject(e)}))},onError:function(t){this.onRequestError(t),this.onResponseError(t)},create:function(t){return zt(Object(Ht.a)(t,this.defaults))}},Yt=function(){var t=Xt[Wt];Qt["$"+t]=function(){return this[t].apply(this,arguments).then((function(t){return t&&t.data}))}},Wt=0,Xt=["request","delete","get","head","options","post","put","patch"];Wt<Xt.length;Wt++)Yt();var zt=function(t){var e=qt.a.create(t);return e.CancelToken=qt.a.CancelToken,e.isCancel=qt.a.isCancel,function(t){for(var e in Qt)t[e]=Qt[e].bind(t)}(e),e.onRequest((function(t){t.headers=Gt(Gt({},e.defaults.headers.common),t.headers)})),Vt(e),e},Vt=function(t){var e={finish:function(){},start:function(){},fail:function(){},set:function(){}},n=function(){var t="undefined"!=typeof window&&window.$nuxt;return t&&t.$loading&&t.$loading.set?t.$loading:e},r=0;t.onRequest((function(t){t&&!1===t.progress||r++})),t.onResponse((function(t){t&&t.config&&!1===t.config.progress||--r<=0&&(r=0,n().finish())})),t.onError((function(t){t&&t.config&&!1===t.config.progress||(r--,qt.a.isCancel(t)?r<=0&&(r=0,n().finish()):(n().fail(),n().finish()))}));var o=function(t){if(r&&t.total){var progress=100*t.loaded/(t.total*r);n().set(Math.min(100,progress))}};t.defaults.onUploadProgress=o,t.defaults.onDownloadProgress=o},Zt=function(t,e){var n=t.$config&&t.$config.axios||{},r=n.browserBaseURL||n.browserBaseUrl||n.baseURL||n.baseUrl||"http://localhost:3000/";var o=zt({baseURL:r,headers:{common:{Accept:"application/json, text/plain, */*"},delete:{},get:{},head:{},post:{},put:{},patch:{}}});t.$axios=o,e("axios",o)},te=n(15),ee=(n(151),n(437),n(444),n(445),n(447),n(448),n(449),n(450),n(452),n(453),n(454),n(455),n(456),n(457),n(458),n(315),n(33)),ne={img:function(t,e){var path="";if(null!=t&&"string"==typeof t&&""!=t){if(t.split(",").length>1&&(t=t.split(",")[0]),e&&t!=this.$store.getters.defaultGoodsImage){var n=t.split("."),r=n[n.length-1];n.pop(),n[n.length-1]=n[n.length-1]+"_"+e.size.toUpperCase(),n.push(r),t=n.join(".")}path=-1==t.indexOf("http://")&&-1==t.indexOf("https://")?ee.a.imgDomain+"/"+t:t}return path},timeStampTurnTime:function(t){if(null!=t&&""!=t&&t>0){var e=new Date;e.setTime(1e3*t);var n=e.getFullYear(),r=e.getMonth()+1;r=r<10?"0"+r:r;var o=e.getDate();o=o<10?"0"+o:o;var c=e.getHours();c=c<10?"0"+c:c;var l=e.getMinutes(),d=e.getSeconds();return n+"-"+r+"-"+o+" "+c+":"+(l=l<10?"0"+l:l)+":"+(d=d<10?"0"+d:d)}return""},countDown:function(t){var e=0,n=0,r=0,o=0;return t>0&&(e=Math.floor(t/86400),n=Math.floor(t/3600)-24*e,r=Math.floor(t/60)-24*e*60-60*n,o=Math.floor(t)-24*e*60*60-60*n*60-60*r),{d:e,h:n,i:r,s:o}},unique:function(t,e){var n=new Map;return t.filter((function(a){return!n.has(a[e])&&n.set(a[e],1)}))},inArray:function(t,e){return null==e?-1:e.indexOf(t)},getDay:function(t){var e=new Date,n=e.getTime()+864e5*t;e.setTime(n);var r=function(t){var e=t;return 1==t.toString().length&&(e="0"+t),e},o=e.getFullYear(),c=e.getMonth(),l=e.getDate(),d=e.getDay();return{t:parseInt(e.getTime()/1e3),y:o,m:c=r(c+1),d:l=r(l),w:["周日","周一","周二","周三","周四","周五","周六"][d]}},copy:function(t,e){var n=document.createElement("input");n.value=t,document.body.appendChild(n),n.select(),document.execCommand("Copy"),n.className="oInput",n.style.display="none",this.$message({message:"复制成功",type:"success"}),"function"==typeof e&&e()},deepClone:function(t){var e=function(t){return"object"==Object(te.a)(t)};if(!e(t))throw new Error("obj 不是一个对象！");var n=Array.isArray(t)?[]:{};for(var r in t)n[r]=e(t[r])?this.deepClone(t[r]):t[r];return n},filterPrice:function(t){return t.toFixed(2)},pushToTab:function(t){if("string"==typeof t){var e="";e=-1!=t.indexOf("http")||-1!=t.indexOf("https")?t:ee.a.webDomain+t,window.open(e,"_blank")}else if("object"==Object(te.a)(t)){var n="";n=-1!=t.path.indexOf("http")||-1!=t.path.indexOf("https")?t.path:ee.a.webDomain+t.path,t.qurey&&Object.keys(t.qurey).forEach((function(e,n){t.qurey+=(0==n?"?":"&")+e+"="+t.qurey[e]})),window.open(n,"_blank")}},verifyMobile:function(t){return/^\d{11}$/.test(t)},handleLink:function(t){var e={};for(var n in t)t[n]&&(e[n]=t[n]);return e}};l.default.prototype.$util=ne,l.default.prototype.$img=ne.img,l.default.prototype.$timeStampTurnTime=ne.timeStampTurnTime,l.default.prototype.$copy=ne.copy;var re=n(147),oe=n.n(re),ie=(n(518),null),ae=function(t){ie&&ie.close(),ie=Object(re.Message)(t)};["error","success","info","warning"].forEach((function(t){ae[t]=function(e){return"string"==typeof e&&(e={message:e}),e.type=t,ae(e)}})),l.default.use(oe.a),l.default.prototype.$message=ae;var se=n(311),ce=n.n(se);n(532);l.default.use(ce.a);var ue=function(t){var e=t.app;t.redirect;e.router.afterEach((function(t,e){window.scrollTo(0,0)}))};function le(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function de(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?le(Object(source),!0).forEach((function(e){Object(c.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):le(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}l.default.component(h.a.name,h.a),l.default.component(_.a.name,de(de({},_.a),{},{render:function(t,e){return _.a._warned||(_.a._warned=!0,console.warn("<no-ssr> has been deprecated and will be removed in Nuxt 3, please use <client-only> instead")),_.a.render(t,e)}})),l.default.component(E.name,E),l.default.component("NChild",E),l.default.component(L.name,L),Object.defineProperty(l.default.prototype,"$nuxt",{get:function(){var t=this.$root.$options.$nuxt;return t||"undefined"==typeof window?t:window.$nuxt},configurable:!0}),l.default.use(f.a,{keyName:"head",attribute:"data-n-head",ssrAttribute:"data-n-head-ssr",tagIDKeyName:"hid"});var fe={name:"page",mode:"out-in",appear:!0,appearClass:"appear",appearActiveClass:"appear-active",appearToClass:"appear-to"};d.a.Store.prototype.registerModule;function pe(t){return me.apply(this,arguments)}function me(){return me=Object(o.a)(regeneratorRuntime.mark((function t(e){var n,r,c,d,f,m,path,h,v=arguments;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return h=function(t,e){if(!t)throw new Error("inject(key, value) has no key provided");if(void 0===e)throw new Error("inject('".concat(t,"', value) has no value provided"));d[t="$"+t]=e,d.context[t]||(d.context[t]=e),c[t]=d[t];var n="__nuxt_"+t+"_installed__";l.default[n]||(l.default[n]=!0,l.default.use((function(){Object.prototype.hasOwnProperty.call(l.default.prototype,t)||Object.defineProperty(l.default.prototype,t,{get:function(){return this.$root.$options[t]}})})))},n=v.length>1&&void 0!==v[1]?v[1]:{},t.next=4,S(0,n);case 4:return r=t.sent,(c=Et(e)).$router=r,d=de({head:{title:"",htmlAttrs:{lang:"en"},meta:[{charset:"utf-8"},{name:"viewport",content:"width=device-width, initial-scale=1"},{hid:"description",name:"description",content:""},{name:"format-detection",content:"telephone=no"}],link:[{rel:"icon",type:"image/x-icon",href:"/favicon.ico"}],style:[],script:[]},store:c,router:r,nuxt:{defaultTransition:fe,transitions:[fe],setTransitions:function(t){return Array.isArray(t)||(t=[t]),t=t.map((function(t){return t=t?"string"==typeof t?Object.assign({},fe,{name:t}):Object.assign({},fe,t):fe})),this.$options.nuxt.transitions=t,t},err:null,dateErr:null,error:function(t){t=t||null,d.context._errored=Boolean(t),t=t?Object(j.p)(t):null;var n=d.nuxt;return this&&(n=this.nuxt||this.$options.nuxt),n.dateErr=Date.now(),n.err=t,e&&(e.nuxt.error=t),t}}},Ct),c.app=d,f=e?e.next:function(t){return d.router.push(t)},e?m=r.resolve(e.url).route:(path=Object(j.f)(r.options.base,r.options.mode),m=r.resolve(path).route),t.next=13,Object(j.t)(d,{store:c,route:m,next:f,error:d.nuxt.error.bind(d),payload:e?e.payload:void 0,req:e?e.req:void 0,res:e?e.res:void 0,beforeRenderFns:e?e.beforeRenderFns:void 0,ssrContext:e});case 13:h("config",n),window.__NUXT__&&window.__NUXT__.state&&c.replaceState(window.__NUXT__.state),d.context.enablePreview=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};d.previewData=Object.assign({},t),h("preview",t)},t.next=19;break;case 19:return t.next=22,Zt(d.context,h);case 22:t.next=25;break;case 25:if("function"!=typeof ee.a){t.next=28;break}return t.next=28,Object(ee.a)(d.context,h);case 28:t.next=31;break;case 31:t.next=34;break;case 34:return t.next=37,ue(d.context);case 37:return d.context.enablePreview=function(){console.warn("You cannot call enablePreview() outside a plugin.")},t.next=40,new Promise((function(t,e){if(!r.resolve(d.context.route.fullPath).route.matched.length)return t();r.replace(d.context.route.fullPath,t,(function(n){if(!n._isRouter)return e(n);if(2!==n.type)return t();var c=r.afterEach(function(){var e=Object(o.a)(regeneratorRuntime.mark((function e(n,r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=3,Object(j.j)(n);case 3:d.context.route=e.sent,d.context.params=n.params||{},d.context.query=n.query||{},c(),t();case 8:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}())}))}));case 40:return t.abrupt("return",{store:c,app:d,router:r});case 41:case"end":return t.stop()}}),t)}))),me.apply(this,arguments)}},61:function(t,e,n){"use strict";n(31),n(154),n(24),n(25),n(23),n(7),n(29),n(18),n(30);var r=n(10),o=n(12),c=n(270),l=n(27);function d(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var f={props:{},data:function(){return{keyword:"",defaultSearchWords:"请输入您要查询的商品",cartTotalPrice:0,navList:[],navSelect:"",searchType:"goods"}},components:{},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(o.b)(["siteInfo","defaultGoodsImage","member"])),created:function(){this.keyword=this.$route.query.keyword||"",this.$store.dispatch("site/siteInfo"),this.getDefaultSearchWords(),this.nav()},watch:{$route:function(t){this.initNav(t.path),this.keyword!==t.query.keyword&&(this.keyword=t.query.keyword),"/goods/list"==t.path&&(this.navSelect="")},member:function(){this.member||(this.$store.commit("cart/SET_CART_COUNT",0),this.cartTotalPrice=0)}},methods:{search:function(){if("goods"==this.searchType){this.defaultSearchWords="请输入您要查询的商品"==this.defaultSearchWords?"":this.defaultSearchWords;var t=this.keyword?this.keyword:this.defaultSearchWords,e={};t&&(e.keyword=t),this.$router.push({path:"/goods/list",query:e})}else this.$router.push({path:"/street",query:{keyword:this.keyword}})},getDefaultSearchWords:function(){var t=this;Object(c.a)({}).then((function(e){e&&0==e.code&&e.data.words&&(t.defaultSearchWords=e.data.words)}))},nav:function(){var t=this;Object(l.f)({}).then((function(e){if(0==e.code&&e.data){for(var i in t.navList=e.data,t.navList)t.navList[i].url=JSON.parse(t.navList[i].nav_url).url;t.initNav(t.$route.path)}})).catch((function(e){t.$message.error(e.message)}))},initNav:function(path){for(var i in this.navList)this.navList[i].url!=path||(this.navSelect=path)},navUrl:function(t,e){if(t)if(-1==t.indexOf("http")||-1==t.indexOf("https"))if(e){var n=this.$router.resolve({path:t});window.open(n.href,"_blank")}else this.$router.push({path:t});else e?window.open(t):window.location.href=t}}},m=f,h=(n(405),n(6)),component=Object(h.a)(m,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"header-in"},[e("router-link",{staticClass:"header-left",attrs:{to:"/"}},[t.siteInfo.logo?e("img",{attrs:{src:t.$img(t.siteInfo.logo)}}):t._e()]),t._v(" "),e("div",{staticClass:"header-content"},[e("nav",[e("ul",t._l(t.navList,(function(n,r){return e("li",{key:r,class:n.url==t.navSelect?"router-link-active":"",on:{click:function(e){return t.navUrl(n.url,n.is_blank)}}},[t._v("\n          "+t._s(n.nav_title)+"\n        ")])})),0)])]),t._v(" "),e("div",{staticClass:"header-right"},[e("span",{staticClass:"iconfont icon-xiaosuo"}),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.keyword,expression:"keyword"}],attrs:{type:"text",placeholder:t.defaultSearchWords,maxlength:"50"},domProps:{value:t.keyword},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search.apply(null,arguments)},input:function(e){e.target.composing||(t.keyword=e.target.value)}}}),t._v(" "),e("el-button",{attrs:{size:"small"},on:{click:t.search}},[t._v("搜索")])],1)],1)}),[],!1,null,"235001d5",null);e.a=component.exports},62:function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return c})),n.d(e,"c",(function(){return l})),n.d(e,"d",(function(){return d})),n.d(e,"e",(function(){return f}));var r=n(1);function o(t){return Object(r.a)({data:t,url:"/api/cart/add",forceLogin:!0})}function c(t){return Object(r.a)({data:t,url:"/api/cart/goodslists"})}function l(t){return Object(r.a)({data:t,url:"/api/cart/delete",forceLogin:!0})}function d(t){return Object(r.a)({data:t,url:"/api/cart/edit",forceLogin:!0})}function f(t){return Object(r.a)({data:t,url:"/api/cart/count"})}}},[[332,73,2,74]]]);