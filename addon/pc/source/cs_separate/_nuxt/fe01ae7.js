(window.webpackJsonp=window.webpackJsonp||[]).push([[20,4,5],{535:function(e,t,r){"use strict";var n=r(3),o=r(35),c=r(14),l=r(8),path=r(319),d=r(5),f=r(113),_=r(16),h=r(205),v=r(63),m=r(112),y=r(318),w=r(4),C=r(93).f,k=r(57).f,x=r(26).f,A=r(320),E=r(316).trim,L="Number",O=l[L],I=path[L],P=O.prototype,S=l.TypeError,$=d("".slice),N=d("".charCodeAt),j=function(e){var t=y(e,"number");return"bigint"==typeof t?t:T(t)},T=function(e){var t,r,n,o,c,l,d,code,f=y(e,"number");if(m(f))throw S("Cannot convert a Symbol value to a number");if("string"==typeof f&&f.length>2)if(f=E(f),43===(t=N(f,0))||45===t){if(88===(r=N(f,2))||120===r)return NaN}else if(48===t){switch(N(f,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+f}for(l=(c=$(f,2)).length,d=0;d<l;d++)if((code=N(c,d))<48||code>o)return NaN;return parseInt(c,n)}return+f},G=f(L,!O(" 0o1")||!O("0b1")||O("+0x1")),B=function(e){return v(P,e)&&w((function(){A(e)}))},D=function(e){var t=arguments.length<1?0:O(j(e));return B(this)?h(Object(t),this,D):t};D.prototype=P,G&&!o&&(P.constructor=D),n({global:!0,constructor:!0,wrap:!0,forced:G},{Number:D});var R=function(e,source){for(var t,r=c?C(source):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),n=0;r.length>n;n++)_(source,t=r[n])&&!_(e,t)&&x(e,t,k(source,t))};o&&I&&R(path[L],I),(G||o)&&R(path[L],O)},536:function(e,t,r){},539:function(e,t,r){"use strict";r(536)},544:function(e,t,r){"use strict";r.r(t);r(24),r(25),r(23),r(7),r(29),r(18),r(30);var n=r(10),o=(r(535),r(12)),c=r(206);function l(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}var d={name:"goods_recommend",props:{page:{type:[Number,String],default:1},pageSize:{type:[Number,String],default:5}},data:function(){return{loading:!0,list:[]}},created:function(){this.getGoodsRecommend()},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(t){Object(n.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},Object(o.b)(["defaultGoodsImage"])),methods:{getGoodsRecommend:function(){var e=this;Object(c.e)({page:this.page,page_size:this.pageSize}).then((function(t){0==t.code&&(e.list=t.data.list),e.loading=!1})).catch((function(t){e.loading=!1}))},imageError:function(e){this.list[e].sku_image=this.defaultGoodsImage}}},f=d,_=(r(539),r(6)),component=Object(_.a)(f,(function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"goods-recommend"},[t("h4",[e._v("商品精选")]),e._v(" "),e.list.length?t("ul",e._l(e.list,(function(r,n){return t("li",{key:n,on:{click:function(t){return e.$util.pushToTab({path:"/sku/"+r.sku_id})}}},[t("div",{staticClass:"img-wrap"},[t("img",{attrs:{src:e.$img(r.sku_image,{size:"mid"})},on:{error:function(t){return e.imageError(n)}}})]),e._v(" "),t("div",{staticClass:"price"},[e._v("￥"+e._s(r.discount_price))]),e._v(" "),t("p",{staticClass:"sku-name"},[e._v(e._s(r.goods_name))]),e._v(" "),t("div",{staticClass:"info-wrap"})])})),0):e._e()])}),[],!1,null,"a68a46cc",null);t.default=component.exports},545:function(e,t,r){e.exports=r.p+"img/goods_empty.288af96.png"},549:function(e,t,r){},565:function(e,t,r){"use strict";r(549)},566:function(e,t,r){"use strict";var n=r(3),o=r(5),c=r(47),l=r(44),d=r(58),f=r(323),_=r(19),h=r(4),v=r(322),m=r(212),y=r(567),w=r(568),C=r(114),k=r(569),x=[],A=o(x.sort),E=o(x.push),L=h((function(){x.sort(void 0)})),O=h((function(){x.sort(null)})),I=m("sort"),P=!h((function(){if(C)return C<70;if(!(y&&y>3)){if(w)return!0;if(k)return k<603;var code,e,t,r,n="";for(code=65;code<76;code++){switch(e=String.fromCharCode(code),code){case 66:case 69:case 70:case 72:t=3;break;case 68:case 71:t=4;break;default:t=2}for(r=0;r<47;r++)x.push({k:e+r,v:t})}for(x.sort((function(a,b){return b.v-a.v})),r=0;r<x.length;r++)e=x[r].k.charAt(0),n.charAt(n.length-1)!==e&&(n+=e);return"DGBEFHACIJK"!==n}}));n({target:"Array",proto:!0,forced:L||!O||!I||!P},{sort:function(e){void 0!==e&&c(e);var t=l(this);if(P)return void 0===e?A(t):A(t,e);var r,n,o=[],h=d(t);for(n=0;n<h;n++)n in t&&E(o,t[n]);for(v(o,function(e){return function(t,r){return void 0===r?-1:void 0===t?1:void 0!==e?+e(t,r)||0:_(t)>_(r)?1:-1}}(e)),r=d(o),n=0;n<r;)t[n]=o[n++];for(;n<h;)f(t,n++);return t}})},567:function(e,t,r){var n=r(94).match(/firefox\/(\d+)/i);e.exports=!!n&&+n[1]},568:function(e,t,r){var n=r(94);e.exports=/MSIE|Trident/.test(n)},569:function(e,t,r){var n=r(94).match(/AppleWebKit\/(\d+)\./);e.exports=!!n&&+n[1]},592:function(e,t,r){},655:function(e,t,r){"use strict";r.r(t);r(23),r(7),r(92),r(56),r(317);function n(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],i=0;i<e.length;){var r=e[i];if("*"!==r&&"+"!==r&&"?"!==r)if("\\"!==r)if("{"!==r)if("}"!==r)if(":"!==r)if("("!==r)t.push({type:"CHAR",index:i,value:e[i++]});else{var n=1,pattern="";if("?"===e[c=i+1])throw new TypeError('Pattern cannot start with "?" at '+c);for(;c<e.length;)if("\\"!==e[c]){if(")"===e[c]){if(0==--n){c++;break}}else if("("===e[c]&&(n++,"?"!==e[c+1]))throw new TypeError("Capturing groups are not allowed at "+c);pattern+=e[c++]}else pattern+=e[c++]+e[c++];if(n)throw new TypeError("Unbalanced pattern at "+i);if(!pattern)throw new TypeError("Missing pattern at "+i);t.push({type:"PATTERN",index:i,value:pattern}),i=c}else{for(var o="",c=i+1;c<e.length;){var code=e.charCodeAt(c);if(!(code>=48&&code<=57||code>=65&&code<=90||code>=97&&code<=122||95===code))break;o+=e[c++]}if(!o)throw new TypeError("Missing parameter name at "+i);t.push({type:"NAME",index:i,value:o}),i=c}else t.push({type:"CLOSE",index:i,value:e[i++]});else t.push({type:"OPEN",index:i,value:e[i++]});else t.push({type:"ESCAPED_CHAR",index:i++,value:e[i++]});else t.push({type:"MODIFIER",index:i,value:e[i++]})}return t.push({type:"END",index:i,value:""}),t}(e),n=t.prefixes,o=void 0===n?"./":n,l="[^"+c(t.delimiter||"/#?")+"]+?",d=[],f=0,i=0,path="",_=function(e){if(i<r.length&&r[i].type===e)return r[i++].value},h=function(e){var t=_(e);if(void 0!==t)return t;var n=r[i],o=n.type,c=n.index;throw new TypeError("Unexpected "+o+" at "+c+", expected "+e)},v=function(){for(var e,t="";e=_("CHAR")||_("ESCAPED_CHAR");)t+=e;return t};i<r.length;){var m=_("CHAR"),y=_("NAME"),pattern=_("PATTERN");if(y||pattern){var w=m||"";-1===o.indexOf(w)&&(path+=w,w=""),path&&(d.push(path),path=""),d.push({name:y||f++,prefix:w,suffix:"",pattern:pattern||l,modifier:_("MODIFIER")||""})}else{var C=m||_("ESCAPED_CHAR");if(C)path+=C;else if(path&&(d.push(path),path=""),_("OPEN")){w=v();var k=_("NAME")||"",x=_("PATTERN")||"",A=v();h("CLOSE"),d.push({name:k||(x?f++:""),pattern:k&&!x?l:x,prefix:w,suffix:A,modifier:_("MODIFIER")||""})}else h("END")}}return d}function o(e,t){return function(e,t){void 0===t&&(t={});var r=l(t),n=t.encode,o=void 0===n?function(e){return e}:n,c=t.validate,d=void 0===c||c,f=e.map((function(e){if("object"==typeof e)return new RegExp("^(?:"+e.pattern+")$",r)}));return function(data){for(var path="",i=0;i<e.length;i++){var t=e[i];if("string"!=typeof t){var r=data?data[t.name]:void 0,n="?"===t.modifier||"*"===t.modifier,c="*"===t.modifier||"+"===t.modifier;if(Array.isArray(r)){if(!c)throw new TypeError('Expected "'+t.name+'" to not repeat, but got an array');if(0===r.length){if(n)continue;throw new TypeError('Expected "'+t.name+'" to not be empty')}for(var l=0;l<r.length;l++){var _=o(r[l],t);if(d&&!f[i].test(_))throw new TypeError('Expected all "'+t.name+'" to match "'+t.pattern+'", but got "'+_+'"');path+=t.prefix+_+t.suffix}}else if("string"!=typeof r&&"number"!=typeof r){if(!n){var h=c?"an array":"a string";throw new TypeError('Expected "'+t.name+'" to be '+h)}}else{_=o(String(r),t);if(d&&!f[i].test(_))throw new TypeError('Expected "'+t.name+'" to match "'+t.pattern+'", but got "'+_+'"');path+=t.prefix+_+t.suffix}}else path+=t}return path}}(n(e,t),t)}function c(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function l(e){return e&&e.sensitive?"":"i"}var d={props:{hasExtItem:!1},data:function(){return{levelList:null}},watch:{$route:function(){this.getBreadcrumb()}},created:function(){this.getBreadcrumb()},methods:{getBreadcrumb:function(){var e=this.$route.matched.filter((function(e){return e.meta&&e.meta.title})),t=e[0];this.isHome(t)||(e=[{path:"/index",meta:{title:"首页"}}].concat(e)),this.levelList=e.filter((function(e){return e.meta&&e.meta.title&&!1!==e.meta.breadcrumb}))},isHome:function(e){var t=e&&e.name;return!!t&&t.trim().toLocaleLowerCase()==="index".toLocaleLowerCase()},pathCompile:function(path){var e=this.$route.params;return o(path)(e)},handleLink:function(e){var t=e.redirect,path=e.path;t?this.$router.push(t):this.$router.push(this.pathCompile(path))}}},f=(r(565),r(6)),component=Object(f.a)(d,(function(){var e=this,t=e._self._c;return t("el-breadcrumb",{staticClass:"app-breadcrumb",attrs:{separator:"/"}},[t("transition-group",{attrs:{name:"breadcrumb"}},[e._l(e.levelList,(function(r,n){return t("el-breadcrumb-item",{key:r.path},[0==n?t("span",[t("i",{staticClass:"el-icon-s-home"})]):e._e(),e._v(" "),"noRedirect"===r.redirect||n==e.levelList.length-1?t("span",{staticClass:"no-redirect"},[e._v(e._s(r.meta.title))]):t("a",{on:{click:function(t){return t.preventDefault(),e.handleLink(r)}}},[e._v(e._s(r.meta.title))])])})),e._v(" "),e.hasExtItem?t("el-breadcrumb-item",{key:"ext_item"},[e._t("ext_item")],2):e._e()],2)],1)}),[],!1,null,"66cc7143",null);t.default=component.exports},680:function(e,t,r){"use strict";r(592)},751:function(e,t,r){"use strict";r.r(t);var n=r(655),o=r(544),c=(r(24),r(25),r(23),r(7),r(29),r(18),r(30),r(10)),l=(r(92),r(566),r(535),r(206)),d=r(12),f=r(152);function _(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}function h(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?_(Object(source),!0).forEach((function(t){Object(c.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):_(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var v={data:function(){return{goodsList:[],total:0,keyword:"",catewords:"",currentPage:1,pageSize:25,is_free_shipping:0,filters:{category_id:0,category_level:0,brand_id:0,min_price:"",max_price:"",order:"",sort:"desc",coupon:0},loading:!0,first_index:0,categoryList:[],selectCategoryId:0,categoryAll:{id:0,level:0,isAllow:!0},brandList:[],brandInitialList:[],currentInitial:"",choosedBrand:"",isShowMoreBrand:!1}},created:function(){this.keyword=this.$route.query.keyword||"",this.$route.query.keyword&&(window.document.title="".concat(this.$route.query.keyword," - ").concat(this.siteInfo.site_name)),this.filters.category_id=this.$route.query.category_id||"",this.filters.category_level=this.$route.query.level||"",this.filters.brand_id=this.$route.query.brand_id||"",this.filters.coupon=this.$route.query.coupon||0,this.getBrandList(),this.getGoodsList(),this.$route.query.category_id&&this.$route.query.category_id>0?this.categorySearch():this.getGoodsCategoryList()},computed:h({salesArrowDirection:function(){return"sale_num"===this.filters.order&&"desc"===this.filters.sort?"arrow-down":"arrow-up"},priceArrowDirection:function(){return"discount_price"===this.filters.order&&"desc"===this.filters.sort?"arrow-down":"arrow-up"}},Object(d.b)(["defaultGoodsImage","siteInfo"])),methods:{categorySearch:function(){var e=this;Object(f.b)({category_id:this.filters.category_id}).then((function(t){if(0==t.code&&t.data){var data=t.data;switch(e.catewords=data.category_full_name,e.first_index=data.category_id_1,e.categoryList=data.child_list,e.selectCategoryId=e.filters.category_id,data.level){case 1:e.categoryAll.level=1,e.categoryAll.id=data.category_id_1,e.categoryAll.isAllow=!0;break;case 2:e.categoryAll.level=1,e.categoryAll.id=data.category_id_1,e.categoryAll.isAllow=!0,3==e.categoryList[0].level&&(e.categoryAll.level=2,e.categoryAll.id=data.category_id_2);break;case 3:e.categoryAll.level=2,e.categoryAll.id=data.category_id_2,e.categoryAll.isAllow=!1}for(var i=0;i<e.categoryList.length;i++){if(e.categoryList[i].category_id==e.categoryAll.id){e.categoryAll.id=0,e.categoryAll.isAllow=!1;break}}window.document.title="".concat(data.category_name," - ").concat(e.siteInfo.site_name)}}))},getGoodsCategoryList:function(){var e=this;Object(f.c)({}).then((function(t){0==t.code&&t.data&&(e.categoryAll.level=1,e.categoryAll.id=0,e.categoryAll.isAllow=!0,e.categoryList=t.data)}))},getGoodsList:function(){var e=this,t=h({page:this.currentPage,page_size:this.pageSize,keyword:this.keyword},this.filters);Object(l.h)(t||{}).then((function(t){var r=t.data,n=r.count,o=(r.page_count,r.list);e.total=n,e.goodsList=o,e.loading=!1})).catch((function(t){e.loading=!1}))},handlePageSizeChange:function(e){this.pageSize=e,this.getGoodsList()},handleCurrentPageChange:function(e){this.currentPage=e,this.getGoodsList()},handlePriceRange:function(){if(Number(this.filters.min_price)>Number(this.filters.max_price)){var e=[this.filters.max_price,this.filters.min_price];this.filters.min_price=e[0],this.filters.max_price=e[1]}this.getGoodsList()},changeSort:function(e){this.filters.order===e?this.$set(this.filters,"sort","desc"===this.filters.sort?"asc":"desc"):(this.$set(this.filters,"order",e),this.$set(this.filters,"sort","desc")),this.getGoodsList()},getBrandList:function(){var e=this;Object(l.c)({page:1,page_size:0}).then((function(t){if(t.code>=0&&t.data&&(e.brandList=t.data.list,e.filters.brand_id))for(var i=0;i<e.brandList.length;i++)e.brandList[i].brand_id==e.filters.brand_id&&(e.choosedBrand=e.brandList[i])}))},handleChangeInitial:function(e){this.currentInitial=e},onChooseBrand:function(e){this.choosedBrand=e,this.filters.brand_id=e.brand_id,this.getGoodsList()},closeBrand:function(){this.choosedBrand="",this.filters.brand_id="",this.getGoodsList()},showPrice:function(data){var e=data.discount_price;return data.member_price&&parseFloat(data.member_price)<parseFloat(e)&&(e=data.member_price),e}},watch:{is_free_shipping:function(e){this.filters.is_free_shipping=e?1:"",this.getGoodsList()},$route:function(e){this.currentPage=1,e.query.keyword&&(window.document.title="".concat(e.query.keyword," - ").concat(this.siteInfo.site_name)),e.query.level&&e.query.category_id>0?(this.filters.category_level=e.query.level,this.filters.category_id=e.query.category_id,this.getGoodsList(),this.categorySearch()):(this.getGoodsCategoryList(),this.first_index=0,this.selectCategoryId=0,window.document.title="".concat(this.siteInfo.site_name)),null!=e.query.category_id&&0!=e.query.category_id||(this.catewords="",this.keyword=e.query.keyword,this.filters.category_id=e.query.category_id||"",this.filters.category_level=e.query.level||"",this.filters.brand_id=e.query.brand_id||"",this.getGoodsList())}}},m={name:"list",components:{BreadCrumbs:n.default,GoodsRecommend:o.default},mixins:[v]},y=(r(680),r(6)),component=Object(y.a)(m,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"goods-container"},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"goods-list"},[e.keyword?t("div",{staticClass:"goods-nav"},[t("router-link",{attrs:{to:"/"}},[e._v("首页")]),e._v(" "),t("span",[e._v("/")]),e._v(" "),t("span",{staticClass:"keyword"},[e._v(e._s(e.keyword))])],1):e.catewords?t("div",{staticClass:"goods-nav"},[t("router-link",{attrs:{to:"/"}},[e._v("首页")]),e._v(" "),t("span",[e._v("/")]),e._v(" "),t("router-link",{attrs:{to:{path:"/goods/list",query:e.$util.handleLink({category_id:e.first_index,level:1,brand_id:e.filters.brand_id})}}},[e._v(e._s(e.catewords.split("$_SPLIT_$")[0]))]),e._v(" "),2!=e.filters.category_level&&3!=e.filters.category_level||!e.catewords.split("$_SPLIT_$")[1]?e._e():t("span",[e._v("/")]),e._v(" "),2!=e.filters.category_level&&3!=e.filters.category_level||!e.catewords.split("$_SPLIT_$")[1]?e._e():t("span",{staticClass:"keyword"},[e._v(e._s(e.catewords.split("$_SPLIT_$")[1]))])],1):e._e(),e._v(" "),e.choosedBrand?t("div",{staticClass:"attr_filter"},[t("el-tag",{attrs:{type:"info",closable:"",effect:"plain"},on:{close:e.closeBrand}},[e.choosedBrand?t("span",{staticClass:"title"},[e._v("品牌：")]):e._e(),e._v("\n        "+e._s(e.choosedBrand.brand_name)+"\n      ")])],1):e._e(),e._v(" "),t("div",{staticClass:"goods-screen-wrap"},[e.keyword?e._e():t("div",{staticClass:"goods-screen-item classify-info"},[t("span",{staticClass:"screen-item-name"},[e._v("分类：")]),e._v(" "),t("ul",{staticClass:"screen-item-content"},[t("li",{class:{active:e.categoryAll.isAllow&&(0==e.categoryAll.id||e.categoryAll.id==e.filters.category_id)}},[t("router-link",{attrs:{to:{path:"/goods/list",query:e.$util.handleLink({category_id:e.categoryAll.id,level:e.categoryAll.level,brand_id:e.filters.brand_id})}}},[e._v("全部")])],1),e._v(" "),e._l(e.categoryList,(function(r){return t("li",{class:{active:r.category_id==e.selectCategoryId}},[t("router-link",{attrs:{to:{path:"/goods/list",query:e.$util.handleLink({category_id:r.category_id,level:r.level,brand_id:e.filters.brand_id})}}},[e._v(e._s(r.category_name))])],1)}))],2)]),e._v(" "),e.brandList.length>0?t("div",{staticClass:"brand"},[t("div",{staticClass:"table_head"},[e._v("品牌：")]),e._v(" "),t("div",{staticClass:"table_body",class:{more:e.isShowMoreBrand}},e._l(e.brandList,(function(r){return t("el-card",{directives:[{name:"show",rawName:"v-show",value:""===e.currentInitial||r.brand_initial===e.currentInitial,expression:"currentInitial === '' || item.brand_initial === currentInitial"}],key:r.id,staticClass:"brand-item",attrs:{"body-style":"padding: 0;height: 100%;",shadow:"hover"}},[t("el-image",{attrs:{src:e.$img(r.image_url||e.defaultGoodsImage),alt:r.brand_name,title:r.brand_name,fit:"contain"},on:{click:function(t){return e.onChooseBrand(r)}}})],1)})),1),e._v(" "),e.brandList.length>14?t("div",{staticClass:"more-wrap",on:{click:function(t){e.isShowMoreBrand=!e.isShowMoreBrand}}},[e._v("\n          "+e._s(e.isShowMoreBrand?"收起":"更多")+"\n        ")]):e._e()]):e._e(),e._v(" "),t("div",{staticClass:"goods-screen-item other-screen-info"},[t("span",{staticClass:"screen-item-name"},[e._v("筛选：")]),e._v(" "),t("div",{staticClass:"screen-item-content"},[t("div",{staticClass:"item",on:{click:function(t){return e.changeSort("")}}},[t("div",{staticClass:"item-name"},[e._v("综合")])]),e._v(" "),t("div",{class:["item","search-arrow",e.salesArrowDirection],on:{click:function(t){return e.changeSort("sale_num")}}},[e._v("销量")]),e._v(" "),t("div",{class:["item","search-arrow",e.priceArrowDirection],on:{click:function(t){return e.changeSort("discount_price")}}},[e._v("价格")]),e._v(" "),t("div",{staticClass:"item-other"},[t("el-checkbox",{attrs:{label:"包邮"},model:{value:e.is_free_shipping,callback:function(t){e.is_free_shipping=t},expression:"is_free_shipping"}})],1),e._v(" "),t("div",{staticClass:"input-wrap"},[t("div",{staticClass:"price_range"},[t("el-input",{attrs:{placeholder:"最低价格"},model:{value:e.filters.min_price,callback:function(t){e.$set(e.filters,"min_price",t)},expression:"filters.min_price"}}),e._v(" "),t("span",[e._v("—")]),e._v(" "),t("el-input",{attrs:{placeholder:"最高价格"},model:{value:e.filters.max_price,callback:function(t){e.$set(e.filters,"max_price",t)},expression:"filters.max_price"}})],1),e._v(" "),t("el-button",{attrs:{plain:"",size:"mini"},on:{click:e.handlePriceRange}},[e._v("确定")])],1)])])]),e._v(" "),t("div",{staticClass:"list-wrap"},[t("div",{staticClass:"list-right"},[e.goodsList.length?t("div",{staticClass:"cargo-list"},[t("div",{staticClass:"goods-info"},e._l(e.goodsList,(function(r,n){return t("div",{key:r.goods_id,staticClass:"item",on:{click:function(t){return e.$router.push({path:"/sku/"+r.sku_id})}}},[t("img",{staticClass:"img-wrap",attrs:{src:e.$img(r.goods_image,{size:"mid"})},on:{error:function(t){r.goods_image=e.defaultGoodsImage}}}),e._v(" "),t("div",{staticClass:"price-wrap"},[t("div",{staticClass:"price"},[t("span",[e._v("￥")]),e._v(" "),t("span",[e._v(e._s(e.showPrice(r)))]),e._v(" "),t("div",{staticClass:"price-icon-wrap"},[r.member_price&&r.member_price==e.showPrice(r)?t("img",{attrs:{src:e.$img("public/uniapp/index/VIP.png")}}):1==r.promotion_type?t("img",{attrs:{src:e.$img("public/uniapp/index/discount.png")}}):e._e()])]),e._v(" "),parseInt(r.market_price)?t("div",{staticClass:"market-price"},[e._v("￥"+e._s(r.market_price))]):e._e()]),e._v(" "),t("div",{staticClass:"goods-name"},[e._v(e._s(r.goods_name))]),e._v(" "),t("div",{staticClass:"other-info"},[t("span",{staticClass:"sale-num"},[e._v(e._s(r.sale_num||0)+"人付款")]),e._v(" "),t("div",{staticClass:"saling"},[1==r.is_free_shipping?t("div",{staticClass:"free-shipping"},[e._v("包邮")]):e._e(),e._v(" "),1==r.promotion_type?t("div",{staticClass:"free-shipping"},[e._v("限时折扣")]):e._e()])])])})),0),e._v(" "),t("div",{staticClass:"pager"},[t("el-pagination",{attrs:{background:"","pager-count":5,total:e.total,"prev-text":"上一页","next-text":"下一页","current-page":e.currentPage,"page-size":e.pageSize,"hide-on-single-page":""},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handlePageSizeChange,"current-change":e.handleCurrentPageChange}})],1)]):e.loading?e._e():t("div",{staticClass:"empty"},[t("img",{attrs:{src:r(545)}}),e._v(" "),t("span",[e._v("没有找到您想要的商品。换个条件试试吧")])])])])])])}),[],!1,null,"66d151de",null);t.default=component.exports}}]);