(window.webpackJsonp=window.webpackJsonp||[]).push([[60],{552:function(e,t,r){"use strict";r.d(t,"c",(function(){return o})),r.d(t,"b",(function(){return c})),r.d(t,"a",(function(){return f})),r.d(t,"d",(function(){return v}));var n=r(1);function o(e){return Object(n.a)({url:"/api/verify/verifyInfo",data:e,forceLogin:!0})}function c(e){return Object(n.a)({url:"/api/verify/verify",data:e,forceLogin:!0})}function f(e){return Object(n.a)({url:"/api/verify/getVerifyType",data:e})}function v(e){return Object(n.a)({url:"/api/verify/lists",data:e,forceLogin:!0})}},632:function(e,t,r){},725:function(e,t,r){"use strict";r(632)},799:function(e,t,r){"use strict";r.r(t);r(56),r(24),r(25),r(23),r(7),r(29),r(18),r(30);var n=r(10),o=r(552),c=r(12);function f(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}var v={name:"verification_detail",components:{},data:function(){return{verify_code:"",verifyInfo:{},isSub:!1,loading:!0}},created:function(){this.verify_code=this.$route.query.code,this.getVerifyInfo()},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?f(Object(source),!0).forEach((function(t){Object(n.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):f(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},Object(c.b)(["defaultGoodsImage"])),layout:"member",methods:{getVerifyInfo:function(){var e=this;Object(o.c)({verify_code:this.verify_code}).then((function(t){t.code>=0?e.verifyInfo=t.data:(e.$message({message:t.message,type:"warning"}),e.$router.push("/member")),e.loading=!1})).catch((function(t){e.$message.error(t.message),e.$router.push("/member"),e.loading=!1}))},verify:function(){var e=this;this.isSub||(this.isSub=!0,Object(o.b)({verify_code:this.verify_code}).then((function(t){t.code>=0?e.$message({message:t.message,type:"success",duration:2e3,onClose:function(){e.$router.push("/order/verification_list")}}):(e.$message({message:t.message,type:"warning"}),e.isSub=!1)})).catch((function(t){e.$message.error(t.message),e.isSub=!1})))},imageError:function(e){this.verifyInfo.item_array[e].img=this.defaultGoodsImage}}},d=v,l=(r(725),r(6)),component=Object(l.a)(d,(function(){var e=this,t=e._self._c;return t("el-card",{staticClass:"box-card order-list"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("el-breadcrumb",{attrs:{separator:"/"}},[t("el-breadcrumb-item",{attrs:{to:{path:"/order/verification_list"}}},[e._v("核销记录")]),e._v(" "),t("el-breadcrumb-item",[e._v("核销验证")])],1)],1),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"ns-verification"},[t("div",{staticClass:"ns-verification-order"},[t("p",{staticClass:"ns-site-name"},[e._v(e._s(e.verifyInfo.site_name))]),e._v(" "),e._l(e.verifyInfo.item_array,(function(r,n){return t("div",{key:n,staticClass:"ns-goods-list"},[t("div",{staticClass:"ns-goods-img"},[t("el-image",{attrs:{fit:"cover",src:e.$img(r.img)},on:{error:function(t){return e.imageError(n)}}})],1),e._v(" "),t("div",{staticClass:"ns-goods-info"},[t("p",[e._v(e._s(r.name))]),e._v(" "),t("p",{staticClass:"ns-goods-price ns-text-color"},[e._v("￥"+e._s(r.price))]),e._v(" "),t("p",[e._v("数量："+e._s(r.num))])])])})),e._v(" "),t("div",{staticClass:"ns-order-info"},[e._l(e.verifyInfo.remark_array,(function(r,n){return t("p",{key:n},[e._v(e._s(r.title)+"："+e._s(r.value))])})),e._v(" "),t("p",[e._v("核销类型："+e._s(e.verifyInfo.verify_type_name))]),e._v(" "),e.verifyInfo.is_verify?[t("p",[e._v("核销状态：已核销")]),e._v(" "),e.verifyInfo.verify_time?t("p",[e._v("核销人员："+e._s(e.verifyInfo.verifier_name))]):e._e(),e._v(" "),e.verifyInfo.verify_time?t("p",[e._v("核销时间："+e._s(e.$timeStampTurnTime(e.verifyInfo.verify_time)))]):e._e()]:e._e()],2),e._v(" "),t("div",{staticClass:"ns-btn"},[0==e.verifyInfo.is_verify?t("el-button",{on:{click:e.verify}},[e._v("确认使用")]):e._e()],1)],2)])])}),[],!1,null,"3cbe8353",null);t.default=component.exports}}]);