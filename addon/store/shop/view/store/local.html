<style>
	.map-view{position:relative}
	.map-block-view{display:none}
	.map-block{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;width:1100px}
	.local-map{-webkit-box-flex:0;-webkit-flex:0 0 750px;-ms-flex:0 0 750px;flex:0 0 750px;height:500px;border:1px solid #ededed;margin-right:20px;border-radius:4px;overflow-y:hidden;position:relative;}
	.overlayers{-webkit-box-flex:1;-webkit-flex:1 1 auto;-ms-flex:1 1 auto;flex:1 1 auto;border:1px solid #ededed;border-radius:4px;width:100%;height:494px;overflow-y:auto}
	.overlayers::-webkit-scrollbar{width:10px;height:1px}
	.overlayers::-webkit-scrollbar-thumb{border-radius:10px;box-shadow:inset 0 0 5px var(--base-color);background:var(--base-color)}
	.overlayers::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #ededed;border-radius:10px;background:#ededed}
	.map{height:500px;width:100%;position: relative;}
	.region-list{width:310px}
	.region-list li{padding:30px 12px;position:relative;border:1px solid transparent;border-bottom-color:#ebedf0}
	.area-content{overflow:hidden;margin:8px 0}
	.area-label{display:inline-block;width:70px;float:left}
	.area-input-inline{float:left;width:190px}
	.region-add-block{width:290px}
	.region-add-block button{display:block;padding:0;margin:10px auto;font-size:14px;width:196px;color:#323233;line-height:32px;background:#fff;outline:0;border:1px solid #dcdee0;border-radius:2px;cursor:pointer}
	.region-add-block button:hover{border-color:var(--base-color);color:var(--base-color)}
	.region-view{display:none;}
	.radius-view{display:none;}
	.administrative-view{display:none;}
	.administrative-region-view{display:none;}
	.administrative-radius-view{display:none;}
	.time-view{display:none;}
	.time-type-view{display:none;}
	.area-block-delete{position:absolute;top:-2px;right:5px;font-size:12px;cursor:pointer}
	.layui-form-item .layui-form-checkbox[lay-skin=primary] {margin-top: 0;}
	.layui-form-radio {margin-top: 0;}
	.day-wrap {display: flex;align-items: center}
	.day-input {width: 80px}
	.day-input[readonly] {background: #f5f5f5}
	.day-wrap .layui-form-radio:nth-child(4) {margin-right: 0px}
	.empty-address-text{font-size: 14px;color: #f43530;height: 34px;line-height: 34px;}
</style>
<link rel="stylesheet" href="SHOP_CSS/formSelects-v4.css" />

<div class="layui-form">
	<div class="layui-card card-common card-brief head">
		<div class="layui-card-header">
			<span class="card-title">同城配送基础设置</span>
		</div>
		<div class="layui-card-body">
			<div class="layui-form-item">
				<label class="layui-form-label">配送方式：</label>
				<div class="layui-input-inline">
					<input type="checkbox" name="type" value="default" title="商家自配送" lay-skin="primary" checked>
					<input type="checkbox" name="type" value="other" title="第三方配送" lay-skin="primary" disabled >
				</div>
			</div>

			<!--配送时间设置-->
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">配送时间设置：</label>
					<div class="layui-input-block">
						<input type="radio" name="time_is_open" value="1" title="开启"  lay-filter="time_is_open" {if $local_info.time_is_open == 1}checked{/if}/>
						<input type="radio" name="time_is_open" value="0" title="关闭"  lay-filter="time_is_open" {if $local_info.time_is_open == 0 || !isset($local_info.time_is_open)}checked{/if}/>
					</div>
					<div class="word-aux">开启后，买家下单选择同城配送时，可选择配送时间，提交订单后，将在买家备注中显示。</div>
				</div>
			</div>
			<div class="layui-form-item time-view" {if $local_info.time_is_open == 0} style="display:none;"{/if}>
				<div class="layui-inline">
					<label class="layui-form-label"></label>
					<div class="layui-input-inline">
						<input type="radio" name="time_type" value="0" title="每天"  lay-filter="time_type" {if $local_info.time_type == 0 || !isset($local_info.time_type)}checked{/if}/>
						<input type="radio" name="time_type" value="1" title="自定义"  lay-filter="time_type" {if $local_info.time_type == 1 }checked{/if}/>
					</div>
				</div>
			</div>
			<div class="time-view ">
				<div class="layui-form-item time-type-view" lay-verify="time_week">
					<div class="layui-inline">
						<label class="layui-form-label"></label>
						<div class="layui-input-inline">
							<input type="checkbox" value="1" class='time-week' name="time_week[]" title="周一" lay-skin="primary" {if !empty($local_info.time_week) && in_array(1,$local_info.time_week)} checked {/if}>
							<input type="checkbox" value="2" class='time-week' name="time_week[]" title="周二" lay-skin="primary" {if !empty($local_info.time_week) && in_array(2,$local_info.time_week)} checked {/if}>
							<input type="checkbox" value="3" class='time-week' name="time_week[]" title="周三" lay-skin="primary" {if !empty($local_info.time_week) && in_array(3,$local_info.time_week)} checked {/if}>
							<input type="checkbox" value="4" class='time-week' name="time_week[]" title="周四" lay-skin="primary" {if !empty($local_info.time_week) && in_array(4,$local_info.time_week)} checked {/if}>
							<input type="checkbox" value="5" class='time-week' name="time_week[]" title="周五" lay-skin="primary" {if !empty($local_info.time_week) && in_array(5,$local_info.time_week)} checked {/if}>
							<input type="checkbox" value="6" class='time-week' name="time_week[]" title="周六" lay-skin="primary" {if !empty($local_info.time_week) && in_array(6,$local_info.time_week)} checked {/if}>
							<input type="checkbox" value="0" class='time-week' name="time_week[]" title="周日" lay-skin="primary" {if !empty($local_info.time_week) && in_array(0,$local_info.time_week)} checked {/if}>
						</div>
					</div>
				</div>
				<div class="time-type-view-all">
					<div class="delivery-time">
						{foreach name="$local_info.delivery_time" key="k" item="item"}
						<div class="layui-form-item" >
							<label class="layui-form-label">{$k == 0 ? '配送时段设置：' : ''}</label>
							<div class="layui-input-inline">
								<input type="text" class="layui-input" id="startTime{$k == 0 ? '' : $k}" lay-verify="start_time" placeholder="配送开始时间" value="" readonly >
								<input type="hidden" class="layui-input" name="start_time" placeholder="配送开始时间" value="{$item.start_time}">
							</div>
							<div class="layui-form-mid layui-word-aux">~</div>
							<div class="layui-input-inline">
								<input type="text" class="layui-input" id="endTime{$k == 0 ? '' : $k}" lay-verify="end_time" placeholder="配送结束时间" value="" readonly >
								<input type="hidden" class="layui-input" name="end_time" placeholder="配送结束时间" value="{$item.end_time}">
							</div>
							<div class="layui-form-mid layui-word-aux">
								{if $k eq 0}
								<a href="javascript:;" class="text-color add">添加</a>
								{else/}
								<a href="javascript:;" class="text-color delete">删除</a>
								{/if}
							</div>
						</div>
						{/foreach}
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">细分时段：</label>
						<div class="layui-input-block">
							<input type="radio" name="time_interval" value="30" title="30分钟" {if $local_info.time_interval == 30}checked{/if}/>
							<input type="radio" name="time_interval" value="60" title="一小时" {if $local_info.time_interval == 60}checked{/if}/>
							<input type="radio" name="time_interval" value="90" title="90分钟" {if $local_info.time_interval == 90}checked{/if}/>
							<input type="radio" name="time_interval" value="120" title="两小时" {if $local_info.time_interval == 120}checked{/if}/>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">提前预约：</label>
					<div class="layui-input-block day-wrap">
						<input type="radio" name="advance_day" value="0" title="无需提前" {if $local_info.advance_day == 0}checked{/if} lay-filter="day_select"/>
						<input type="radio" name="advance_day" value="1" title="提前" {if $local_info.advance_day != 0}checked{/if} lay-filter="day_select"/>
						<div class="layui-input-inline">
							<input type="number" name="advance_day_num" lay-verify="advance_day" class="layui-input day-input" {if $local_info.advance_day == 0}readonly{else/}value="{$local_info.advance_day}"{/if}>
						</div>
						<div class="layui-form-mid layui-word-aux">天</div>
					</div>
					<div class="word-aux">预约配送是否需提前进行预约</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">最长预约：</label>
					<div class="layui-input-block day-wrap">
						<input type="radio" name="most_day" value="0" title="仅当日" {if $local_info.most_day == 0}checked{/if} lay-filter="day_select"/>
						<input type="radio" name="most_day" value="1" title="可预约" {if $local_info.most_day != 0}checked{/if} lay-filter="day_select"/>
						<div class="layui-input-inline">
							<input type="number" name="most_day_num" lay-verify="most_day" class="layui-input day-input" {if $local_info.most_day == 0}readonly{else/}value="{$local_info.most_day}"{/if}>
						</div>
						<div class="layui-form-mid layui-word-aux">天内</div>
					</div>
					<div class="word-aux">预约配送最长可预约多少天内进行提货</div>
				</div>
			</div>
		</div>

		<div class="layui-card card-common card-brief head">
			<div class="layui-card-header">
				<span class="card-title">配送区域设置</span>
			</div>
			<div class="layui-card-body">
				<div class="layui-form-item">
					<label class="layui-form-label">门店地址：</label>
					<div class="layui-input-block">
					<div class="layui-input-inline">
						<input type="text" class="layui-input len-long" readonly  value="{$store_detail.full_address} {$store_detail.address}" lay-verify="address_info">
					</div>
					<div class="layui-form-mid layui-word-aux"><a href="{:href_url('store://shop/store/editstore', ['store_id' => $store_id])}" target='_blank' class="text-color">修改</a></div>
					</div>
					<div class="word-aux">配送区域以此地址为起点进行距离计算。</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">配送区域：</label>
						<div class="layui-input-block">
							<input type="radio" name="area_type" value="1" title="服务半径"  lay-filter="area_type" {if $local_info.area_type == 1}checked{/if}/>
							<input type="radio" name="area_type" value="2" title="自定义区域"  lay-filter="area_type" {if $local_info.area_type == 2}checked{/if}/>
							<input type="radio" name="area_type" value="3" title="行政区域"  lay-filter="area_type" {if $local_info.area_type == 3}checked{/if}/>
						</div>
						<div class="word-aux">订单中商品在优惠前的价格（不包含运费）低于起送金额时，买家无法下单</div>
					</div>
				</div>

				<div class="layui-form-item man_type">
					<div class="layui-inline">
						<label class="layui-form-label">配送优惠：</label>
						<div class="layui-input-block">
							<input type="radio" name="man_type" lay-filter="man_type" value="free" title="免邮" {if $local_info.man_type == 'free' || empty($local_info.man_type)} checked {/if}>
							<input type="radio" name="man_type" lay-filter="man_type" value="discount" title="优惠" {if $local_info.man_type == 'discount'} checked {/if}>
						</div>
		<!--				<div class="word-aux">当配送区域交叉时，以最低费用计算费用。 因考虑实际送货路况，配送费按汽车导航距离计算，非地图直线距离。</div>-->
					</div>
					<div class="layui-block">
						<label class="layui-form-label"></label>
						<div class="layui-input-block">
							<div class="layui-form-mid">满</div>
							<div class="layui-input-inline">
								<input type="number" name="man_money" lay-verify="man_money"  class="layui-input"  value="{$local_info.man_money}" onblur="$('input[name=\'man_money\']').val(Math.abs($('input[name=\'man_money\']').val()));">
							</div>
							<div class="layui-form-mid discount-txt-before">元，免邮</div>
							<div class="layui-input-inline man-discount" style="width: 100px;">
								<input type="number" name="man_discount" lay-verify="man_discount" lay-verify="man_discount"  placeholder="" autocomplete="off" class="layui-input" value="{$local_info.man_discount}" onblur="manDiscount()">
							</div>
							<div class="layui-form-mid discount-txt-after">元</div>
						</div>
					</div>
				</div>

				<div class="layui-form-item administrative-view">
					<label class="layui-form-label"><span class="required">*</span>可配送区域：</label>

					<div class="layui-input-inline  len-mid area-select">
						<select name="district_id" xm-select="select1" lay-search lay-filter="district_id" lay-verify="district_id">
							{foreach $district_list as $k => $v}
							<option value="{$v.id}" {if in_array($v.id, $local_info.area_array)}selected="selected"{/if}>{$v.name}</option>
							{/foreach}
						</select>
					</div>
				</div>
				<div class="layui-form-item radius-view">
					<div class="layui-inline">
						<label class="layui-form-label"></label>
						<div class="layui-input-block">

							<div class="layui-form-mid">距离</div>
							<div class="layui-input-inline">
								<input type="number" name="radius_start_distance" lay-verify="radius_start_distance" class="layui-input" value="{if $local_info.area_type == 1}{$local_info.start_distance}{/if}">
							</div>
							<div class="layui-form-mid">公里以内,配送费用</div>
							<div class="layui-input-inline" style="width: 100px;">
								<input type="number" name="radius_start_delivery_money" lay-verify="radius_start_delivery_money" placeholder="￥" autocomplete="off" class="layui-input" value="{if $local_info.area_type == 1}{$local_info.start_delivery_money}{/if}">
							</div>
						</div>
					</div>
				</div>
				<div class="layui-form-item radius-view">
					<div class="layui-inline">
						<label class="layui-form-label"></label>
						<div class="layui-input-block">

							<div class="layui-form-mid">距离每增加</div>
							<div class="layui-input-inline">
								<input type="number" name="radius_continued_distance" lay-verify="radius_continued_distance"  class="layui-input"  value="{if $local_info.area_type == 1}{$local_info.continued_distance}{/if}">
							</div>
							<div class="layui-form-mid">公里，运费将增加</div>
							<div class="layui-input-inline" style="width: 100px;">
								<input type="number" name="radius_continued_delivery_money" lay-verify="radius_continued_delivery_money"  placeholder="￥" autocomplete="off" class="layui-input" value="{if $local_info.area_type == 1}{$local_info.continued_delivery_money}{/if}">
							</div>
						</div>
						<div class="word-aux">当配送区域交叉时，以最低费用计算费用。 因考虑实际送货路况，配送费按汽车导航距离计算，非地图直线距离。</div>

					</div>
				</div>

				<div class="layui-inline map-block-view">
					<label class="layui-form-label">地图定位：</label>
					<div class="layui-input-block map-view">
						{if $tencent_map_key}
							{if $check_map_key.status == 0}
							<div class="map-block">
								<div class="local-map">
									<div class="map" id="container"></div>
								</div>

								<div class="overlayers">
									<ul class="region-list"></ul>
									<div class="region-add-block">
										<button onclick="addArea();">增加配送区域</button>
									</div>
								</div>
							</div>
							{else/}
							<div class="empty-address-text">{$check_map_key.message}。<a href="{:href_url('shop/config/map')}" class="text-color">重新配置</a></div>
							{/if}
						{else/}
							<div class="empty-address-text">腾讯地图尚未配置，无法定位地址。<a href="{:href_url('shop/config/map')}" class="text-color">点击配置</a></div>
						{/if}
					</div>

				</div>

			</div>

		</div>

		<div class="layui-card card-common card-brief head administrative-view">
			<div class="layui-card-header">
				<span class="card-title">配送价格</span>
			</div>
			<div class="layui-card-body">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">起送价格：</label>

						<div class="layui-input-block">
							<div class="layui-input-inline">
								<input type="number" name='xz_start_money' lay-verify="xz_start_money"  class="layui-input" value="{if $local_info.area_type == 3}{$local_info.start_money}{/if}">
							</div>
							<div class="layui-form-mid">元</div>
						</div>
						<div class="word-aux">订单中商品在优惠券的价格（不包含运费）低于配送价时，买家无法下单</div>

					</div>
				</div>
				<div class="layui-form-item administrative-region-view">
					<div class="layui-inline">
						<label class="layui-form-label">配送费：</label>
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<input type="number" name='xz_delivery_money'  lay-verify="xz_delivery_money" class="layui-input administrative-delivery-money" value="{if $local_info.area_type == 3}{$local_info.delivery_money}{/if}">
							</div>
							<div class="layui-form-mid">元</div>
						</div>

					</div>
				</div>
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">阶梯价：</label>
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<input type="checkbox" name="is_open_step" value="1" lay-skin="switch" lay-filter="is_open_step" {if isset($local_info.is_open_step) && $local_info.is_open_step == 1}checked{/if}/>
							</div>
							<div class="layui-form-mid">元</div>
						</div>

					</div>
				</div>

				<div class="layui-form-item administrative-radius-view">
					<div class="layui-inline">
						<label class="layui-form-label"></label>
						<div class="layui-input-inline">

							<div class="layui-form-mid">半径</div>
							<div class="layui-input-inline">
								<input type="number" name="xz_start_distance"  lay-verify="xz_start_distance" class="layui-input" value="{if $local_info.area_type == 3}{$local_info.start_distance}{/if}">
							</div>
							<div class="layui-form-mid">公里以内,配送费用</div>
							<div class="layui-input-inline" style="width: 100px;">
								<input type="number" name="xz_start_delivery_money" placeholder="￥"  lay-verify="xz_start_delivery_money" autocomplete="off" class="layui-input" value="{if $local_info.area_type == 3}{$local_info.start_delivery_money}{/if}">
							</div>
						</div>

					</div>
				</div>
				<div class="layui-form-item administrative-radius-view">
					<div class="layui-inline">
						<label class="layui-form-label"></label>
						<div class="layui-input-inline">

							<div class="layui-form-mid">距离每增加</div>
							<div class="layui-input-inline">
								<input type="number" name="xz_continued_distance" lay-verify="xz_continued_distance" class="layui-input" value="{if $local_info.area_type == 3}{$local_info.continued_distance}{/if}">
							</div>
							<div class="layui-form-mid">公里，运费将增加</div>
							<div class="layui-input-inline" style="width: 100px;">
								<input type="number" name="xz_continued_delivery_money" lay-verify="xz_continued_delivery_money" placeholder="￥" autocomplete="off" class="layui-input" value="{if $local_info.area_type == 3}{$local_info.continued_delivery_money}{/if}">
							</div>
						</div>

					</div>
				</div>
			</div>
		</div>

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
	</div>
</div>

<!--<script type="text/javascript" src="{:get_http_type()}://webapi.amap.com/maps?v=1.4.15&amp;key=2df5711d4e2fd9ecd1622b5a53fc6b1d&plugin=AMap.CircleEditor,AMap.PolyEditor"></script>-->
<script src="https://map.qq.com/api/gljs?libraries=tools,service&v=1.exp&key={$tencent_map_key}"></script>
<script type="text/javascript" src="SHOP_JS/qq_local.js"></script>
<script type="text/html" id="area-html">
	<li key = '{{ d.key }}'>
		<div class="area-content">
			<label class="area-label">区域名称</label>
			<div class="area-input-inline">
				<input type="text" class="layui-input area-name" lay-verify="area_name" value="{{ d.area_name || '' }}">
			</div>
		</div>
		<div class="area-content">
			<label class="area-label">起送价</label>
			<div class="area-input-inline">
				<input type="number" class="layui-input start-price"  lay-verify="start_price" value="{{ d.start_price || '' }}">
			</div>

		</div>
		<div class="area-content region-view">
			<label class="area-label">配送费</label>
			<div class="area-input-inline">
				<input type="number" class="layui-input delivery-money"  lay-verify="delivery_money" value="{{ d.delivery_money || ''  }}">
			</div>
		</div>
		<div class="area-content">
			<label class="area-label">划分方式</label>
			<div class="area-input-inline layui-input-inline">
				<input type="radio" name="rule_type{{ d.key }}" class='rule-type' value="circle" title="半径"  lay-filter="huafen_type{{ d.key }}" {{#  if(d.rule_type == 'circle' || d.rule_type == undefined){ }} checked{{#  } }} />
				<input type="radio" name="rule_type{{ d.key }}" class='rule-type' value="polygon" title="自定义"  lay-filter="huafen_type{{ d.key }}" {{#  if(d.rule_type == 'polygon'){ }} checked{{#  } }}/>
			</div>
		</div>
		<div class="area-block-delete text-color" onclick="deleteArea('{{ d.key }}');">删除</div>
	</li>
</script>
<script>
	var laytpl,form;
	var init_key = 1;
	var tencentMapKey = '{$tencent_map_key}';
	var area_array = JSON.parse('{:json_encode($local_info.local_area_array, JSON_UNESCAPED_UNICODE)}');
	if(area_array.length === 0){
		area_array = [{rule_type:'circle'}];
	}

	layui.use([ 'laydate', 'form', 'laytpl','formSelects'], function() {
		var laydate = layui.laydate;
		var formSelects = layui.formSelects;

		form = layui.form;
		laytpl = layui.laytpl;
		form.render();
		formSelects.render();

		function fetchTimeSelect(){
			$('.delivery-time .layui-form-item').each(function (index, item) {
				//时间选择器
				var startTime = $(item).find("input[name=start_time]").val(), endTime = $(item).find("input[name=end_time]").val(), initTime = parseInt({:strtotime(date('Y-m-d'))});
				laydate.render({
					elem: '#startTime' + (index ? index : '')
					,type: 'time'
					,value: startTime ? ns.time_to_date((initTime + parseInt(startTime)), 'H:i:s') : ''
					,done: function(value, date, endDate){
						var time = date.hours * 3600 + date.minutes * 60 + date.seconds;
						$(item).find("input[name=start_time]").val(time);
					}
				});
				//时间选择器
				laydate.render({
					elem: '#endTime' + (index ? index : '')
					,type: 'time'
					,value: startTime ? ns.time_to_date((initTime + parseInt(endTime)), 'H:i:s') : ''
					,done: function(value, date, endDate){
						var time = date.hours * 3600 + date.minutes * 60 + date.seconds;
						$(item).find("input[name=end_time]").val(time);
					}
				});
			})
		}
		fetchTimeSelect();

		$('body').off('click', '.delivery-time .delete').on('click', '.delivery-time .delete', function () {
			$(this).parents('.layui-form-item').remove()
		});

		$('body').off('click', '.delivery-time .add').on('click', '.delivery-time .add', function () {
			var length = $('.delivery-time .layui-form-item').length;
			if (length >= 3) { layer.msg('最多添加三个时段'); return;}
			var h = `<div class="layui-form-item" >
				<label class="layui-form-label"></label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" id="startTime`+ length +`" lay-verify="start_time" placeholder="配送开始时间" value="" readonly >
					<input type="hidden" class="layui-input" name="start_time" placeholder="配送开始时间" value="">
				</div>
				<div class="layui-form-mid layui-word-aux">~</div>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" id="endTime`+ length +`" lay-verify="end_time" placeholder="配送结束时间" value="" readonly >
					<input type="hidden" class="layui-input" name="end_time" placeholder="配送结束时间" value="">
				</div>
				<div class="layui-form-mid layui-word-aux">
					<a href="javascript:;" class="text-color delete">删除</a>
				</div>
			</div>`;
			$('.delivery-time').append(h);
			fetchTimeSelect();
		});

		//开启定时达
		form.on('radio(time_is_open)', function(data){
			timeChange(data.value);
		});

		//运费优惠设置
		form.on('radio(man_type)', function(data){
			switchManType();
		});

		//开启定时达
		form.on('radio(time_type)', function(data){
			timeTypeChange(data.value);
		});
		//区域类型
		form.on('radio(area_type)', function(data){
			areaChange(data.value);
		});

		//启用阶梯价
		form.on('switch(is_open_step)', function(data){
			stepPriceChange(data.elem.checked ? 1 : 0);
		});

		form.on('radio(day_select)', function (data){
			if (data.value == 1) {
				$(data.elem).parents('.day-wrap').find('input[type="number"]').prop('readonly', false);
			} else {
				$(data.elem).parents('.day-wrap').find('input[type="number"]').prop('readonly', true);
			}
		})

		init();
		form.render();

		form.verify({
			address_info:function (value, item){
				var longitude = '{$store_detail.longitude}';
				var latitude = '{$store_detail.latitude}';
				if(!longitude || !latitude){
					return '请先完善门店地址';
				}
			},
			start_time: function(value, item){
				if ($('[name="time_is_open"]:checked').val() == 1) {
					if(!value) return '请输入开始时间';
					var end_time = $(item).parents('.layui-form-item').find("input[name=end_time]").val();
					var start_time = $(item).parents('.layui-form-item').find("input[name=start_time]").val();
					if(parseInt(start_time) > parseInt(end_time)){
						return '配送开始时间不能大于配送结束时间';
					}
					var prev_endtime = $(item).parents('.layui-form-item').prev('.layui-form-item').find("input[name=end_time]").val();
					if (prev_endtime && parseInt(prev_endtime) > parseInt(start_time)) return '配送开始时间不能小于上一阶段配送结束时间';
				}
			},
			end_time: function(value, item){
				if ($('[name="time_is_open"]:checked').val() == 1) {
					if(!value) return '请输入结束时间';
					var end_time = $(item).parents('.layui-form-item').find("input[name=end_time]").val();
					var start_time = $(item).parents('.layui-form-item').find("input[name=start_time]").val();
					var time_interval = $('[name="time_interval"]:checked').val();

					if (parseInt(end_time) < parseInt(start_time)) {
						return '配送结束时间不能小于配送开始时间';
					}
					if ((parseInt(end_time) - parseInt(start_time)) / 60 < parseInt(time_interval)) {
						return '配送时间间隔不能小于' + time_interval + '分钟';
					}
				}
			},
			time_week: function(){
				if ($('[name="time_is_open"]:checked').val() == 1 && $('[name="time_type"]:checked').val() == 1 && !$('.time-week:checked').length)
					return '请选择可配送日期';
			},
			advance_day: function (value){
				if ($('[name="advance_day"]:checked').val() == 1) {
					if (value == '' || value == 0) return '请输入提前预约时间';
					if (value < 0) return '提前预约时间不能为负数';
				}
			},
			most_day: function (value){
				if ($('[name="most_day"]:checked').val() == 1) {
					if (value == '' || value == 0) return '请输入最长可预约时间';
					if (value < 0) return '最长可预约时间不能为负数';
					if (value > 15) return '最长可预约时间不能超过15天';
				}
			},
			radius_start_distance: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				if(area_type == 1 && value == ''){
					return '请填写配送区域相关信息';
				}
                if (area_type == 1 && parseFloat(value) <= 0) {
                    return '距离不能小于等于0';
                }
			},
			radius_start_delivery_money: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				if(area_type == 1 && value == ''){
					return '请填写配送区域相关信息';
				}
			},
			radius_continued_distance: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				if(area_type == 1 && value == ''){
					return '请填写配送区域相关信息';
				}
                if (area_type == 1 && parseFloat(value) <= 0) {
                    return '距离不能小于等于0';
                }
			},
			radius_continued_delivery_money: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				if(area_type == 1 && value == ''){
					return '请填写配送区域相关信息';
				}
			},

			xz_start_distance: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				var is_open_step = $("input[name=is_open_step]:checked").val();
				if(area_type == 3 && is_open_step == 1 && value == ''){
					return '请填写配送区域相关信息';
				}
			},
			xz_start_delivery_money: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				var is_open_step = $("input[name=is_open_step]:checked").val();
				if(area_type == 3 && is_open_step == 1 && value == ''){
					return '请填写配送区域相关信息';
				}

			},
			xz_continued_distance: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				var is_open_step = $("input[name=is_open_step]:checked").val();
				if(area_type == 3 && is_open_step == 1 && value == ''){
					return '请填写配送区域相关信息';
				}

			},
			xz_continued_delivery_money: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				var is_open_step = $("input[name=is_open_step]:checked").val();
				if(area_type == 3 && is_open_step == 1 && value == ''){
					return '请填写配送区域相关信息';
				}

			},

			xz_start_money: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				var is_open_step = $("input[name=is_open_step]:checked").val();
				if(area_type == 3 && value == ''){
					return '请填写配送区域相关信息';
				}

			},
			xz_delivery_money: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				var is_open_step = $("input[name=is_open_step]:checked").val();
				if(area_type == 3 && is_open_step == 0 && value == ''){
					return '请填写配送区域相关信息';
				}

			},

			start_price: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				if((area_type == 1 || area_type == 2) && value == ''){
					return '请填写配送区域相关信息';
				}

			},
			delivery_money: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();

				if(area_type == 2 && value == ''){
					return '请填写配送区域相关信息';
				}

			},
			//配送区域
			area_name: function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				if((area_type == 1 || area_type == 2) && value == ''){
					return '请填写配送区域相关信息';

				}

			},
			district_id : function(value, item){
				var area_type = $("input[name=area_type]:checked").val();
				if(area_type == 3 && value == ''){
					return '请填写配送区域相关信息';
				}

			},
			man_discount: function(value, item){
				var man_type = $("[name=man_type]:checked").val();
				if(man_type == 'discount'){
					if(parseFloat(value) < 0){
						return '运费折扣不能小于0';
					}
				}

			},
			man_money: function(value, item){
				if(parseFloat(value) < 0){
					return '金额不能小于0';
				}
			}

		});

		var repeat_flag = false;
		//提交
		form.on('submit(save)', function(data) {

			if (repeat_flag) return false;
			repeat_flag = true;
			var transfer_type =$("input[name=type]:checked()");
			if(transfer_type.length <= 0){
				repeat_flag = false;
				layer.msg("请至少选择一种配送方式！");
				return
			}
			var field = data.field;
			var json = {};
			json.store_id = "{$store_id}";
			json.type = field.type;
			json.man_money = field.man_money;
			json.man_type = field.man_type;
			json.man_discount = field.man_discount;
			json.area_type = field.area_type;
			//是否开启定时达
			json.time_is_open = field.time_is_open;
			json.time_interval = $('[name="time_interval"]:checked').val();
			if(field.time_is_open){
				//时间类型
				json.time_type =  field.time_type;
				//自定义时间
				if(field.time_type == 1){
					var time_week = [];
					$('.time-week').each(function(){
						if($(this).prop('checked')){
							time_week.push($(this).val());
						}
					});
					json.time_week =  time_week.toString();

				}
			}

			json.delivery_time = [];
			$('.delivery-time .layui-form-item').each(function (index, item) {
				json.delivery_time.push({
					start_time: $(item).find("input[name=start_time]").val(),
					end_time: $(item).find("input[name=end_time]").val()
				})
			});

			json.start_time =  json.delivery_time[0].start_time;
			json.end_time =  json.delivery_time[0].end_time;

			json.delivery_time = JSON.stringify(json.delivery_time);

			json.advance_day = $('[name="advance_day"]:checked').val() == 0 ? 0 : $('[name="advance_day_num"]').val();
			json.most_day = $('[name="most_day"]:checked').val() == 0 ? 0 : $('[name="most_day_num"]').val();

			switch(field.area_type){
				case '1':
					json.start_distance = field.radius_start_distance;
					json.start_delivery_money = field.radius_start_delivery_money;
					json.continued_distance = field.radius_continued_distance;
					json.continued_delivery_money = field.radius_continued_delivery_money;
					var local_area_json = eachOverlayers(field.area_type);
					json.local_area_json = JSON.stringify(local_area_json);
					break;
				case '2':
					var local_area_json = eachOverlayers(field.area_type);
					json.local_area_json = JSON.stringify(local_area_json);
					break;
				case '3':
					//地址id
					var area_array = formSelects.value('select1', 'val').toString();
					json.area_array = area_array;
					json.start_money = field.xz_start_money;
					json.is_open_step = field.is_open_step;
					if(field.is_open_step){
						json.start_distance = field.xz_start_distance;
						json.start_delivery_money = field.xz_start_delivery_money;
						json.continued_distance = field.xz_continued_distance;
						json.continued_delivery_money = field.xz_continued_delivery_money;
					}else{
						json.delivery_money = field.xz_delivery_money;
					}
					break;
			}
			if(json.local_area_json == '[]'){
				repeat_flag = false;
				layer.msg('至少保留一个配送区域');
				return;
			}
			$.ajax({
				type: 'POST',
				dataType: 'JSON',
				url: ns.url("store://shop/store/local"),
				data: json,
				success: function (res) {
					layer.msg(res.message);
					repeat_flag = false;
				}
			});
		});

	});

	//删除区域
	function deleteArea(key){
		var parent = $('li[key='+key+']');
		parent.remove();
		removeOverlayers(key);
	}

	//遍历获取区域配置
	function eachOverlayers(area_type){
		var area_json = [];
		$('.region-list li').each(function(){
			var start_price = $(this).find('.start-price').val();
			var area_name = $(this).find('.area-name').val();
			var rule_type = $(this).find('.rule-type:checked').val();

			var key = $(this).attr('key');
			var path = getOverlayersPath(key, rule_type);
			var item_json = {
				start_price:start_price,
				area_name:area_name,
				rule_type:rule_type,
				path:path,
			};
			if(area_type == 2){
				var delivery_money = $(this).find('.delivery-money').val();
				item_json.delivery_money = delivery_money;
			}

			area_json.push(item_json);
		});
		return area_json;
	}

	$("body").off('click', '.region-list li').on('click', '.region-list li', function(){
		$('.region-list li').removeClass('border-color');

		$(this).addClass('border-color');
		// var rule_type = $(this).find('.rule_type:checked');
		var key = $(this).attr('key');
		//创建覆盖物
		foursOverlayers(key);
	});

	//初始化
	function init(){
		//是否启用定时达
		timeChange($("input[name=time_is_open]:checked").val());
		//是否启用定时达
		timeTypeChange($("input[name=time_type]:checked").val());

		setTimeout(function () {
			//初始化区域类型
			areaChange($("input[name=area_type]:checked").val());
			//初始化阶梯价
			stepPriceChange($("input[name=is_open_step]").prop('checked') ? 1 : 0);

			if ($("input[name=area_type]:checked").val() == 1) {
				initMap();
			}
		},200);
		form.render();
	}

	function initMap() {
		if(tencentMapKey == '') return;

		if ($('#container canvas').length) return;

		if ('{$store_detail.latitude}' == "" || '{$store_detail.longitude}' == "") {
			var latlng = {lat: '', lng: ''};
		} else {
			var latlng = {lat: '{$store_detail.latitude}', lng: '{$store_detail.longitude}'};
		}
		createMap('container', latlng);

		$.each(area_array, function (i, item) {
			addArea(item);
		});
	}

	function timeChange(is_open){
		$('.time-view').hide();
		if(is_open == 0){

		}else if(is_open == 1){
			$('.time-view').show();
		}
	}

	function manDiscount(){
		$('input[name="man_discount"]').val(Math.abs($('input[name="man_discount"]').val()));
	}

	switchManType(1);
	function switchManType(init = 0){
		let data = $('[name="man_type"]:checked').val();
		if(data == 'free'){
			$('.man_type .discount-txt-before').text('元，免邮费');
			$('.man_type .discount-txt-after, .man_type .man-discount').addClass('layui-hide');

		}else if(data == 'discount'){
			$('.man_type .discount-txt-after, .man_type .man-discount').removeClass('layui-hide');
			$('.man_type .discount-txt-before').text('元，运费优惠');
		}
		manDiscount();
	}

	function timeTypeChange(type){
		$('.time-type-view').hide();
		if(type == 0){

		}else if(type == 1){
			$('.time-type-view').show();
		}
	}

	function areaChange(area_type){
		$(".region-view").hide();
		$(".radius-view").hide();
		$(".administrative-view").hide();
		$(".map-block-view").hide();
		if(area_type== 1){
			$(".radius-view").show();
			$(".map-block-view").show();
			initMap();
		}else if(area_type == 2){
			$(".region-view").show();
			$(".map-block-view").show();
			initMap();
		}else if(area_type == 3){
			$(".administrative-view").show();
		}
	}

	function stepPriceChange(is_open){
		$(".administrative-region-view").hide();
		$(".administrative-radius-view").hide();
		if(is_open == 0){

			$(".administrative-region-view").show();
		}else if(is_open == 1){
			$(".administrative-radius-view").show();
		}
	}

	/**
	 * 创建配送区域
	 */
	function addArea(data){
		var temp_data = data != undefined ? data : [];
		var rule_type = temp_data.length == 0 ? 'circle' : data.rule_type;
		var key = init_key;
		temp_data['key'] = key;
		temp_data['rule_type'] = rule_type;
		var tpl = $("#area-html").html();
		laytpl(tpl).render(temp_data, function(html){
			$('.region-list li').removeClass('border-color');
			$('.region-list').append(html);
			$('.region-list li').last().addClass('border-color');
			form.render();

			form.on('radio(huafen_type'+key +')', function(data){
				var parent_obj = $(data.elem).parent().parent().parent();
				var rule_type = data.value;
				var key = parent_obj.attr('key');
				removeOverlayers(key);
				//创建覆盖物
				createOverlayers(rule_type, key);
			});
		});

		var path = data != undefined ? data.path : undefined;

		//创建覆盖物
		createOverlayers(rule_type, key, path);

		areaChange($("input[name=area_type]:checked").val());
		init_key++;

	}

/**
 * 创建覆盖物
 * @param type
 * @param key
 * @param path
 */
function createOverlayers(type, key, path){
	switch(type){
		//多边形
		case 'polygon':{
			createPolygon(key, getRandomColor(), getRandomColor(), path);
			break;
		}
		//圆
		case 'circle':{
			createCircle(key, getRandomColor(), getRandomColor(), path);
			break;
		}
	}
}

/**
 * 随机生成颜色
 * @returns {string}
 */
function getRandomColor(){
	return  '#' + (function(color){
		return (color +=  '0123456789abcdef'[Math.floor(Math.random()*16)])
		&& (color.length == 6) ?  color : arguments.callee(color);
	})('');
}

/**
 * 一天内的时间比较
 * @param start_time
 * @param end_time
 * @returns {boolean}
 * @constructor
 */
function compareDate(start_time, end_time) {
	var date = new Date();
	var start_time_arr = start_time.split(":");
	var end_time_arr = end_time.split(":");
	return date.setHours(end_time_arr[0], end_time_arr[1], end_time_arr[2]) > date.setHours(start_time_arr[0], start_time_arr[1], start_time_arr[2]);
}
</script>
