<style>
	.day-wrap {display: flex;align-items: center}
	.day-input {width: 80px}
	.day-input[readonly] {background: #f5f5f5}
	.day-wrap .layui-form-radio:nth-child(4) {margin-right: 0px}

	/* 溢价设置样式 */
	.markup-value-item .layui-input-inline.len-short {width: 120px;}
	.markup-unit {display: inline-block; margin-left: 10px; line-height: 38px;}
	.markup-unit-text {font-weight: bold; color: #666;}
	.markup-desc {margin-top: 5px;}
	.category-select-item .layui-btn {margin-left: 10px;}
	.category-select-item input[readonly] {background-color: #f8f8f8;}
</style>

<div class="layui-form form-wrap" lay-filter="storeform" >
    <input type="hidden" name="store_id" value="{$info.store_id}"/>
	<div class="layui-card card-common card-brief head">
		<div class="layui-card-header">
			<span class="card-title">配送设置</span>
		</div>
		<div class="layui-card-body">
			<div class="layui-form-item">
				<label class="layui-form-label">物流配送：</label>
				<div class="layui-input-block">
					<input type="checkbox" name="is_express" value="1" lay-skin="switch" {if !empty($info) && $info.is_express==1 }checked{/if}>
				</div>
				<div class="word-aux">物流配送只有在连锁门店模式有效，在平台运营模式，按照总店查询</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">同城配送：</label>
				<div class="layui-input-block">
					<input type="checkbox" name="is_o2o" value="1" lay-skin="switch" {if $info['is_o2o'] == 1} checked {/if} lay-verify="is_o2o">
				</div>
				<div class="word-aux ">开启同城配送需要门店设置配送费用以及配送员</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">门店自提：</label>
				<div class="layui-input-block">
					<input type="checkbox" name="is_pickup" value="1" lay-skin="switch" {if $info['is_pickup'] == 1} checked {/if} lay-verify="is_pickup" lay-filter="pickup">
				</div>
			</div>

			<div class="layui-form-item time-view {if $info['is_pickup'] == 0}layui-hide{/if}">
				<div class="layui-inline">
					<label class="layui-form-label">自提日期：</label>
					<div class="layui-input-inline">
						<input type="radio" name="time_type" value="0" title="每天"  lay-filter="time_type" {if $info.time_type == 0 || !isset($info.time_type)}checked{/if}/>
						<input type="radio" name="time_type" value="1" title="自定义"  lay-filter="time_type" {if $info.time_type == 1 }checked{/if}/>
					</div>
				</div>
			</div>

			<div class="time-view {if $info['is_pickup'] == 0}layui-hide{/if}">
				<div class="layui-form-item time-type-view" lay-verify="time_week">
					<div class="layui-inline">
						<label class="layui-form-label"></label>
						<div class="layui-input-inline">
							<input type="checkbox" value="1" class='time-week' name="time_week[]" title="周一" lay-skin="primary" {if !empty($info.time_week) && in_array(1,$info.time_week)} checked {/if}>
							<input type="checkbox" value="2" class='time-week' name="time_week[]" title="周二" lay-skin="primary" {if !empty($info.time_week) && in_array(2,$info.time_week)} checked {/if}>
							<input type="checkbox" value="3" class='time-week' name="time_week[]" title="周三" lay-skin="primary" {if !empty($info.time_week) && in_array(3,$info.time_week)} checked {/if}>
							<input type="checkbox" value="4" class='time-week' name="time_week[]" title="周四" lay-skin="primary" {if !empty($info.time_week) && in_array(4,$info.time_week)} checked {/if}>
							<input type="checkbox" value="5" class='time-week' name="time_week[]" title="周五" lay-skin="primary" {if !empty($info.time_week) && in_array(5,$info.time_week)} checked {/if}>
							<input type="checkbox" value="6" class='time-week' name="time_week[]" title="周六" lay-skin="primary" {if !empty($info.time_week) && in_array(6,$info.time_week)} checked {/if}>
							<input type="checkbox" value="0" class='time-week' name="time_week[]" title="周日" lay-skin="primary" {if !empty($info.time_week) && in_array(0,$info.time_week)} checked {/if}>
						</div>
					</div>
				</div>
				<div class="time-type-view-all">
					<div class="delivery-time">
						{foreach name="$info.delivery_time" key="k" item="item"}
						<div class="layui-form-item" >
							<label class="layui-form-label">{$k == 0 ? '自提时段设置：' : ''}</label>
							<div class="layui-input-inline">
								<input type="text" class="layui-input" id="startTime{$k == 0 ? '' : $k}" lay-verify="start_time" placeholder="配送开始时间" value="" readonly >
								<input type="hidden" class="layui-input" name="start_time" placeholder="配送开始时间" value="{$item.start_time}">
							</div>
							<div class="layui-form-mid layui-word-aux">~</div>
							<div class="layui-input-inline">
								<input type="text" class="layui-input" id="endTime{$k == 0 ? '' : $k}" lay-verify="end_time" placeholder="配送结束时间" value="" readonly >
								<input type="hidden" class="layui-input" name="end_time" placeholder="配送结束时间" value="{$item.end_time}">
							</div>
							<div class="layui-form-mid layui-word-aux">
								{if $k eq 0}
								<a href="javascript:;" class="text-color add">添加</a>
								{else/}
								<a href="javascript:;" class="text-color delete">删除</a>
								{/if}
							</div>
						</div>
						{/foreach}
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">细分时段：</label>
						<div class="layui-input-block">
							<input type="radio" name="time_interval" value="30" title="30分钟" {if $info.time_interval == 30}checked{/if}/>
							<input type="radio" name="time_interval" value="60" title="一小时" {if $info.time_interval == 60}checked{/if}/>
							<input type="radio" name="time_interval" value="90" title="90分钟" {if $info.time_interval == 90}checked{/if}/>
							<input type="radio" name="time_interval" value="120" title="两小时" {if $info.time_interval == 120}checked{/if}/>
						</div>
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">提前预约：</label>
					<div class="layui-input-block day-wrap">
						<input type="radio" name="advance_day" value="0" title="无需提前" {if $info.advance_day == 0}checked{/if} lay-filter="day_select"/>
						<input type="radio" name="advance_day" value="1" title="提前" {if $info.advance_day != 0}checked{/if} lay-filter="day_select"/>
						<div class="layui-input-inline">
							<input type="number" name="advance_day_num" lay-verify="advance_day" class="layui-input day-input" {if $info.advance_day == 0}readonly{else/}value="{$info.advance_day}"{/if}>
						</div>
						<div class="layui-form-mid layui-word-aux">天</div>
					</div>
					<div class="word-aux">预约提货是否需提前进行预约</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">最长预约：</label>
					<div class="layui-input-block day-wrap">
						<input type="radio" name="most_day" value="0" title="仅当日" {if $info.most_day == 0}checked{/if} lay-filter="day_select"/>
						<input type="radio" name="most_day" value="1" title="可预约" {if $info.most_day != 0}checked{/if} lay-filter="day_select"/>
						<div class="layui-input-inline">
							<input type="number" name="most_day_num" lay-verify="most_day" class="layui-input day-input" {if $info.most_day == 0}readonly{else/}value="{$info.most_day}"{/if}>
						</div>
						<div class="layui-form-mid layui-word-aux">天内</div>
					</div>
					<div class="word-aux">预约提货最长可预约多少天内进行提货</div>
				</div>
			</div>
		</div>
		<div class="layui-card-header">
			<span class="card-title">库存设置</span>
		</div>
		<div class="layui-card-body">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">库存设置：</label>
					<div class="layui-input-inline">
						<input type="radio" {if $info.is_default}disabled{/if} name="stock_type" value="all" title="总部统一库存" {if $info['stock_type'] == 'all'} checked {/if}>
						<input type="radio" {if $info.is_default}disabled{/if} name="stock_type" value="store" title="门店独立库存"  {if $info['stock_type'] == 'store'} checked {/if}>
					</div>
				</div>
				<div class="word-aux">总部统一库存：门店不用入库，商品查询以及销售由总部进行出入库<br>门店独立库存：门店自提以及收银扣除门店库存，门店需要入库</div>
			</div>
		</div>
		<div class="layui-card-header">
			<span class="card-title">营业设置</span>
		</div>
		<div class="layui-card-body">
			<div class="layui-form-item" {if $info.is_default == 1}style="display:none;"{/if}>
				<label class="layui-form-label">营业状态：</label>
				<div class="layui-input-block">
					{if $info.is_default == 1}
					<input type="checkbox" name="status" value="1" lay-skin="switch" checked lay-filter="status" title="营业|休息">
					{else/}
					<input type="checkbox" name="status" value="1" lay-skin="switch" lay-filter="status" {if !empty($info) && $info.status==1 }checked{/if} title="营业|休息">
					{/if}
				</div>
				<div class="word-aux">门店休息并且设置前台不展示则前台无法看到该门店</div>
			</div>

			<div class="close-block {if $info.status == 1}layui-hide{/if}">
				<div class="layui-form-item">
					<label class="layui-form-label">休息展示：</label>
					<div class="layui-input-block">
						<input type="radio" name="close_show" value="0" title="前台不展示"  lay-filter="close_show" {if $info.close_show == 0}checked{/if}/>
						<input type="radio" name="close_show" value="1" title="前台展示"  lay-filter="close_show" {if $info.close_show == 1}checked{/if}/>
					</div>
					<div class="word-aux">门店休息并且设置前台不展示则前台无法看到该门店</div>
				</div>

				<div class="layui-form-item {if $info.close_show == 0}layui-hide{/if} close-desc-block" >
					<label class="layui-form-label"><span class="required">*</span>休息说明：</label>
					<div class="layui-input-block">
						<input type="text" name="close_desc" autocomplete="off" lay-verify="close_desc" class="layui-input len-long" value="{$info.close_desc}" placeholder="请输入临时停业说明，最多输入50个字">
					</div>
					<div class="word-aux">门店休息且前台展示时会显示该说明，最多输入50个字</div>
				</div>
			</div>
			<div class="time-type-view-all">
				<div class="open-date-delivery-time">
					{foreach name="$info.open_date_config" key="k" item="item"}
					<div class="layui-form-item" >
						<label class="layui-form-label">{$k == 0 ? '营业时间设置：' : ''}</label>
						<div class="layui-input-inline">
							<input type="text" class="layui-input" id="openDateStartTime{$k == 0 ? '' : $k}" lay-verify="open_date_start_time" placeholder="营业开始时间" value="" readonly >
							<input type="hidden" name="open_date_start_time" placeholder="配送开始时间" value="{$item.start_time}">
						</div>
						<div class="layui-form-mid layui-word-aux">~</div>
						<div class="layui-input-inline">
							<input type="text" class="layui-input" id="openDateEndTime{$k == 0 ? '' : $k}" lay-verify="open_date_end_time" placeholder="营业结束时间" value="" readonly >
							<input type="hidden" name="open_date_end_time" placeholder="配送结束时间" value="{$item.end_time}">
						</div>
						<div class="layui-form-mid layui-word-aux">
							{if $k eq 0}
							<a href="javascript:;" class="text-color add">添加</a>
							{else/}
							<a href="javascript:;" class="text-color delete">删除</a>
							{/if}
						</div>
					</div>
					{/foreach}
				</div>
				<div class="word-aux">如果不设置，则默认全天营业</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">非营业时间配送下单：</label>
				<div class="layui-input-block">
					<input type="radio" name="out_open_date_o2o_pay" value="1" title="允许"  lay-filter="time_type" {if $info.out_open_date_o2o_pay == 1}checked{/if}/>
					<input type="radio" name="out_open_date_o2o_pay" value="0" title="不允许"  lay-filter="time_type" {if $info.out_open_date_o2o_pay == 0 }checked{/if}/>
				</div>
				<div class="word-aux">如果设置为不允许，则在非营业时间用户不可以选择同城配送下单</div>
			</div>
		</div>

		<div class="layui-card-header">
			<span class="card-title">价格设置</span>
		</div>
		<div class="layui-card-body">
			<div class="layui-form-item">
				<label class="layui-form-label">商品溢价：</label>
				<div class="layui-input-block">
					<input type="radio" name="markup_type" value="none" title="不设置溢价" lay-filter="markup_type" {if !isset($info.markup_type) || $info.markup_type == 'none'}checked{/if}/>
					<input type="radio" name="markup_type" value="fixed" title="固定金额溢价" lay-filter="markup_type" {if isset($info.markup_type) && $info.markup_type == 'fixed'}checked{/if}/>
					<input type="radio" name="markup_type" value="percent" title="百分比溢价" lay-filter="markup_type" {if isset($info.markup_type) && $info.markup_type == 'percent'}checked{/if}/>
				</div>
				<div class="word-aux">设置该门店商品的溢价方式</div>
			</div>

			<div class="layui-form-item markup-value-item {if !isset($info.markup_type) || $info.markup_type == 'none'}layui-hide{/if}">
				<label class="layui-form-label">溢价数值：</label>
				<div class="layui-input-block">
					<div class="layui-input-inline len-short">
						<input type="number" name="markup_value" value="{$info.markup_value|default=0}" class="layui-input" step="0.01" min="0" placeholder="请输入溢价数值" />
					</div>
					<div class="layui-input-inline markup-unit">
						<span class="markup-unit-text">{if isset($info.markup_type) && $info.markup_type == 'percent'}%{else/}元{/if}</span>
					</div>
				</div>
				<div class="word-aux markup-desc">
					<span class="fixed-desc {if isset($info.markup_type) && $info.markup_type != 'fixed'}layui-hide{/if}">固定金额：在商品原价基础上增加指定金额</span>
					<span class="percent-desc {if !isset($info.markup_type) || $info.markup_type != 'percent'}layui-hide{/if}">百分比：在商品原价基础上增加指定百分比</span>
				</div>
			</div>

			<div class="layui-form-item markup-value-item {if !isset($info.markup_type) || $info.markup_type == 'none'}layui-hide{/if}">
				<label class="layui-form-label">应用范围：</label>
				<div class="layui-input-block">
					<input type="radio" name="markup_apply_to" value="all" title="全部商品" {if !isset($info.markup_apply_to) || $info.markup_apply_to == 'all'}checked{/if}/>
					<input type="radio" name="markup_apply_to" value="category" title="指定分类" {if isset($info.markup_apply_to) && $info.markup_apply_to == 'category'}checked{/if}/>
				</div>
				<div class="word-aux">选择溢价应用的商品范围</div>
			</div>

			<div class="layui-form-item category-select-item {if !isset($info.markup_apply_to) || $info.markup_apply_to != 'category' || !isset($info.markup_type) || $info.markup_type == 'none'}layui-hide{/if}">
				<label class="layui-form-label">选择分类：</label>
				<div class="layui-input-block">
					<input type="text" name="markup_category_names" value="{$info.markup_category_names|default=''}" class="layui-input len-long" placeholder="请选择商品分类" readonly />
					<input type="hidden" name="markup_category_ids" value="{$info.markup_category_ids|default=''}" />
					<button type="button" class="layui-btn layui-btn-normal" id="selectCategory">选择分类</button>
				</div>
				<div class="word-aux">选择需要应用溢价的商品分类</div>
			</div>
		</div>
	</div>
	<div class="layui-card-body">
		<div class="form-row">
			<button class="layui-btn" lay-submit lay-filter="save">保存</button>
			<button class="layui-btn layui-btn-primary" onclick="backStockList()">返回</button>
			<a id="storeImage"></a>
		</div>
	</div>
</div>

<script>
	var form, repeat_flag, map_class;
	var saveData = null;
	var totalUploadNum = 0;
	var completeUploadNum = 0;
	var upload;
	var store_business = "{$business_config['store_business']}";

	layui.use(['form','laydate'], function() {
		var laydate = layui.laydate;
		form = layui.form;
		repeat_flag = false;//防重复标识

		form.render();

		/************************************* 自提时间选择 开始 **********************************/
		function fetchTimeSelect(){
			$('.delivery-time .layui-form-item').each(function (index, item) {
				//时间选择器
				var startTime = $(item).find("input[name=start_time]").val(), endTime = $(item).find("input[name=end_time]").val(), initTime = parseInt({:strtotime(date('Y-m-d'))});
				laydate.render({
					elem: '#startTime' + (index ? index : '')
					,type: 'time'
					,done: function(value, date, endDate){
						var time = date.hours * 3600 + date.minutes * 60 + date.seconds;
						$(item).find("input[name=start_time]").val(time || 0);
					}
				});
				$('#startTime' + (index ? index : '')).val(startTime ? ns.time_to_date((initTime + parseInt(startTime)), 'H:i:s') : '');
				//时间选择器
				laydate.render({
					elem: '#endTime' + (index ? index : '')
					,type: 'time'
					,done: function(value, date, endDate){
						var time = date.hours * 3600 + date.minutes * 60 + date.seconds;
						$(item).find("input[name=end_time]").val(time || 0);
					}
				});
				$('#endTime' + (index ? index : '')).val(endTime ? ns.time_to_date((initTime + parseInt(endTime)), 'H:i:s') : '');
			})
		}
		fetchTimeSelect();

		$('body').off('click', '.delivery-time .delete').on('click', '.delivery-time .delete', function () {
			$(this).parents('.layui-form-item').remove()
		});

		$('body').off('click', '.delivery-time .add').on('click', '.delivery-time .add', function () {
			var length = $('.delivery-time .layui-form-item').length;
			if (length >= 3) { layer.msg('最多添加三个时段'); return;}
			var h = `<div class="layui-form-item" >
				<label class="layui-form-label"></label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" id="startTime`+ length +`" lay-verify="start_time" placeholder="配送开始时间" value="" readonly >
					<input type="hidden" class="layui-input" name="start_time" placeholder="配送开始时间" value="">
				</div>
				<div class="layui-form-mid layui-word-aux">~</div>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" id="endTime`+ length +`" lay-verify="end_time" placeholder="配送结束时间" value="" readonly >
					<input type="hidden" class="layui-input" name="end_time" placeholder="配送结束时间" value="">
				</div>
				<div class="layui-form-mid layui-word-aux">
					<a href="javascript:;" class="text-color delete">删除</a>
				</div>
			</div>`;
			$('.delivery-time').append(h);
			fetchTimeSelect();
		});
		/************************************* 自提时间选择 结束 **********************************/

		/************************************* 营业时间选择 开始 **********************************/
		function fetchOpenDateTimeSelect(){
			$('.open-date-delivery-time .layui-form-item').each(function (index, item) {
				//时间选择器
				var startTime = $(item).find("input[name=open_date_start_time]").val(), endTime = $(item).find("input[name=open_date_end_time]").val(), initTime = parseInt({:strtotime(date('Y-m-d'))});
				laydate.render({
					elem: '#openDateStartTime' + (index ? index : '')
					,type: 'time'
					,done: function(value, date, endDate){
						var time = date.hours * 3600 + date.minutes * 60 + date.seconds;
						$(item).find("input[name=open_date_start_time]").val(time || 0);
					}
				});
				$('#openDateStartTime' + (index ? index : '')).val(startTime ? ns.time_to_date((initTime + parseInt(startTime)), 'H:i:s') : '');
				//时间选择器
				laydate.render({
					elem: '#openDateEndTime' + (index ? index : '')
					,type: 'time'
					,done: function(value, date, endDate){
						var time = date.hours * 3600 + date.minutes * 60 + date.seconds;
						$(item).find("input[name=open_date_end_time]").val(time || 0);
					}
				});
				$('#openDateEndTime' + (index ? index : '')).val(endTime ? ns.time_to_date((initTime + parseInt(endTime)), 'H:i:s') : '');
			})
		}
		fetchOpenDateTimeSelect();

		$('body').off('click', '.open-date-delivery-time .delete').on('click', '.open-date-delivery-time .delete', function () {
			$(this).parents('.layui-form-item').remove();
		});

		$('body').off('click', '.open-date-delivery-time .add').on('click', '.open-date-delivery-time .add', function () {
			var length = $('.open-date-delivery-time .layui-form-item').length;
			if (length >= 3) { layer.msg('最多添加三个时段'); return;}
			var h = `<div class="layui-form-item" >
				<label class="layui-form-label"></label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" id="openDateStartTime`+ length +`" lay-verify="open_date_start_time" placeholder="营业开始时间" value="" readonly >
					<input type="hidden" class="layui-input" name="open_date_start_time" placeholder="营业开始时间" value="">
				</div>
				<div class="layui-form-mid layui-word-aux">~</div>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" id="openDateEndTime`+ length +`" lay-verify="open_date_end_time" placeholder="营业结束时间" value="" readonly >
					<input type="hidden" class="layui-input" name="open_date_end_time" placeholder="营业送结束时间" value="">
				</div>
				<div class="layui-form-mid layui-word-aux">
					<a href="javascript:;" class="text-color delete">删除</a>
				</div>
			</div>`;
			$('.open-date-delivery-time').append(h);
			fetchOpenDateTimeSelect();
		});
		/************************************* 营业时间选择 结束 **********************************/

		form.on('radio(day_select)', function (data){
			if (data.value == 1) {
				$(data.elem).parents('.day-wrap').find('input[type="number"]').prop('readonly', false);
			} else {
				$(data.elem).parents('.day-wrap').find('input[type="number"]').prop('readonly', true);
			}
		})

		form.on('switch(status)', function (data){
			if (!data.elem.checked) {
				$(".close-block").removeClass('layui-hide');
			} else {
				$(".close-block").addClass('layui-hide');
			}
		})

		form.on('radio(close_show)', function (data){
			if (data.value == 1) {
				$(".close-desc-block").removeClass('layui-hide');
			} else {
				$(".close-desc-block").addClass('layui-hide');
			}
		})

		// 溢价类型切换
		form.on('radio(markup_type)', function (data){
			if (data.value == 'none') {
				$(".markup-value-item").addClass('layui-hide');
				$(".category-select-item").addClass('layui-hide');
			} else {
				$(".markup-value-item").removeClass('layui-hide');
				// 更新单位显示
				if (data.value == 'percent') {
					$(".markup-unit-text").text('%');
					$(".fixed-desc").addClass('layui-hide');
					$(".percent-desc").removeClass('layui-hide');
				} else {
					$(".markup-unit-text").text('元');
					$(".percent-desc").addClass('layui-hide');
					$(".fixed-desc").removeClass('layui-hide');
				}
				// 检查应用范围
				var applyTo = $('input[name="markup_apply_to"]:checked').val();
				if (applyTo == 'category') {
					$(".category-select-item").removeClass('layui-hide');
				}
			}
		})

		// 应用范围切换
		form.on('radio(markup_apply_to)', function (data){
			if (data.value == 'category') {
				$(".category-select-item").removeClass('layui-hide');
			} else {
				$(".category-select-item").addClass('layui-hide');
			}
		})

		// 选择分类按钮
		$('#selectCategory').on('click', function(){
			// 这里可以打开分类选择弹窗
			layer.msg('分类选择功能待实现');
		})

        form.verify({
			start_time: function(value, item){
				if($('[name="is_pickup"]').is(':checked')) {
					if(!value) return '请选择自提开始时间';
					var end_time = $(item).parents('.layui-form-item').find("input[name=end_time]").val();
					var start_time = $(item).parents('.layui-form-item').find("input[name=start_time]").val();
					if (parseInt(start_time) > parseInt(end_time)) {
						return '开始时间不能大于结束时间';
					}
					// var prev_endtime = $(item).parents('.layui-form-item').prev('.layui-form-item').find("input[name=end_time]").val();
					// if (prev_endtime && parseInt(prev_endtime) > parseInt(start_time)) return '开始时间不能小于上一阶段结束时间';
				}
			},
			end_time: function(value, item){
				if($('[name="is_pickup"]').is(':checked')) {
					if(!value) return '请选择自提结束时间';
					var end_time = $(item).parents('.layui-form-item').find("input[name=end_time]").val();
					var start_time = $(item).parents('.layui-form-item').find("input[name=start_time]").val();
					var time_interval = $('[name="time_interval"]:checked').val();
					if (parseInt(end_time) < parseInt(start_time)) {
						return '结束时间不能小于开始时间';
					}
					if ((parseInt(end_time) - parseInt(start_time)) / 60 < parseInt(time_interval)) {
						return '时间间隔不能小于' + time_interval + '分钟';
					}
				}
			},
			open_date_start_time: function(value, item){
				if(!value && $('input[name=open_date_start_time]').length > 1) return '请选择营业开始时间';
				var end_time = $(item).parents('.layui-form-item').find("input[name=open_date_end_time]").val();
				var start_time = $(item).parents('.layui-form-item').find("input[name=open_date_start_time]").val();
				if (parseInt(start_time) > parseInt(end_time)) {
					return '开始时间不能大于结束时间';
				}
				var prev_endtime = $(item).parents('.layui-form-item').prev('.layui-form-item').find("input[name=open_date_end_time]").val();
				// if (prev_endtime && parseInt(prev_endtime) > parseInt(start_time)) return '开始时间不能小于上一阶段结束时间';
			},
			open_date_end_time: function(value, item){
				if(!value && ($('input[name=open_date_end_time]').length > 1 || Number($('input[name=open_date_start_time]').val()) > 0)) return '请选择营业结束时间';
				var end_time = $(item).parents('.layui-form-item').find("input[name=open_date_end_time]").val();
				var start_time = $(item).parents('.layui-form-item').find("input[name=open_date_start_time]").val();
				if (parseInt(end_time) < parseInt(start_time)) {
					return '结束时间不能小于开始时间';
				}
			},
			required : function(value, item){
				var msg = $(item).attr("placeholder") != undefined ? $(item).attr("placeholder") : '';
				if(value == '') return msg;
			},
			time_week: function(){
				if ($('[name="is_pickup"]').is(':checked') && $('[name="time_type"]:checked').val() == 1 && !$('.time-week:checked').length)
					return '请选择可配送日期';
			},
			advance_day: function (value){
				if ($('[name="advance_day"]:checked').val() == 1) {
					if (value == '' || value == 0) return '请输入提前预约时间';
					if (value < 0) return '提前预约时间不能为负数';
				}
			},
			most_day: function (value){
				if ($('[name="most_day"]:checked').val() == 1) {
					if (value == '' || value == 0) return '请输入最长可预约时间';
					if (value < 0) return '最长可预约时间不能为负数';
					if (value > 15) return '最长可预约时间不能超过15天';
				}
			},
			is_o2o:function (value, item){
				var longitude = '{$info.longitude}';
				var latitude = '{$info.latitude}';
				if((!longitude || !latitude) && item.checked){
					return '开启同城配送需先完善门店地址';
				}
			},
			is_pickup:function (value, item){
				var longitude = '{$info.longitude}';
				var latitude = '{$info.latitude}';
				if((!longitude || !latitude) && item.checked){
					return '开启门店自提需先完善门店地址';
				}
			},
			close_desc:function (value){
				var status = $("input[name=status]").prop('checked');
				var close_show = $("input[name=close_show]:checked").val();
				if(!status && close_show === '1' && !value){
					return '请输入休息说明';
				}
			},
			markup_value: function (value){
				var markupType = $('[name="markup_type"]:checked').val();
				if (markupType != 'none') {
					if (!value || value == '') {
						return '请输入溢价数值';
					}
					if (value < 0) {
						return '溢价数值不能为负数';
					}
					if (markupType == 'percent' && value > 100) {
						return '百分比溢价不能超过100%';
					}
					if (markupType == 'fixed' && value > 99999) {
						return '固定金额溢价不能超过99999元';
					}
				}
			},
			markup_category_names: function (value){
				var markupType = $('[name="markup_type"]:checked').val();
				var applyTo = $('[name="markup_apply_to"]:checked').val();
				if (markupType != 'none' && applyTo == 'category' && !value) {
					return '请选择商品分类';
				}
			}
        });

        timeTypeChange($("input[name=time_type]:checked").val());
        
        form.on('radio(time_type)', function(data){
			timeTypeChange(data.value);
		});
		form.on('switch(pickup)', function(data){
			if(data.elem.checked){
				$('.time-view').removeClass('layui-hide')
			}else{
				$('.time-view').addClass('layui-hide')
			}
		});

		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data){
			//组装自提时间
			data.field.delivery_time = [];
			$('.delivery-time .layui-form-item').each(function (index, item) {
				data.field.delivery_time.push({
					start_time: $(item).find("input[name=start_time]").val(),
					end_time: $(item).find("input[name=end_time]").val()
				})
			});
			data.field.delivery_time = JSON.stringify(data.field.delivery_time);
			//组装营业时间
			data.field.open_date_config = [];
			$('.open-date-delivery-time .layui-form-item').each(function (index, item) {
				let start_time = $(item).find("input[name=open_date_start_time]").val();
				let end_time = $(item).find("input[name=open_date_end_time]").val();
				if(Number(start_time) >= 0 && Number(end_time) > 0){
					data.field.open_date_config.push({
						start_time: start_time,
						end_time: end_time,
					})
				}
			});
			data.field.open_date = getOpenDateShow(data.field.open_date_config);
			data.field.open_date_config = JSON.stringify(data.field.open_date_config);

			data.field.advance_day = $('[name="advance_day"]:checked').val() == 0 ? 0 : $('[name="advance_day_num"]').val();
			data.field.most_day = $('[name="most_day"]:checked').val() == 0 ? 0 : $('[name="most_day_num"]').val();

			if (store_business == 'store' && !data.field.is_express && !data.field.is_o2o && !data.field.is_pickup) {
				layer.msg('至少需启用一种配送方式');
				return;
			}

			if(repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				type : "POST",
				dataType: 'JSON',
				url : ns.url("store://shop/store/operate"),
				async : true,
				data : data.field,
				success : function(res) {
					repeat_flag = false;
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title: '操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function(index, layero) {
								location.hash = ns.hash("store://shop/store/lists");
								layer.close(index);
							},
							btn2: function(index, layero) {
								layer.close(index);
							}
						});
					} else {
						layer.msg(res.message);
					}
				}
			})
		});

	});

	//获取营业时间显示
	function getOpenDateShow(open_date_config) {
		let open_date = '';
		if(open_date_config.length > 0){
			let arr = [];
			open_date_config.forEach((item)=>{
				arr.push(stampToTime(item.start_time)+'-'+stampToTime(item.end_time));
			})
			open_date = arr.join('，');
		}else{
			open_date = '全天';
		}
		return open_date;
	}

	//时间戳转时间
	function stampToTime(stamp, type='all'){
		let m = Math.floor(stamp / 60);
		let h = Math.floor(m / 60);
		m = m % 60;
		if(m < 10) m = '0'+m;
		if(h < 10) h = '0'+h;
		if(type === 'harf' && h > 12) h -= 12;
		return h + ':' + m;
	}

	function backStockList() {
		location.hash = ns.hash("store://shop/store/lists");
	}

	function timeTypeChange(type){
         if(type == 1){
			$('.time-type-view').show();
		}else{
            $('.time-type-view').hide();
        }
	}
</script>
