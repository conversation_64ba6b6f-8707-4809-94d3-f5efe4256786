*{
	 padding:0px;
	 margin:0px;
 }
body{
	font-family:Verdana, Arial, Helvetica, sans-serif;
	font-size:12px;
	color: #333;
	background-color: #edf0f3;
}
ul{
	list-style:none;
}
a{
	color: #333;
	text-decoration: none;
}
a:hover{
	color:#FF6A00;
	text-decoration:none;
}
input,button,select{
	vertical-align:middle;
	outline: none;
}
.fc-690{
	color:#333;
}
.fs-14{
	font-size:14px;
}

img{width: 100%; height:100%}
.guide {display: flex;position: relative;}
.head-block {position: fixed;height:60px;width: 100%;overflow: hidden;background:rgba(0, 0, 0, 0.19);top: 0;left:0}
.head-block-title {width: 1200px; height:60px;margin: 0 auto}
.head-block .head-block-left {display: flex;float: left}
.head-block .head-block-left>div{width: 140px; height:60px;}
.head-block .head-block-left span{color:#fff;font-size: 14px;display: inline-block;padding-top: 25px;padding-left: 15px;}
.head-block .head-block-right {float: right;height: 60px;line-height: 60px}
.head-block .head-block-right a{color:#fff;text-decoration: none;font-size: 14px}
.head-block .head-block-right span{color:#fff;font-size: 16px}