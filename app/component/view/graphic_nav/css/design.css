@CHARSET "UTF-8";

/*图文导航组件*/
.graphic-navigation .preview-draggable .preview-box {
	padding: 8px 0;
	margin: 0 15px;
	border-radius: 5px;
}

.graphic-navigation .preview-draggable ul {
	overflow: hidden;
	list-style: none;
}

.graphic-navigation .preview-draggable ul.horizontal-scroll {
	overflow-x: scroll;
	white-space: nowrap;
}

.graphic-navigation .preview-draggable ul.horizontal-scroll::-webkit-scrollbar {
	display: none;
}

.graphic-navigation .preview-draggable li {
	width: 50%;
	text-align: center;
	display: inline-block;
	vertical-align: top;
}

.graphic-navigation .preview-draggable li img {
	width: auto;
	height: auto;
	max-width: 100%;
	max-height: 100%;
}

.graphic-navigation .preview-draggable li:last-child {
	border: 0;
}

.graphic-navigation .preview-draggable li span {
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	height: 20px;
	display: block;
	line-height: 20px;
}

/*.graphic-navigation .preview-draggable .graphic-nav{visibility: hidden;}*/
.graphic-navigation .preview-draggable .graphic-nav>.wrap {
	/* overflow-x: hidden;white-space: nowrap; background: #ffffff; */
	display: flex;
	/* justify-content: space-around; */
	flex-wrap: wrap;
	padding: 0 5px;
}

.graphic-navigation .graphic-nav-list .template-list .template-item {
	float: left;
	text-align: center;
	border: 1px solid #e5e5e5;
	margin-right: 20px;
	padding: 5px;
	background: #ffffff;
	cursor: pointer;
}

.graphic-navigation .graphic-nav-list .template-list .template-item img {
	display: block;
}

.graphic-navigation .add-item {
	padding: 10px;
	border: 1px dashed #e5e5e5;
	margin: 16px 0 10px;
	cursor: pointer;
	text-align: center;
}

.graphic-navigation .add-item i {
	display: inline-block;
	height: 24px;
	line-height: 24px;
	font-size: 18px;
	margin-right: 10px;
	font-style: normal;
}

.graphic-navigation .add-item span {
	display: inline-block;
	height: 24px;
	line-height: 24px;
}

.graphic-navigation .error-msg {
	margin: 5px 0 0 53px;
	color: #f44;
	display: none;
}

/* 新的css */
.graphic-navigation .graphic-nav{
	padding: 10px 5px;
}
/* 预览 */
/* 固定显示  */
.graphic-navigation .graphic-nav.fixed{
	display: flex;
	flex-wrap: wrap;
}
/* 单边滑动 */
.graphic-navigation .graphic-nav.singleSlide{
	display: flex;
	overflow-x: auto;
}
.graphic-navigation .graphic-nav.singleSlide::-webkit-scrollbar {
    height: 5px;
}
.graphic-navigation .graphic-nav.singleSlide::-webkit-scrollbar-track {
    background-color: #e4e4e4;
}
 
.graphic-navigation .graphic-nav.singleSlide::-webkit-scrollbar-thumb {
    background-color: #999;
}

.graphic-navigation .graphic-nav.singleSlide .graphic-nav-item{
	flex-shrink: 0;
}
/* 分页 */
.graphic-navigation .graphic-nav.pageSlide{
	position: relative;
}
.graphic-navigation .graphic-nav.pageSlide .graphic-nav-wrap{
	display: flex;
	flex-wrap: wrap;
	width: 100%;
	height: 100%;
}
.graphic-navigation .graphic-nav.pageSlide .carousel-btn{
	position: absolute;
	top: 0;
	bottom: 0;
	right: 0;
	left: 0;
}

.graphic-navigation .graphic-nav.pageSlide .carousel-btn .arrows{
	display: flex;
    justify-content: space-between;
    padding: 0 15px;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}

.graphic-navigation .graphic-nav.pageSlide .carousel-btn .arrows i{
	display: none;
	width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    color: #fff;
    background-color: rgba(0, 0, 0, .35);
	border-radius: 50%;
	cursor: pointer;
}

.graphic-navigation .graphic-nav.pageSlide .carousel-btn .dot-wrap{
	text-align: center;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.graphic-navigation .graphic-nav.pageSlide .carousel-btn .dot-wrap.hide{
	display: none;
}

.graphic-navigation .graphic-nav.pageSlide .carousel-btn .dot-wrap.straightLine i{
	width: 12px;
    height: 4px;
    border-radius: 0;
}

.graphic-navigation .graphic-nav.pageSlide .carousel-btn .dot-wrap i{
	display: inline-block;
	width: 7px;
	height: 7px;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, .1);
	margin-right: 5px;
	cursor: pointer;
}

.graphic-navigation .graphic-nav.pageSlide .carousel-btn .dot-wrap i.active{
	background-color: rgba(0, 0, 0, .5);
}

.graphic-navigation .graphic-nav.pageSlide .carousel-btn:hover .arrows i{
	display: block;
}

.graphic-navigation .graphic-nav .graphic-nav-item{
	display: flex;
    flex-direction: column;
	align-items: center;
	padding: 7px 0;
	box-sizing: border-box;
}

.graphic-navigation .graphic-nav .graphic-nav-item .graphic-text{
	padding-top: 6px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	width: 100%;
	text-align: center;
}

.graphic-navigation .graphic-nav .graphic-nav-item .graphic-img{
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 50px;
	height: 50px;
	font-size: 40px;
}

.graphic-navigation .graphic-nav .graphic-nav-item .graphic-img .tag{
	position: absolute;
    top: -5px;
    right: -12px;
    color: #fff;
    background-color: red;
    border-radius: 12px;
    border-bottom-left-radius: 0;
    transform: scale(0.8);
    padding: 2px 6px;
	font-size: 12px;
}

.graphic-navigation .graphic-nav .graphic-nav-item .graphic-img img{
	max-width: 100%;
	max-height: 100%;
}

.graphic-navigation .graphic-nav .graphic-nav-item .graphic-img i{
	font-size: 25px;
	color: #666;
}

/* 图文导航项 */
.graphic-navigation p.hint {
	padding-left: 15px;
	font-size: 12px;
	color: #909399;
	line-height: 20px;
}

.graphic-navigation .graphic-nav-list>ul {
	padding: 10px 0 10px 15px;
}

.graphic-navigation .graphic-nav-list>ul>li {
	padding: 10px 10px 10px 0px;
	background: #ffffff;
	border: 1px dashed #e5e5e5;
	position: relative;
	margin-top: 16px;
}

.graphic-navigation .graphic-nav-list>ul>li>.iconfont {
	position: absolute;
	top: calc(50% - 10px);
	left: 15px;
	cursor: move;
	font-size: 20px;
}

.graphic-navigation .graphic-nav-list>ul>li:first-child {
	margin-top: 0;
}

.graphic-navigation .graphic-nav-list>ul>li:hover .del {
	display: block;
}
.graphic-navigation .edit-attribute .attr-wrap .restore-wrap .img-block, .graphic-navigation .edit-attribute .attr-wrap .restore-wrap .img-block.has-choose-image > div{
	width: 50px;
	height: 50px;
	line-height: 50px;
}
.graphic-navigation .edit-attribute .attr-wrap .restore-wrap .img-block.has-choose-image img {
	width: 35px;
	height: 35px;
}
.graphic-navigation .edit-attribute .icon-box {
	width: 60px;
	height: 60px;
	font-size: 60px;
	border: 1px dashed #ddd;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0!important;
	cursor: pointer;
	position: relative;
}
.graphic-navigation .edit-attribute .icon-box .select-icon {
	width: inherit;
	height: inherit;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	line-height: 1;
}
.graphic-navigation .edit-attribute .icon-box .select-icon .add {
	font-size: 26px;
	color: var(--base-color);
}

.graphic-navigation .edit-attribute .icon-box .operation {
	position: absolute;
	width: 100%;
	height: 100%;
	background: rgba(0,0,0,.6);
	flex-direction: column;
	display: none;
}

.graphic-navigation .edit-attribute .icon-box:hover .operation {
	display: flex;
}

.graphic-navigation .edit-attribute .icon-box .operation-warp {
	flex: 1;
	height: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}
.graphic-navigation .edit-attribute .icon-box .iconfont {
	margin: 0 3px;
	font-size: 16px!important;
}
.graphic-navigation .edit-attribute .icon-box .operation .js-replace{
	line-height: 1;
	color: #fff;
	text-align: center;
	padding: 5px 0;
	background: rgba(0,0,0,.7);
	font-size: 12px;
	height: unset;
}
.graphic-navigation .edit-attribute .graphic-nav-list .icon-box .icon-wrap:hover .operation{
	display: block;
}
.graphic-navigation .edit-attribute .graphic-nav-list .img-upload .upload-img-box:hover .operation{
	display: block;
}

.graphic-navigation .edit-attribute .navigation-set-list .img-upload {
	display: flex;
	align-items: center;

}
.graphic-navigation .edit-attribute .navigation-set-list .img-upload img {
	width: 100%;
	height: 100%;
}

.graphic-navigation .edit-attribute .navigation-set-list .action-box {
	display: flex;
}

.graphic-navigation .edit-attribute .navigation-set-list .action {
	margin-right: 3px;
	width: 42px;
	height: 28px;
	line-height: 28px;
	text-align: center;
	border: 1px solid #EEEEEE;
	cursor: pointer;
}

.graphic-navigation .edit-attribute .navigation-set-list .action:hover {
	border-color: var(--base-color);
	color: var(--base-color);
}

.graphic-navigation .img-icon-box{
	display: flex;
	align-items: center;
}