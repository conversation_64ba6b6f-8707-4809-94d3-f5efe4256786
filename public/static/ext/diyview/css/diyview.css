#diyView .layui-form-label.sm{width: 90px;font-size: 13px;}
#diyView .layui-form-label.sm + .layui-input-block{margin-left: 100px;}
.layui-form-select dl {z-index: 9999;}
.layui-btn:hover {opacity: 1;}
.footer{display:none;}
.top-full-screen .layui-layout-admin .layui-body{top: 0;left: 0;}
.layui-layout-admin .layui-body .body-content {min-height: initial;margin: 0;padding-top: 0;padding-bottom: 0; background: #fff;}
.position-page-btn {position: absolute;left: 50%;margin-left: 140px;border-color: #f7f8fa;box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .1);z-index: 9;}

/*快捷操作*/
.quick-action{position: absolute;width: 42px;background: #fff;left: 50%;top: 20%;margin-left: 140px;border-radius: 4px;text-align: center;box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .1);z-index: 9;}
.quick-action li{position: relative;}
.quick-action li i {display: block;text-align: center;font-size: 20px;line-height: 40px;cursor: pointer;}
.quick-action li.disabled i{color: #CECECE;/*cursor: not-allowed;*/}
.quick-action li span {position: absolute;top: 5px;right: -60px;font-size: 14px;height: 30px;line-height: 30px;padding: 0 10px;background: #fff;border-radius: 4px;box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .1);display: none;}
.quick-action li:not(.disabled):hover span {display: block;}
.quick-action li span:after {content: "";position: absolute;left: -3px;top: 50%;margin-top: -3px;background: #fff;width: 6px;height: 6px;transform: rotate(45deg);}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

.loading-layer {width: 400px;height: 525px;position: absolute;z-index: 997;background: #f8f8f8;left: 50%;top: 84px;margin-left: -270px;}
.loading-anim {position: absolute;left: 50%;top: 40%;transform: translate(-50%, -50%);width: 35px;height: 35px;perspective: 800px;transform-style: preserve-3d;transition: all 0.2s ease-out;}
.loading-anim > .item {position: relative;width: 35px;height: 35px;perspective: 800px;transform-style: preserve-3d;transition: all 0.2s ease-out;}
.loading-anim .out {position: absolute;border-radius: 50%;
	border: 3px solid;top: 15%;left: 15%;width: 70%;height: 70%;
	border-left-color: var(--base-color);
	border-right-color: #C5C5C5;
	border-top-color: var(--base-color);
	border-bottom-color: #C5C5C5;
	animation: spin 0.6s linear normal infinite;
	perspective: 800px;
	transform-style: preserve-3d;
	transition: all 0.2s ease-out;}


/* 顶部风格 */
.nav-style {display: flex;flex-wrap: wrap;}
.nav-style .text-title {margin-right: 2%;margin-bottom: 15px;width: 32%;height: 100px;line-height: 100px;border: 1px solid #cccc;background-color: #eee;display: inline-block;box-sizing: border-box;}
.nav-style .text-title:nth-child(3n+3) {margin-right: 0;}
.nav-style .text-title img {width: 100%}

.template-edit-title {border-bottom: 5px solid #f6f7f9;}
.template-edit-title:last-of-type{border-bottom: none;}
.template-edit-title h3 {font-size: 14px;padding: 10px;color: #303133;}

/*颜色选择器*/
.colorSelector {display: inline-block;vertical-align: middle;cursor: pointer;border-radius: 2px;width: 38px;}
.colorSelector:nth-of-type(2){margin-left: 5px;}
.colorSelector div{border-radius: 2px;width: 34px;margin-left: -17px;background-position: initial;}
.color-selector-reset {display: inline-block;line-height: 34px;cursor: pointer;margin-left: 10px;}
.flexbox-fix-btn {justify-content: center;}
.flexbox-fix-btn .btn {width: 40px;line-height: 22px;font-size: 12px;margin: 0;box-sizing: border-box;border-radius: 3px;}
.flex {justify-content: space-between;display: flex;align-items: center;}
.flex .flex_fill {flex: 1;text-align: right;margin-left: 5px;}
.flex .flex_left {display: flex;align-items: center;}
.flex .flex_left .curr-color {color: #303133;margin-left: 7px;}
.flex .flex_left .curr-color span:first-child{margin-right: 10px;}

.selected-style {cursor: pointer;float: right;color: #909399;}
.selected-style i {font-size: 12px;line-height: 34px;color: #818181;margin-left: 5px;}

/* 链接地址 */
.layui-input-block span.sm {display: flex;line-height: 34px;cursor: pointer;}
.layui-input-block span.sm span {display: inline-block;max-width: 130px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
.layui-input-block span.sm i {margin-left: 5px;font-size: 12px;color: #818181;}
.component-links .layui-input-block .sm {float: right;}

/* 选择icon */
.component-icon .layui-input-block .sm {width: 50px;height: 50px;justify-content: space-around;align-items: center;border: 1px solid #CCC;}
.component-icon .layui-input-block .sm i{margin: 0;font-size: 16px;}
.component-icon .layui-input-block .sm i.js-icon{font-size: 20px;}

/* 进度条样式 */
.layui-input-block {line-height: 34px;min-height: 34px;}
.side-process {display: inline-block;margin-right: 15px;margin-left: 5px;width: 260px;vertical-align: middle;}
.slide-prompt-text {color: #909399;}
.layui-slider-wrap {top: -17px;}
.layui-slider {height: 3px;}

#diyView {position: relative;background: #f7f8fa;padding: 20px 0 0;visibility: hidden;/*min-width: 1300px;*/}

.custom-save {text-align: center;background: #fff;padding: 15px 400px 15px 290px;}

.preview-wrap {overflow: hidden;margin-left: 255px;margin-right: 392px;}
.preview-wrap .preview-restore-wrap { /*width: 102%;*/margin-right: -12px;visibility: hidden;}
.preview-wrap .preview-restore-wrap .div-wrap {overflow: auto;height: 525px;}


.preview-wrap .preview-restore-wrap .diy-view-wrap {width: 375px;background-repeat: no-repeat;background-position-y: 128px;background-size: 100%;margin: 0 auto 20px;border:2px solid #f0f1f3;padding-top: 64px;margin-top: -64px;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head {height: 64px;width: 375px;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head > span {background: #fff url(../img/preview_head.png) no-repeat 50%/cover;font-size: 14px;display: block;text-align: center;height: 64px;line-height: 82px;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;cursor: pointer;margin: 0 auto;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-block {min-height: 400px;}

/*组件列表*/
.component-list {width: 235px;padding: 0 10px;background: #ffffff;float: left;position: absolute;top: 0;overflow: hidden;user-select: none;z-index: 9999;}
.component-list .tab{display: flex;margin: 10px 0 5px -10px;}
.component-list .tab span{font-weight: normal;font-size: 14px;cursor: pointer;line-height: 40px;flex: 1;text-align: center;position: relative;background: #fff;border-top: 1px solid #f1f1f1;border-bottom: 1px solid #f1f1f1;border-left: 1px solid #f1f1f1;}
.component-list .tab span:last-child{border-right: 1px solid #f1f1f1;}
.component-list .tab span.selected{background-color: #f1f1f1;}
/*.component-list .tab span.selected:after{content: '';border-bottom: 2px solid;position: absolute;left: 50%;bottom: 0;width: 65%;transform: translate(-50%, -50%);}*/
.component-list nav {height: 610px;overflow-y: auto;width: 257px;}
.component-list h3 {font-size: 14px;/*margin-top: 10px;*/cursor: pointer;line-height: 40px;}
.component-list h3 img {width: 16px;margin-right: 5px;}
.component-list ul {overflow: hidden;margin: 0;padding: 0;transition: all .3s;opacity: 1;}
.component-list ul li {float: left;font-size: 12px;width: 78px;height: 75px;text-align: center;cursor: pointer;position: relative;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;}
.component-list ul li.hot:after {content: '';background: url(../img/hot_component.png) no-repeat center/100%;position: absolute;width: 16px;height: 20px;right: 20%;top: 0;}
.component-list ul li.disabled { /*cursor: not-allowed;*/color: #bbb;}
.component-list ul li img {width: 42px;margin-top: 11px;}
.component-list ul li span:last-child {font-size: 12px;/*color: #909399;*/display: block;margin-top: 5px;}
.component-list ul li i{font-size: 23px;margin-bottom: 4px;/*color: #909399;*/margin-top: 14px;display: inline-block;}

/*预览*/
.draggable-element {outline: 2px dotted transparent; /*background: #ffffff;position: relative;*/}
.draggable-element .preview-draggable { cursor: move;position: relative;}
.draggable-element .preview-draggable .preview-box {padding: 0;}
.draggable-element.selected {outline: 2px solid;}
.del {background: #999;color: #FFFFFF;position: absolute;border-radius: 50%;width: 20px;height: 20px;font-size: 12px;font-style: normal;line-height: 18px;text-align: center;right: -10px;top: -10px;cursor: pointer;z-index: 1;display: none;}
/*ul li .content-block + .del {right: unset;left: 68px;top: 0;}*/
.draggable-element .preview-draggable:hover .del {display: block;}
.draggable-element .comp-title{position: absolute;left: -90px;min-width: 70px;min-height: 20px;padding: 4px 7px;box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .1);box-sizing: border-box;text-align: center;line-height: 20px;font-size: 12px;background: #fff;border-radius: 2px;top:0;/*top: 50%;margin-top: -14px;*/}

/*右侧编辑栏*/
.edit-attribute {position: absolute;top: 0;right: 0;background: #ffffff;border-top: 1px solid #f7f8fa;width: 370px;padding: 10px;z-index: 1;overflow: hidden;display: none;}
.draggable-element.selected .edit-attribute{display: block;}

/* 选中页面设置 */
.page-set-wrap.selected .edit-attribute{display: block;}

/* 选中弹窗广告组件 */
.pop-window-wrap.selected .edit-attribute{display: block;}

.edit-attribute .attr-wrap {width: 392px;overflow-x: hidden;overflow-y: auto;height: 600px;}
.edit-attribute .attr-wrap .restore-wrap {width: 360px;}
.edit-attribute .attr-wrap .restore-wrap .layui-form-label {color: #666 !important;}
.edit-attribute .attr-wrap .restore-wrap .attr-title {padding: 10px 0 15px 10px;border-bottom: 2px solid #f2f4f6;margin-bottom: 10px;color: #303133;display: flex;justify-content: space-between;align-items: center;}
.edit-attribute .attr-wrap .restore-wrap .attr-title .title{font-size: 18px;}
.edit-attribute .attr-wrap .restore-wrap .attr-title .tab-wrap{background-color: #f5f5f5;border-radius: 50px;font-size: 14px;display: flex;}
.edit-attribute .attr-wrap .restore-wrap .attr-title .tab-wrap span{cursor: pointer;padding: 5px 15px;border-radius: 50px;}
.edit-attribute .attr-wrap .restore-wrap .attr-title .tab-wrap span.active{color: #fff;}

.edit-attribute .attr-wrap .restore-wrap .layui-form input[type=radio] {display: inline-block;opacity: 0;position: absolute;top: 10px;width: 60px;height: 20px;cursor: pointer;}
.edit-attribute .attr-wrap .restore-wrap .img-block {display: inline-block;margin-right: 10px;cursor: pointer;vertical-align: top;text-align: center;border: 1px solid #CCCCCC;width: 66px;height: 66px;line-height: 66px;}
.edit-attribute .attr-wrap .restore-wrap .img-block > div {position: relative}
.edit-attribute .attr-wrap .restore-wrap .img-block > div.del {display: inline-block;margin-right: 10px;cursor: pointer;vertical-align: top;text-align: center;border: 1px solid #CCCCCC;width: 66px;height: 66px;line-height: 66px;}
.edit-attribute .attr-wrap .restore-wrap .img-block + .content-block {display: inline-block;width: calc(100% - 78px);font-size: 14px;}
.edit-attribute .attr-wrap .restore-wrap .img-block + .content-block .layui-form-label.sm {width: 68px !important;}
.edit-attribute .attr-wrap .restore-wrap .img-block + .content-block .layui-input-block {margin-left: 78px;}
.edit-attribute .attr-wrap .restore-wrap .img-block + .content-block .layui-input-block span.sm {float: right;}
.edit-attribute .attr-wrap .restore-wrap .img-block i.add {display: block;font-style: normal;text-align: center;font-size: 26px;min-width: 26px;}
.edit-attribute .attr-wrap .restore-wrap .img-block i.del {display: none;}
.edit-attribute .attr-wrap .restore-wrap .img-block:hover i.del {display: block;}
.edit-attribute .attr-wrap .restore-wrap .img-block.has-choose-image {width: 66px;height: 64px;border: 1px dashed #e5e5e5;display: inline-block;vertical-align: top;position: relative;line-height: 64px;margin-right: 10px;text-align: center;padding: 0;overflow: hidden}
.edit-attribute .attr-wrap .restore-wrap .img-block.has-choose-image > div {width: 66px;height: 64px;}
.edit-attribute .attr-wrap .restore-wrap .img-block.has-choose-image img {width: auto;height: auto;max-width: 100%;max-height: 100%;}
.edit-attribute .attr-wrap .restore-wrap .img-block.has-choose-image span {position: absolute;bottom: 0;left: 0;width: 100%;text-align: center;font-size: 12px;background: rgba(0, 0, 0, .6);color: #ffffff;line-height: initial;cursor: pointer;}
.edit-attribute .attr-wrap .restore-wrap .img-block span {font-size: 12px;line-height: 23px;}
.layui-btn.layui-btn-primary.sm {margin-top: 5px;padding: 5px 10px !important;height: auto;font-size: 12px;border-radius: 0;vertical-align: baseline;line-height: 1}
/*.edit-attribute .attr-wrap .restore-wrap .layui-form-radio {margin-top: 0;line-height: 34px;}*/
/*.edit-attribute .attr-wrap .restore-wrap .layui-form-radio:last-child{margin-right: 0;}*/

/* 单选 */
.icon-radio .icon-wrap {float: right;}
.icon-radio .icon-wrap li {display: inline-block;width: 50px;height: 32px;line-height: 30px;text-align: center;border: 1px solid #EEEEEE;border-right: 1px solid transparent;cursor: pointer;float: left;box-sizing: border-box;color: #909399;}
.icon-radio .icon-wrap li .iconfont.angle{font-size: 14px;}
.icon-radio .icon-wrap li:last-child {border-right: 1px solid #EEEEEE;}

/* 复选 */
.checkbox-wrap .layui-form-checkbox, .checkbox-wrap .layui-input-inline-checkbox .layui-form-checkbox {float: right;}
.checkbox-wrap .layui-form-checkbox[lay-skin=primary] {margin-top: 4px;padding-left: 20px;}
.checkbox-wrap .layui-input-inline-checkbox span {color: #666;}
.checkbox-wrap .layui-input-block {margin-left: 35px;}
.checkbox-wrap .layui-input-inline-checkbox .layui-form-checkbox[lay-skin=primary] {margin-top: 8px;}

/* 图片上传 */
.edit-attribute .attr-wrap .restore-wrap .img-upload .img-block {padding: 4px;margin-right: 0;float: right;}
.edit-attribute .attr-wrap .restore-wrap  .img-block .operation{position: absolute;width: 100%;height: 100%;background: rgba(0,0,0,.6);flex-direction: column;display: none;top:0;}
.edit-attribute .attr-wrap .restore-wrap  .img-block:hover .operation {display: flex;}
.edit-attribute .attr-wrap .restore-wrap  .img-block .operation-warp {flex: 1;height: 0;display: flex;align-items: center;justify-content: center;color: #fff;}
.edit-attribute .attr-wrap .restore-wrap  .img-block .iconfont {margin: 0 3px;font-size: 16px!important;}
.edit-attribute .attr-wrap .restore-wrap  .img-block .operation .js-replace{line-height: 1;color: #fff;text-align: center;padding: 5px 0;background: rgba(0,0,0,.7);font-size: 12px;}
.edit-attribute .attr-wrap .restore-wrap .img-block .icon-wrap:hover .operation{display: block;}
.edit-attribute .attr-wrap .restore-wrap  .img-block .upload-img-box:hover .operation{display: block;}
.img-upload + .diy-word-aux {margin-left: 15px;}

.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-1{}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-1 .preview-head-div {background: url(../img/preview_head.png) no-repeat 50%/cover;font-size: 16px;height: 64px;line-height: 90px;cursor: pointer;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-1 .preview-head-div span {display: inline-block;padding: 0 15px 0 15px;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-1.center .preview-head-div span{padding-right:20px;width: 160px;margin: 0 auto;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}

.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-2{}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-2 .preview-head-div {background: url(../img/preview_head.png) no-repeat 50%/cover;height: 64px;cursor: pointer;padding: 0 15px;line-height: 90px;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-2 .preview-head-div span{font-size: 16px;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-2 img{margin-right: 15px;max-width:150px;height: 28px;}

.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-3{}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-3 .preview-head-div {background: url(../img/preview_head.png) no-repeat 50%/cover;height: 64px;cursor: pointer;padding: 0 15px;line-height: 90px;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-3 .preview-head-div .img-text-search{padding-right: 100px;display: flex;align-items: center;padding-top: 28px;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-3 .preview-head-div .img-text-search img {margin-right: 10px;height: 30px;max-width: 85px;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-3 .preview-head-div .img-text-search .top-search-box {flex:1;width: 80%;height: 30px;display: flex;align-items:center;border-radius:30px;background: rgb(255, 255, 255);color:#909399;line-height: 2.1;border:1px solid #E6E6E6;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-3 .preview-head-div .img-text-search .top-search-box i {margin-right: 5px;margin-left: 10px;}

.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-4{}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-4 .preview-head-div {background: url(../img/preview_head.png) no-repeat 50%/cover;height: 64px;cursor: pointer;padding: 0 15px;line-height: 90px;display: flex;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-4 span.store-name{margin: 0 5px;font-size: 16px;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-4 .nearby-store-name{display: inline-block;margin: -1px 5px 0;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-4 .nearby-store-name span{background: rgba(0,0,0,.3);font-size: 12px;border-radius: 20px;padding: 5px 10px;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-4 i:first-child{font-size: 18px;}
.preview-wrap .preview-restore-wrap .diy-view-wrap .preview-head .nav-tabbar.style-4 i:last-child{font-size: 14px;}

.diy-word-aux {margin-left: 95px;display: block;margin-top: 5px;color: #B2B2B2;font-size: 12px;line-height: 1.6;}

.layui-layer-page .layui-layer-content {overflow: auto !important;}
.top-full-screen .layui-layout-admin .layui-body{padding-right: 0 !important;width: 100%  !important;margin-left: 0 !important;}
.top-full-screen .layui-layout-admin .layui-body .body-wrap{margin-top: 0 !important;}
.top-full-screen .custom-save{display: flex;justify-content: space-between;align-items: center;padding: 10px 0;border-bottom: 1px solid #ececec;}
/*.top-full-screen .custom-save .layui-btn{background-color: #fff;}*/
/*.top-full-screen .cancel-btn{border-color: #ececec;color: #333;}*/
/*.top-full-screen .cancel-btn:hover{color: #333;}*/

.bg-select .bg-select-ul{text-align:right}
.bg-select .bg-select-ul li{display:inline-block;width:20px;height:20px;border-radius:3px;border:1px solid #d7d7d7;vertical-align:middle;cursor:pointer;padding:2px;box-sizing:border-box;margin-left:5px}
.bg-select .bg-select-ul li div{width:100%;height:100%;border-radius:3px}

/* 超出单行影藏 */
.using-hidden {overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
/* 超出两行影藏 */
.multi-hidden {overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;}

/* 商品来源选择 */
.source-selected {display: flex;flex-wrap: wrap;}
.source-selected .source {flex: 1;}
.source-selected .source-item,.template-selected .source-item {width: 46px;height: 32px;text-align: center;border: 1px solid #e5e5e5;padding: 5px;cursor: pointer;background: #ffffff;box-sizing: border-box;border-right: 1px transparent solid;display: flex;align-items: center;justify-content: center;color: #909399;}
.source-selected .source-item:last-child,.template-selected .source-item:last-child{border-right: 1px solid #e5e5e5;}

/* 列表样式 */
.list-style .layui-input-block{margin-left: 40px !important;}
.template-selected {display: flex;flex-wrap: wrap;margin-left: 50px;}
.style-selected{display: flex;flex-wrap: wrap;margin-top: 10px;margin-left: 50px;}
.style-selected .layui-form-radio:nth-child(3n){margin-right: 0;}

/* 图片和图标选择 */
.icon-img-upload {width: 66px;height: 66px;font-size: 66px;display: flex;align-items: center;justify-items: center;border: 1px solid #CCCCCC;text-align: center;margin-right: 10px;position: relative;}
.icon-img-upload .add {color: var(--base-color);font-size: 26px;margin: 0 auto;width: 66px;height: 66px;line-height: 66px;text-align: center;cursor: pointer;}
.icon-img-upload img {max-width: 100%;max-height: 100%;width: 100%;}
.icon-img-upload .operation {position: absolute;left: 0;top: 0;width: 100%;height: 100%;background: rgba(0, 0, 0, 0.5);color: #fff;display: none;flex-direction: column;z-index: 5;}
.icon-img-upload:hover .operation {display: flex;}
.icon-img-upload .operation .operation-warp {flex: 1;height: 0;display: flex;align-items: center;justify-content: center;}
.icon-img-upload .operation-warp i {margin: 0 2px;cursor: pointer;}
.icon-img-upload .operation .js-replace {height: 24px;color: #fff;background: rgba(0, 0, 0, 0.5);font-size: 12px;line-height: 24px;cursor: pointer;}
.common-set .word-aux{margin: 0 0 0 100px;padding: 0;}

/* 选择图标风格 */
.select-icon-style {position: fixed;width: 100vw;height: 100vh;left: 0;top: 0;z-index: 9999;}
.select-icon-style .icon-style-wrap {position: absolute;background: #fff;border: 1px solid #ddd;right: 40px;margin-top: 15px;}
.select-icon-style .icon-style-wrap iframe {width: 100%;height: 100%;}