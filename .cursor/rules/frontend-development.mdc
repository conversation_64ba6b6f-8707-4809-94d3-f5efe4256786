---
description: NiuShop前端开发指南
globs: 
alwaysApply: false
---
# NiuShop前端开发指南

## 前端技术栈
- H5/小程序端：UNI-APP框架
- PC端：ElementUI + Vue
- 后台管理：LayUI

## 前端目录结构
- [h5](mdc:h5) - H5移动端源码
- [web](mdc:web) - PC端源码
- [cashregister](mdc:cashregister) - 收银台源码

## DIY装修系统
- 基于可视化组件开发
- 组件配置定义在config/diy_view.php
- 支持自定义主题风格和布局

## 前端开发流程
1. 了解项目结构和组件系统
2. 根据需求修改或新增组件
3. 调试并测试前端功能
4. 与后端API进行对接

## 常用前端功能
- 商品展示与搜索
- 会员中心
- 购物车
- 订单管理
- 支付功能
- 营销活动

