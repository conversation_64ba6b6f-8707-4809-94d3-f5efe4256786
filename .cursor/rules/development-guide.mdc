---
description: NiuShop开发指南
globs: 
alwaysApply: false
---
# NiuShop开发指南

## 框架特性
- 基于ThinkPHP 6框架
- 采用事件驱动设计
- 插件钩子机制，功能模块独立
- 标准API接口，前后端分离
- 支持Redis缓存和消息队列

## 开发规范
- 遵循PSR-4自动加载规范
- 控制器采用单一职责原则
- 模型层处理数据逻辑
- 使用事件机制解耦业务逻辑
- API接口遵循RESTful设计风格

## 目录命名规范
- 模块目录：小写字母
- 类文件：大驼峰命名（PascalCase）
- 方法名：小驼峰命名（camelCase）
- 变量名：小驼峰命名（camelCase）

## 常用开发流程
1. 创建数据模型（app/model目录）
2. 编写业务逻辑（app/shop或app/api目录）
3. 注册相关事件（app/event.php）
4. 前端调用API接口

## 插件开发
- 插件位于addon目录
- 遵循插件开发规范
- 使用钩子机制与核心交互

