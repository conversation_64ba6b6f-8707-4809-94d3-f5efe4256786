---
description: NiuShop数据库结构
globs: 
alwaysApply: false
---
# NiuShop数据库结构

## 数据库配置
- 配置文件：[config/database.php](mdc:config/database.php)
- 支持主从分离
- 支持读写分离
- 支持多数据库连接

## 主要数据表
- 用户相关：member, member_level, member_address
- 商品相关：goods, goods_category, goods_sku, goods_attribute
- 订单相关：order, order_goods, order_payment
- 营销相关：promotion, coupon, discount, seckill
- 系统相关：config, admin, auth_group, auth_rule

## 数据模型
- 模型文件位于[app/model](mdc:app/model)目录
- 遵循ThinkPHP的ORM规范
- 采用模型关联处理复杂数据关系

## 数据库设计原则
- 字段命名规范：下划线命名法
- 主键统一使用id字段
- 包含create_time和update_time时间戳字段
- 使用软删除机制（is_delete字段）
- 关联表使用外键约束

## 缓存设计
- 支持Redis缓存
- 高频数据缓存策略
- 队列处理高并发场景

