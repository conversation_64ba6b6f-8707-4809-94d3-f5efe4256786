---
description: NiuShop商城项目结构
globs: 
alwaysApply: false
---
# NiuShop商城项目结构

## 项目概述
NiuShop是一个基于ThinkPHP 6框架开发的开源电商系统，支持多端（H5、小程序、PC等），采用前后端分离架构。

## 主要目录结构
- [app](mdc:app) - 应用目录，包含核心业务逻辑
  - [app/shop](mdc:app/shop) - 商城后台管理模块
  - [app/api](mdc:app/api) - API接口模块
  - [app/model](mdc:app/model) - 数据模型
  - [app/event](mdc:app/event) - 事件处理
- [config](mdc:config) - 配置文件目录
- [public](mdc:public) - 公共资源目录
- [h5](mdc:h5) - H5移动端
- [web](mdc:web) - PC端
- [cashregister](mdc:cashregister) - 收银台
- [vendor](mdc:vendor) - 第三方依赖

## 入口文件
- [index.php](mdc:index.php) - 主入口文件
- [install.php](mdc:install.php) - 安装入口文件

## 技术栈
- 后端：ThinkPHP 6 + LayUI
- 前端：UNI-APP + ElementUI
- 数据库：MySQL
- 缓存：Redis

