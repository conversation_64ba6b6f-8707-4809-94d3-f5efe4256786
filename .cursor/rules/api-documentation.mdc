---
description: NiuShop API接口文档
globs: 
alwaysApply: false
---
# NiuShop API接口文档

## API架构
- RESTful风格API设计
- 前后端分离架构
- 标准JSON数据交互格式
- 支持跨域请求

## 主要API模块
- [app/api](mdc:app/api) - 通用API接口
- [app/shopapi](mdc:app/shopapi) - 商城后台API
- [app/storeapi](mdc:app/storeapi) - 门店API

## API认证
- 基于Token的身份验证
- 权限控制配置在config/auth_shopapi.php
- API请求需要在Header中携带Token

## 数据返回格式
```json
{
  "code": 200,  // 状态码：200成功，其他失败
  "message": "操作成功", // 提示信息
  "data": {}, // 返回数据
  "timestamp": 1623123456 // 时间戳
}
```

## 常用API接口
- 用户认证：登录、注册、找回密码
- 商品接口：列表、详情、搜索、分类
- 订单接口：创建、支付、查询、取消
- 会员接口：信息、积分、优惠券
- 营销接口：活动、优惠券、秒杀

