<?php
/**
 * NiuShop商品快速导入示例
 * 直接使用FastImport控制器添加商品
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 优化PHP配置以处理大JSON文件
ini_set('memory_limit', '256M');        // 增加内存限制
ini_set('max_execution_time', 0);       // 取消执行时间限制

// 加载框架入口文件
require __DIR__ . '/index.php';

// 使用API模式（如需要）
$useApi = false;

// 是否开启性能模式（批量处理更快）
$performanceMode = true;

// API接口URL
$apiUrl = 'http://store.haochigui.com/api/fastimport/add';

// 检查是否存在本地商品列表文件
$goodsListFile = __DIR__ . '/goodsList.json';
$goodsListFile = __DIR__ .'/山姆独立商品数据导出_2025-07-12T094910.json';
$useLocalFile = file_exists($goodsListFile);

// 示例数据
$sampleData = <<<'JSON'
{"spuId":"323992044","hostItem":"980322988","storeId":"6698","title":"Unif 100%NFC\u84dd\u8393\u6c41 200\u6beb\u5347*12","masterBizType":1,"viceBizType":1,"categoryIdList":["10003036","10003047","10011834","10012082"],"images":["https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8619169404080242689.jpg?imageMogr2\/thumbnail\/!80p","https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8619169424208703488.jpg?imageMogr2\/thumbnail\/!80p","https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8619169443666075649.jpg?imageMogr2\/thumbnail\/!80p","https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8619169462653702144.jpg?imageMogr2\/thumbnail\/!80p","https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8619169488574504960.jpg?imageMogr2\/thumbnail\/!80p","https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8619169510313586689.jpg?imageMogr2\/thumbnail\/!80p"],"imageSizeThreeFour":[],"videos":[],"descVideo":[],"isAvailable":true,"isStoreAvailable":true,"isPutOnSale":true,"sevenDaysReturn":true,"intro":"Unif 100%NFC\u84dd\u8393\u6c41 200\u6beb\u5347*12","subTitle":"100%NFC\u84dd\u8393\u6c41\uff1b\u6bcf\u5305\u7ea6\u7531\u4e0d\u5c11\u4e8e180\u9897\u84dd\u8393\u69a8\u6c41\u800c\u6210","brandId":"10271832","weight":3,"desc":"<p><img src=\"https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8618797934057500672.jpg?imageMogr2\/thumbnail\/!80p\" a=\".png\">\n<img src=\"https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8618797957155545089.jpg?imageMogr2\/thumbnail\/!80p\" a=\".png\">\n<img src=\"https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8619169570376019969.jpg?imageMogr2\/thumbnail\/!80p\" a=\".png\">\n<img src=\"https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8618133705440780289.jpg?imageMogr2\/thumbnail\/!80p\" a=\".png\">\n<img src=\"https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8618133730329772033.jpg?imageMogr2\/thumbnail\/!80p\" a=\".png\">\n<img src=\"https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8618133755856318465.jpg?imageMogr2\/thumbnail\/!80p\" a=\".png\">\n<img src=\"https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/37\/bktsitem-ops-prod-8556881536637296640.jpg?imageMogr2\/thumbnail\/!80p\">\n<img src=\"https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/37\/bktsitem-ops-prod-8587247357834108929.jpg?imageMogr2\/thumbnail\/!80p\"><\/p>","priceInfo":[{"priceType":1,"price":"10590","priceTypeName":"\u9500\u552e\u4ef7"},{"priceType":2,"price":"0","priceTypeName":"\u539f\u59cb\u4ef7"}],"stockInfo":{"stockQuantity":2,"safeStockQuantity":0,"soldQuantity":0},"limitInfo":[],"tagInfo":[{"id":"306016","title":"\u53ef\u5168\u56fd\u914d\u9001","tagPlace":7,"tagMark":"NATIONAL"}],"newTagInfo":[],"deliveryAttr":4,"favorite":false,"giveaway":false,"spuExtDTO":{"subTitle":"100%NFC\u84dd\u8393\u6c41\uff1b\u6bcf\u5305\u7ea6\u7531\u4e0d\u5c11\u4e8e180\u9897\u84dd\u8393\u69a8\u6c41\u800c\u6210","subETitle":"","intro":"Unif 100%NFC\u84dd\u8393\u6c41 200\u6beb\u5347*12","hostUpc":["6925303703196"],"departmentId":"52","valuable":true,"detailVideos":[],"temperature":1,"weight":3,"isImport":false,"deliveryAttr":4,"sevenDaysReturn":true,"giveaway":false,"isAccessory":false,"isRoutine":true,"thumbnailImage":"https:\/\/sam-material-online-1302115363.file.myqcloud.com\/\/sams-static\/goods\/464381\/bktsitem-ops-prod-8619169404080242689.jpg","netWeight":3,"smallPackageNum":12,"smallPackageUnit":"mL","status":4},"beltInfo":[],"valuable":true,"detailVideos":[],"temperature":1,"isImport":false,"isSerial":false,"spuSpecInfo":[],"specList":[],"specInfo":[],"attrGroupInfo":[],"attrInfo":[{"attrId":"155662","title":"\u98df\u54c1\u4fdd\u8d28\u671f","attrValueList":[[],{"value":"12\u4e2a\u6708"}],"isImportant":false},{"attrId":"155663","title":"\u51c0\u91cd\/\u51c0\u542b\u91cf","attrValueList":[[],{"value":"200\u6beb\u5347*12"}],"isImportant":false},{"attrId":"155664","title":"\u8fdb\u53e3\/\u56fd\u4ea7","attrValueList":[{"attrValueId":"1136914","value":"\u56fd\u4ea7"}],"isImportant":false}],"extendedWarrantyList":[],"couponContentList":[],"couponList":[],"promotionList":[],"promotionDetailList":[],"deliveryCapacityCountList":[],"complianceInfo":{"id":"261038638727561494","value":"\u5c71\u59c6\u54c1\u8d28\u3001\u9988\u8d60\u7cbe\u9009\uff0c\u5982\u60a8\u6709\u5927\u5b97\u91c7\u4e70\u9700\u6c42\uff0c\u6211\u4eec\u5c06\u4e3a\u60a8\u63d0\u4f9b\u5168\u7a0b\u4e13\u4e1a\u7684\u91c7\u4e70\u54a8\u8be2\u670d\u52a1\u3002\n\u8054\u7cfb\u6211\u4eec\uff1a\u5c71\u59c6app - \u6211\u7684 - \u6211\u7684\u670d\u52a1 - \u798f\u5229\u91c7\u8d2d\uff0c\u5728\u7ebf\u63d0\u4ea4\u91c7\u4e70\u9700\u6c42\uff0c\u8d44\u6df1\u91c7\u4e70\u987e\u95ee\u4e3a\u60a8\u63d0\u4f9b\u4e00\u5bf9\u4e00\u4e13\u5c5e\u670d\u52a1\uff0c\u8ba9\u798f\u5229\u91c7\u8d2d\u66f4\u7701\u5fc3\u3002"},"preSellList":[],"onlyStoreSale":false,"serviceInfo":[],"netWeight":3,"arrivalEndTimeDesc":"\u6709\u8d27\uff0c\u53ef\u5f53\u65e5\u6216\u6b21\u65e5\u53d1\u8d27\uff0c\u4f9d\u7167\u60a8\u5728\u7ed3\u7b97\u9875\u9762\u9009\u62e9\u7684\u914d\u9001\u65f6\u95f4\u7a97\u800c\u5b9a\u3002","isStoreExtent":false,"isGlobalDirectPurchase":false,"isGlobalOwnPickUp":false,"isAllowDelivery":true,"zoneTypeList":[],"isCrabCard":false,"isShowXPlusTag":false,"isCompare":false,"isGovSpu":false,"standardForIntactGoodsUrl":"https:\/\/m-sams.walmartmobile.cn\/common\/help-center\/217","customTabList":[],"isTicket":false,"categoryId":"156048,156053,156059","category_json":"[156048,156053,156059]","price":10590,"stock":0}
JSON;

// 处理数据
if ($useLocalFile) {
    // 记录开始时间
    $startTime = microtime(true);

    // 优化PHP配置以处理大JSON文件
    ini_set('memory_limit', '1024M');       // 增加内存限制到1GB
    ini_set('max_execution_time', 0);       // 取消执行时间限制

    // 强制输出缓冲
    ob_implicit_flush(true);
    if (ob_get_level()) {
        ob_end_flush();
    }

    // 读取本地商品列表文件
    echo "检测到本地商品列表文件: {$goodsListFile}\n";
    echo "当前内存限制: " . ini_get('memory_limit') . "\n";

    // 检查文件大小
    $fileSize = filesize($goodsListFile);
    echo "文件大小: " . number_format($fileSize / 1024 / 1024, 2) . " MB\n";

    // 检查可用内存
    echo "当前内存使用: " . number_format(memory_get_usage() / 1024 / 1024, 2) . " MB\n";
    echo "峰值内存使用: " . number_format(memory_get_peak_usage() / 1024 / 1024, 2) . " MB\n";

    $goodsListContent = file_get_contents($goodsListFile);
    echo "文件读取完成，开始JSON解析...\n";
    echo "读取后内存使用: " . number_format(memory_get_usage() / 1024 / 1024, 2) . " MB\n";
    flush(); // 强制输出

    // 添加错误处理和进度提示
    echo "正在解析JSON，请稍候...\n";
    flush(); // 强制输出

    $goodsList = json_decode($goodsListContent, true);

    echo "JSON解析完成，检查结果...\n";
    flush(); // 强制输出

    // 检查JSON解析是否成功
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "JSON解析失败，错误代码: " . json_last_error() . "\n";
        echo "错误信息: " . json_last_error_msg() . "\n";
        die("JSON解析错误\n");
    }

    echo "JSON解析成功！\n";
    echo "解析后内存使用: " . number_format(memory_get_usage() / 1024 / 1024, 2) . " MB\n";

    if (!$goodsList || !is_array($goodsList)) {
        die("JSON解析错误: " . json_last_error_msg() . "\n");
    }
    $goodsList = $goodsList['goods_data'];
    $totalGoods = count($goodsList);
    echo "成功读取商品列表，共 {$totalGoods} 个商品\n\n";

    
    // 创建FastImport控制器实例
    $controller = new \app\api\controller\FastImport();
    
    // 逐个添加商品
    $successCount = 0;
    $failCount = 0;
    $errors = [];
    
    // 显示进度条设置
    $progressBarWidth = 50; // 进度条宽度
    $lastPercent = 0;
    
    // 批量处理
    foreach ($goodsList as $index => $goodsData) {
        // 计算进度
        $percent = floor(($index + 1) / $totalGoods * 100);
        $progressBarFill = floor($percent * $progressBarWidth / 100);
        
        // 每10%更新一次进度条（性能模式）
        if ($performanceMode && $percent % 10 === 0 && $percent != $lastPercent) {
            $lastPercent = $percent;
            $progressBar = '[' . str_repeat('#', $progressBarFill) . str_repeat(' ', $progressBarWidth - $progressBarFill) . '] ' . $percent . '%';
            echo "\r处理进度: " . $progressBar . " ({$index} / {$totalGoods})";
            flush();
        } 
        // 每次更新进度条（普通模式）
        else if (!$performanceMode) {
            $progressBar = '[' . str_repeat('#', $progressBarFill) . str_repeat(' ', $progressBarWidth - $progressBarFill) . '] ' . $percent . '%';
            echo "\r处理进度: " . $progressBar . " ({$index} / {$totalGoods})";
            flush();
        }
        
        try {
            if ($useApi) {
                // 使用API方式添加
                $jsonData = json_encode($goodsData, JSON_UNESCAPED_UNICODE);
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $apiUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($jsonData)
                ]);
                
                $response = curl_exec($ch);
                
                if (curl_errno($ch)) {
                    $errorMsg = "CURL错误 - " . curl_error($ch);
                    $errors[] = "商品 " . ($index + 1) . ": " . $errorMsg;
                    $failCount++;
                } else {
                    $result = json_decode($response, true);
                    if (isset($result['code']) && $result['code'] === 0) {
                        $successCount++;
                    } else {
                        $errorMsg = $result['message'] ?? '未知错误';
                        $errors[] = "商品 " . ($index + 1) . ": " . $errorMsg;
                        $failCount++;
                    }
                }
                
                curl_close($ch);
            } else {
                // 直接使用控制器添加，无需输出中间结果
                $result = $controller->add($goodsData);
                
                if (is_object($result) && method_exists($result, 'getContent')) {
                    $content = $result->getContent();
                    $resultData = json_decode($content, true);
                    
                    if (isset($resultData['code']) && $resultData['code'] === 0) {
                        $successCount++;
                    } else {
                        $errorMsg = $resultData['message'] ?? '未知错误';
                        $errors[] = "商品 " . ($index + 1) . " (" . ($goodsData['title'] ?? '未知') . "): " . $errorMsg;
                        $failCount++;
                    }
                } else {
                    if (isset($result['code']) && $result['code'] === 0) {
                        $successCount++;
                    } else {
                        $errorMsg = $result['message'] ?? '未知错误';
                        $errors[] = "商品 " . ($index + 1) . " (" . ($goodsData['title'] ?? '未知') . "): " . $errorMsg;
                        $failCount++;
                    }
                }
            }
        } catch (\Exception $e) {
            $errorMsg = $e->getMessage();
            $errors[] = "商品 " . ($index + 1) . " (" . ($goodsData['title'] ?? '未知') . "): " . $errorMsg;
            $failCount++;
        }
    }
    
    // 完成进度条
    $progressBar = '[' . str_repeat('#', $progressBarWidth) . '] 100%';
    echo "\r处理进度: " . $progressBar . " ({$totalGoods} / {$totalGoods})\n\n";
    
    // 计算耗时
    $endTime = microtime(true);
    $timeElapsed = $endTime - $startTime;
    
    echo "批量导入完成!\n";
    echo "总计: {$totalGoods} 个商品, 成功: {$successCount}, 失败: {$failCount}\n";
    echo "总耗时: " . number_format($timeElapsed, 2) . " 秒, 平均: " . ($totalGoods > 0 ? number_format($timeElapsed / $totalGoods, 4) : 0) . " 秒/个\n";
    
    // 显示错误信息
    if (count($errors) > 0) {
        echo "\n失败详情 (最多显示前10条):\n";
        $displayErrors = array_slice($errors, 0, 10);
        foreach ($displayErrors as $error) {
            echo "- {$error}\n";
        }
        
        if (count($errors) > 10) {
            echo "... 还有 " . (count($errors) - 10) . " 条错误未显示\n";
        }
    }
} else {
    // 解析JSON数据
    $data = json_decode($sampleData, true);
    if (!$data) {
        die("JSON解析错误: " . json_last_error_msg() . "\n");
    }

    // 根据配置选择添加方式
    if ($useApi) {
        // 方式1：通过API添加商品
        echo "使用API方式添加商品\n";
        echo "API接口: {$apiUrl}\n\n";
        
        // 调用API
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $sampleData);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($sampleData)
        ]);
        
        $response = curl_exec($ch);
        $error = '';
        
        if (curl_errno($ch)) {
            $error = 'CURL错误: ' . curl_error($ch);
            $result = ['code' => -1, 'message' => $error];
        } else {
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $result = json_decode($response, true);
            if (!$result) {
                $error = '返回结果解析失败';
                $result = ['code' => -1, 'message' => $error];
            }
        }
        
        curl_close($ch);
        
        // 输出结果
        echo "API调用结果:\n";
        echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    } else {
        // 方式2：直接使用FastImport控制器添加商品
        echo "使用FastImport控制器添加商品\n\n";
        
        try {
            // 直接实例化FastImport控制器
            $controller = new \app\api\controller\FastImport();
            
            // 直接传参调用控制器add方法
            $result = $controller->add($data);
            
            // 输出结果
            if (is_object($result) && method_exists($result, 'getContent')) {
                // 处理Response对象
                $content = $result->getContent();
                $resultData = json_decode($content, true);
                
                echo "添加商品结果:\n";
                echo json_encode($resultData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
                
                if (isset($resultData['code']) && $resultData['code'] === 0) {
                    echo "\n商品添加成功！商品ID: " . $resultData['data'] . "\n";
                } else {
                    echo "\n商品添加失败: " . ($resultData['message'] ?? '未知错误') . "\n";
                }
            } else {
                // 直接处理数组结果
                echo "添加商品结果:\n";
                echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
                
                if (isset($result['code']) && $result['code'] === 0) {
                    echo "\n商品添加成功！商品ID: " . $result['data'] . "\n";
                } else {
                    echo "\n商品添加失败: " . ($result['message'] ?? '未知错误') . "\n";
                }
            }
        } catch (\Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
            echo "错误文件: " . $e->getFile() . " 第 " . $e->getLine() . " 行\n";
        }
    }
}

// 使用说明
echo "\n使用方法：\n";
echo "1. 修改脚本开头的配置变量：\n";
echo "   - \$useApi = true/false：是否使用API方式\n";
echo "   - \$performanceMode = true/false：是否开启性能模式（更快导入）\n";
echo "   - \$sampleData = '{...}'：指定JSON字符串\n";
echo "2. 创建goodsList.json文件，包含商品列表数组\n";
echo "3. 运行脚本：php goods_import_example.php\n"; 